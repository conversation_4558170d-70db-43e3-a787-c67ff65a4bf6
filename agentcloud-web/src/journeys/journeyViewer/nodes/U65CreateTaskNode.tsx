import React, { useCallback } from "react";
import NodeProps from "./NodeProps";
import { LoggedIn } from "../../../queries/GetLoggedIn";
import HistoryNode from "../models/HistoryNode";
import { LoadingView } from "../../../util/LoadingView";
import extractAnswerFromNode from "../../../util/extractAnswerFromNode";
import { useContactTcpa } from "../../../queries/GetContactTcpa";
import moment from "moment";
import TaskStatuses from "../../../legacy/js/models/TaskStatuses";
import InternalServerError from "../errors/InternalServerError";
import { useAnswerSubmission } from "../SubmitJourneyAnswer";

interface ExtraInstructions {
    contactId: {
        fieldId: number;
        objectId: number;
    },
    createdTask: {
        fieldId: number;
        objectId: number;
    }
}

export interface U65CreateTaskNodeProps extends NodeProps {
    advance: () => Promise<void>;
    loggedIn: LoggedIn;
    updateProgressBarNodes: (historyNodes: HistoryNode[]) => void,
}

/** Invisible node for creating Contact Callback(Quoted) task on U65 journey. */
export default function U65CreateTaskNode({
    advance,
    loggedIn,
    node,
    sessionId,
    updateProgressBarNodes,
}: U65CreateTaskNodeProps) {

    // If something goes wrong while creating the task, set this to true to alert the user.
    const [taskError, setTaskError] = React.useState<boolean>(false);
    const [taskCreated, setTaskCreated] = React.useState<boolean>(false);

    const extraInstructions = React.useMemo(
        () => JSON.parse(node.extraInstructions) as ExtraInstructions,
        [node.extraInstructions]
    );

    const retrievedContactId = React.useMemo(
        () => {
            return extractAnswerFromNode<number>(
                { fieldId: extraInstructions.contactId.fieldId, objectId: extraInstructions.contactId.objectId },
                node
            );
        },
        [extraInstructions, node]
    );

    const retrievedTask = React.useMemo(
        () => {
            return extractAnswerFromNode<number>(
                { fieldId: extraInstructions.createdTask.fieldId, objectId: extraInstructions.createdTask.objectId },
                node
            );
        },
        [extraInstructions, node]
    );

    const { data: contactData } = useContactTcpa(retrievedContactId);
    const [submitAnswers] = useAnswerSubmission();

    // When changing to Product selection page creates the Contact CallBack (Quoted) task
    const createContactCallbackTask = useCallback(async (contact: any) => {
        const requestOptions = {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                created: moment().tz("America/Chicago").toISOString(),
                updated: moment().tz("America/Chicago").toISOString(),
                priority: {
                    id: 2000,
                    displayName: "Medium",
                },
                action: {
                    id: 72,
                    displayName: "Contact Call Back (Quoted) High Priority",
                },
                parentId: contact.id,
                due: moment().tz("America/Chicago").add(14, "days").toISOString(),
                taskCategory: {
                    displayName: "Opportunity",
                },
                contact: {
                    id: contact.id,
                    agentId: contact.agentId,
                    created: contact.created,
                    emails: contact.emails,
                    phones: contact.phones,
                    status: contact.status
                },
                automated: true,
                orgId: 0, // This is a dummy value because the back-end will set it.
                status: TaskStatuses.ToDo.toJSON(),
                name: "Contact Call Back (Quoted)",
                assignee: loggedIn,
            }),
        };

        try {
            const response = await fetch(
                `/contacts/${contact.id}/tasks`,
                requestOptions
            );

            if (!response.ok) {
                console.error("Bad response", response);
                setTaskError(true);
            } else {
                const nodeAnswer = [];
                nodeAnswer.push({
                    sessionId: sessionId,
                    fieldId: extraInstructions.createdTask.fieldId,
                    objectId: extraInstructions.createdTask.objectId,
                    answer: JSON.stringify("Contact Call Back (Quoted) High Priority")
                });
                const response = await submitAnswers({ variables: { answers: nodeAnswer } });
                if (response.data) {
                    setTaskCreated(true);
                    updateProgressBarNodes(response.data.upsertJourneyAnswers.historyNodes);
                }
            }

        } catch (e) {
            console.error("Error creating callback task", e);
            setTaskError(true);
        }
    }, [loggedIn,extraInstructions,sessionId,submitAnswers,updateProgressBarNodes]);

    React.useEffect(() => {
        if (contactData) {
            // Checks if the task was already created
            if (!retrievedTask) {
                createContactCallbackTask(contactData.contact);
            } else {
                advance();
            }
        }
    }, [contactData]);

    React.useEffect(() => {
        if (taskCreated) {
            advance();
        }
    }, [taskCreated]);

    if (taskError) {
        console.error("Error while creating new task");
        return <InternalServerError inContainer={true}/>;
    }

    return <LoadingView message="Creating related tasks..." />;

}
