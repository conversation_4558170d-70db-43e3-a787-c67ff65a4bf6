// scalastyle:off file.size.limit cyclomatic.complexity
package com.elagy.models

import anorm.SqlParser._
import anorm._
import cats.data._
import cats.implicits._
import com.elagy.util.AnormImplicits._
import com.elagy.models.graphql._
import com.elagy.tcpa.Flag
import com.jaroop.anorm.relational._
import com.jaroop.blocks.authentication.models.Account
import com.jaroop.core.auth.basic.ApiUser
import com.jaroop.core.datatypes.{ <PERSON>ail<PERSON><PERSON><PERSON>, IpAddress, PhoneNumber, State }
import com.jaroop.core.errors._
import com.jaroop.core.libs.GraphQL._
import com.jaroop.core.libs.JsImplicits._
import com.jaroop.core.libs.filters.dsl._
import com.jaroop.core.libs.filters.graphql._
import com.jaroop.core.libs.sql.DatabaseOps.Transaction
import com.jaroop.core.libs.sql._
import com.jaroop.core.libs.sql.errors.{ <PERSON><PERSON><PERSON><PERSON>, ForeignKeyMissing }
import com.jaroop.core.models.Model
import com.jaroop.core.models.api._
import com.jaroop.core.models.patch._
import java.sql.Connection
import java.time._
import javax.inject.{ Inject, Singleton }
import play.api.Logger
import play.api.db.{ Database, NamedDatabase }
import play.api.libs.json._
import sangria.marshalling.playJson._
import sangria.macros.derive.{ DocumentField, _ }
import sangria.schema._
import scala.util.Try

/** A lead is a person who may be sold a policy. When an agent is able to make contact with a lead, the lead is converted
 *  into an opportunity. Leads have much of the same contact data as an opportunity but are less extensive and often
 *  have incomplete information.
 *
 *  @param id The id of the lead in the database.
 *  @param status The current status of the lead such as active, converted, etc.
 *  @param contact Contact information for the lead.
 *  @param product The kind of product a lead might be interested in buying.
 *  @param orgId The id of the organization that the lead was created for.
 *  @param agentId The ID of the agent assigned to this lead. Agents can only access leads assigned to them.
 *  @param campaignId The ID of the marketing campaign where the lead originated.
 *  @param notes Free-form information that users should know about a lead.
 *  @param clientRef A field to be used by a client for referencing the information that the lead came from.
 *  @param clientIp The ip address where the lead came from.
 *  @param referringUrl The url where the lead came from.
 *  @param tcpaToken A tcpa token that is associated with the lead.
 *  @param tcpaFlag A flag returned from the TCPA api associated with this lead.
 *  @param tcpaRejectionReason The reason why the TCPA was rejected for this lead.
 *  @param leadDetails The details used to quote a policy for a lead.
 *  @param mortgageDetails The details about a mortgage that the the lead is associated with.
 *  @param dialerListId The dialer list id that the lead is assigned to.
 *  @param apiUserId The API user that submitted the lead.
 *  @param s3UserId The s3 user that submitted the lead.
 *  @param sessionId The sessionId passed in from the Lead API
 *  @param trustedFormCert This is for HC users to send us leads
 */
case class Lead(
    id: Option[Long],
    status: LeadStatus,
    contact: LeadContact,
    product: Option[ProductType],
    leadDetails: LeadDetails,
    orgId: Long,
    agentId: Option[Long] = None,
    campaignId: Option[Long] = None,
    notes: Option[String] = None,
    clientRef: Option[String] = None,
    clientIp: Option[IpAddress] = None,
    referringUrl: Option[String] = None,
    tcpaToken: Option[String] = None,
    tcpaFlag: Option[Flag] = None,
    tcpaRejectionReason: Option[String] = None,
    mortgageDetails: Option[MortgageDetails] = None,
    apiUserId: Option[Long] = None,
    dialerListId: Option[Long] = None,
    s3UserId: Option[Long] = None,
    sessionId: Option[String] = None,
    trustedFormCert: Option[String] = None
) extends Model[Lead] {
    /** Sets the id of the lead along with the policy details. */
    def withId(id: Long) = this.copy(
        id = Some(id),
        leadDetails = this.leadDetails.copy(leadId = Some(id))
    )
    /**
     * The default phone number that should be used when contacting this lead. This is the primary phone or (if a primary
     * phone is not defined) the first phone number in the list of phone entries.
     */
    lazy val defaultPhone: Option[PhoneEntry] = this.contact.primaryPhone.orElse(this.contact.phones.headOption)
}

object Lead {
    import play.api.libs.functional.syntax._ // scalastyle:ignore

    /** The key that's used to acquire a global lock on a lead via the LockManager. Use this when you have to read
     *  a lead from the database and then update it and can't do it all in a single atomic step.
     */
    def lockString(id: Long): String = s"lead:$id"

    def empty = Lead(
        id = None,
        status = LeadStatus.Active,
        contact = LeadContact.empty,
        product = Some(ProductType.FinalExpense),
        orgId = 0L,
        agentId = None,
        campaignId = None,
        notes = None,
        clientRef = None,
        clientIp = None,
        referringUrl = None,
        tcpaToken = None,
        tcpaFlag = None,
        tcpaRejectionReason = None,
        leadDetails = LeadDetails.empty,
        mortgageDetails = None,
        dialerListId = None,
        sessionId = None,
        trustedFormCert = None
    )

    implicit val reads: Reads[Lead] = (
        (__ \ "id").readNullable[Long] and
        (__ \ "status").read[LeadStatus] and
        (__ \ "contact").read[LeadContact] and
        (__ \ "product").readNullable[ProductType] and
        // The readWithDefault here is for the case when an existing history entry's lead data is read and they may not have
        // the policy details data which are required. So the empty LeadDetails fallback is there so the json reads succeed
        // for those specific history entries.
        (__ \ "leadDetails").readWithDefault[LeadDetails](LeadDetails.empty) and
        (__ \ "orgId").readWithDefault[Long](1L) and // Default to Org 1 in order to read the history entries.
        (__ \ "agentId").readNullable[Long] and
        (__ \ "campaignId").readNullable[Long] and
        (__ \ "notes").readNullable[String] and
        (__ \ "clientRef").readNullable[String] and
        (__ \ "clientIp").readNullable[IpAddress] and
        (__ \ "referringUrl").readNullable[String] and
        (__ \ "tcpaToken").readNullable[String] and
        (__ \ "tcpaFlag").readNullable[Flag] and
        (__ \ "tcpaRejectionReason").readNullable[String] and
        (__ \ "mortgageDetails").readNullable[MortgageDetails] and
        (__ \ "apiUserId").readNullable[Long] and
        (__ \ "dialerListId").readNullable[Long] and
        (__ \ "s3UserId").readNullable[Long] and
        (__ \ "sessionId").readNullable[String] and
        (__ \ "trustedFormCert").readNullable[String]
    )(Lead.apply _)
    implicit val writes: Writes[Lead] = Json.writes[Lead]

    /** Parses the lead with everything except the phones and the emails in the contact. */
    val parser: RowParser[Lead] = {
        get[Option[Long]]("leads.id") ~
        get[LeadStatus]("leads.status_id") ~
        LeadContact.parser ~
        get[Option[ProductType]]("leads.product_type_id") ~
        LeadDetails.parser ~
        get[Long]("leads.organization_id") ~
        get[Option[Long]]("leads.agent_id") ~
        get[Option[Long]]("leads.campaign_id") ~
        get[Option[String]]("leads.notes") ~
        get[Option[String]]("leads.client_ref") ~
        get[Option[IpAddress]]("leads.client_ip") ~
        get[Option[String]]("leads.referring_url") ~
        get[Option[String]]("leads.tcpa_token") ~
        get[Option[Flag]]("leads.tcpa_flag_id") ~
        get[Option[String]]("leads.tcpa_rejection_reason") ~
        MortgageDetails.parser.? ~
        get[Option[Long]]("leads.api_user_id") ~
        get[Option[Long]]("leads.dialer_list_id") ~
        get[Option[Long]]("leads.s3_user_id") ~
        get[Option[String]]("leads.session_id") ~
        get[Option[String]]("leads.trusted_form_cert") map to(Lead.apply _)
    }

    implicit val rf: RowFlattener2[Lead, PhoneEntry, EmailEntry] = RowFlattener[Lead, PhoneEntry, EmailEntry] {
        (lead, phones, emails) => lead.copy(contact = lead.contact.copy(phones = phones, emails = emails))
    }
    val relationalParser = RelationalParser(parser, PhoneEntry.parser, EmailEntry.parser)

    final val table = "eap.leads"
    implicit val patcher: Patcher[Lead] = Patcher {
        case PatchRequest(id, "status", json) => Patcher.simpleUpdate[LeadStatus](id, json, table, "status_id")
        case PatchRequest(id, "contact:firstName", json) => Patcher.simpleUpdateOpt[String](id, json, table, "first_name")
        case PatchRequest(id, "contact:lastName", json) => Patcher.simpleUpdateOpt[String](id, json, table, "last_name")
        case PatchRequest(id, "contact:birthday", json) => Patcher.simpleUpdateOpt[LocalDate](id, json, table, "birthday")
        case PatchRequest(id, "contact:street", json) => Patcher.simpleUpdateOpt[String](id, json, table, "street")
        case PatchRequest(id, "contact:street2", json) => Patcher.simpleUpdateOpt[String](id, json, table, "street2")
        case PatchRequest(id, "contact:city", json) => Patcher.simpleUpdateOpt[String](id, json, table, "city")
        case PatchRequest(id, "contact:state", json) => Patcher.simpleUpdateOpt[State](id, json, table, "state_id")
        case PatchRequest(id, "contact:zip", json) => Patcher.simpleUpdateOpt[String](id, json, table, "zip")
        case PatchRequest(id, "notes", json) => Patcher.simpleUpdateOpt[String](id, json, table, "notes")
        case PatchRequest(id, "primaryPhoneId", json) => Patcher.simpleUpdateOpt[Long](id, json, table, "primary_phone_id")
        case PatchRequest(id, "primaryEmailId", json) => Patcher.simpleUpdateOpt[Long](id, json, table, "primary_email_id")
        case PatchRequest(id, "contact:gender", json) => Patcher.simpleUpdateOpt[Gender](id, json, table, "gender_id")
        case PatchRequest(id, "contact:middleName", json) => Patcher.simpleUpdateOpt[String](id, json, table, "middle_name")
        case PatchRequest(id, "campaign", json) => Patcher.simpleUpdateOpt[Long](id, json, table, "campaign_id")
    }

    implicit val taskRelation: TaskRelation[Lead] = new TaskRelation[Lead] {

        /** The task type associated with the parent type. */
        type T = LeadTask

        /** Exposes the TaskInfo instance for the related task type. */
        lazy val info: TaskInfo[LeadTask] = implicitly

        /** Returns the ID of the parent model. */
        def parentId(parent: Lead): Option[Long] = parent.id
    }

    /** Helps the relationship service create, list, and delete relations for this lead. */
    implicit def leadRelations: Relation[Lead] = new Relation[Lead]{
        def table: String = "eap.lead_relationships"
        def id(a: Lead): Long = a.id.get
    }
}

/** A helper object that synchronizes lead-phone assignments. */
@Singleton
class LeadPhoneSynchronizer @Inject() (@NamedDatabase("default") val db: Database) extends Synchronizer[PhoneEntry] {
    val dbErrorMap: PartialFunction[DatabaseError, String] = PartialFunction.empty
    val mappingTable: String = "eap.lead_phone_entries"
    val objectColumn: String = "phone_entry_id"
    val parentColumn: String = "lead_id"
}

/** A helper object that synchronizes lead-email assignments. */
@Singleton
class LeadEmailSynchronizer @Inject() (@NamedDatabase("default") val db: Database) extends Synchronizer[EmailEntry] {
    val dbErrorMap: PartialFunction[DatabaseError, String] = PartialFunction.empty
    val mappingTable: String = "eap.lead_email_entries"
    val objectColumn: String = "email_entry_id"
    val parentColumn: String = "lead_id"
}

@Singleton
class LeadService @Inject()(
    campaignService: CampaignService,
    cohortService: CohortService,
    contactService: ContactService,
    clientConfig: ConfigProvider,
    phoneEntryService: PhoneEntryService,
    emailEntryService: EmailEntryService,
    leadDetailsService: LeadDetailsService,
    leadPhoneSynchronizer: LeadPhoneSynchronizer,
    leadEmailSynchronizer: LeadEmailSynchronizer,
    mortgageDetailsService: MortgageDetailsService,
    opportunityService: OpportunityService,
    opportunityDetailsService: OpportunityDetailsService,
    partnerService: PartnerService,
    relationshipService: RelationshipService,
    taskService: TaskService
) {

    private final val logger = Logger(this.getClass.getCanonicalName)

    private final val allowNewPRLeadColumns = clientConfig.config.allowNewPRLeadColumns

    /** Reads a lead from the database.
     *  @param id The id of the lead.
     *  @return The lead information from the database.
     */
    def read[F[_]](id: Long)(implicit c: Connection, monad: JMonad[F]): F[Lead] = monad.tryOption(s"Could not find lead $id") {
        SQL"""
            SELECT * FROM eap.leads l
            LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
            LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
            LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
            LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
            LEFT JOIN eap.states s ON (l.state_id = s.id)
            JOIN eap.lead_statuses ls ON (l.status_id = ls.id)
            LEFT JOIN eap.product_types pt ON (l.product_type_id = pt.id)
            LEFT JOIN eap.lead_details ld ON (ld.lead_id = l.id)
            LEFT JOIN eap.family_statuses fs ON (ld.family_status = fs.id)
            LEFT JOIN eap.risk_classes rc ON (ld.risk_class = rc.id)
            LEFT JOIN eap.mortgage_details md ON (md.id = l.mortgage_id)
            WHERE l.id = $id
            ORDER BY pe.id, ee.id
        """.asRelational(Lead.relationalParser.singleOpt)
    }

    /** Reads a list of leads from the database depending on the passed organization id. */
    def list[F[_]](
        ids: NonEmptyList[Long], orgId: Long
    )(implicit c: Connection, monad: JMonad[F]): F[List[Lead]] = monad.tryCatchNonFatal {
        SQL"""
            SELECT * FROM eap.leads l
            LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
            LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
            LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
            LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
            LEFT JOIN eap.states s ON (l.state_id = s.id)
            JOIN eap.lead_statuses ls ON (l.status_id = ls.id)
            LEFT JOIN eap.product_types pt ON (l.product_type_id = pt.id)
            LEFT JOIN eap.lead_details ld ON (ld.lead_id = l.id)
            LEFT JOIN eap.family_statuses fs ON (ld.family_status = fs.id)
            LEFT JOIN eap.risk_classes rc ON (ld.risk_class = rc.id)
            LEFT JOIN eap.mortgage_details md ON (md.id = l.mortgage_id)
            WHERE l.id IN (${ids.toList})
            AND l.organization_id = $orgId
            ORDER BY l.id, pe.id, ee.id
        """.asRelational(Lead.relationalParser.*)
    }

    /** Gets a list of leads from the database that are related to the given one.
     *  @param lead The Lead to find related leads for
     *  @return a list of related leads
     */
    def listRelated[F[_]](lead: Lead)(
        implicit c: Connection,
        relation: Relation[Lead],
        monad: JMonad[F]
    ): F[List[Lead]] = {
        relationshipService.listRelated[F, Lead](list(_, lead.orgId))(lead)
    }

    /**
     * Gets a list of all leads with the given status
     *
     * @param status The status of the leads to return
     * @return The list of leads
     */
    def listByStatus[F[_]](
        status: LeadStatus, orgId: Long
    )(implicit c: Connection, monad: JMonad[F]): F[List[Lead]] = monad.tryCatchNonFatal {
        SQL"""
            SELECT * FROM eap.leads l
            LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
            LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
            LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
            LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
            LEFT JOIN eap.states s ON (l.state_id = s.id)
            JOIN eap.lead_statuses ls ON (l.status_id = ls.id)
            LEFT JOIN eap.product_types pt ON (l.product_type_id = pt.id)
            LEFT JOIN eap.lead_details ld ON (ld.lead_id = l.id)
            LEFT JOIN eap.family_statuses fs ON (ld.family_status = fs.id)
            LEFT JOIN eap.risk_classes rc ON (ld.risk_class = rc.id)
            LEFT JOIN eap.mortgage_details md ON (md.id = l.mortgage_id)
            WHERE l.status_id = ${status.id}
            AND l.organization_id = $orgId
            ORDER BY l.id, pe.id, ee.id
        """.asRelational(Lead.relationalParser.*)
    }

    /** Reads a list of leads from the database. */
    def listAll[F[_]](
        ids: NonEmptyList[Long]
    )(implicit c: Connection, monad: JMonad[F]): F[List[Lead]] = monad.tryCatchNonFatal {
        SQL"""
            SELECT
                l.id,
                l.created,
                l.product_type_id,
                l.agent_id,
                l.status_id,
                l.first_name,
                l.last_name,
                l.birthday,
                l.street,
                l.street2,
                l.city,
                l.state_id,
                l.zip,
                l.notes,
                l.primary_phone_id,
                l.primary_email_id,
                l.legacy_campaign,
                l.middle_name,
                l.gender_id,
                l.client_ref,
                l.client_ip,
                l.referring_url,
                l.tcpa_token,
                l.tcpa_flag_id,
                l.tcpa_rejection_reason,
                l.mortgage_id,
                l.campaign_id,
                l.api_user_id,
                l.dialer_list_id,
                l.aq_migrated_id,
                l.s3_user_id,
                l.organization_id,
                l.session_id,
                l.updated,
                l.trusted_form_cert,
                lpe.lead_id,
                lpe.phone_entry_id,
                pe.id,
                pe.created,
                pe.updated,
                pe.phone_number,
                pe.do_not_contact,
                pe.tcpa_status_id,
                pe.phone_type_id,
                pe.tcpa_text_id,
                pe.organization_id,
                lee.lead_id,
                lee.email_entry_id,
                ee.id,
                ee.created,
                ee.updated,
                ee.email,
                ee.do_not_contact,
                ee.validation_result_id,
                ee.organization_id,
                s.id,
                s.display_name,
                s.abbreviation,
                ls.id,
                ls.display_name,
                pt.id,
                pt.display_name,
                ld.lead_id,
                ld.term_length,
                ld.tobacco,
                ld.opt_in_date_time,
                ld.face_amount,
                ld.height,
                ld.weight,
                ld.us_citizen,
                ld.income_monthly,
                ld.family_status,
                ld.risk_class,
                ld.borrower_id,
                fs.id,
                fs.display_name,
                rc.id,
                rc.display_name,
                md.loan_number,
                md.mortgage_close_date,
                md.insurance_purpose,
                md.loan_officer_first_name,
                md.loan_officer_last_name,
                md.loan_officer_phone,
                md.loan_officer_email,
                md.processor_first_name,
                md.processor_last_name,
                md.processor_phone,
                md.processor_email,
                md.id,
                md.face_amount,
                md.term_length,
                md.borrower_status_id,
                md.tpo_firm
            FROM eap.leads l
            LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
            LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
            LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
            LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
            LEFT JOIN eap.states s ON (l.state_id = s.id)
            JOIN eap.lead_statuses ls ON (l.status_id = ls.id)
            LEFT JOIN eap.product_types pt ON (l.product_type_id = pt.id)
            LEFT JOIN eap.lead_details ld ON (ld.lead_id = l.id)
            LEFT JOIN eap.family_statuses fs ON (ld.family_status = fs.id)
            LEFT JOIN eap.risk_classes rc ON (ld.risk_class = rc.id)
            LEFT JOIN eap.mortgage_details md ON (md.id = l.mortgage_id)
            WHERE l.id IN (${ids.toList})
            ORDER BY l.id, pe.id, ee.id
        """.asRelational(Lead.relationalParser.*)
    }

    /** Returns the number of AAA PR leads from a list of rapport ids that are in a status. */
    def countAAAPrLeadsForStatus[F[_]](
        rapportIds: NonEmptyList[String], status: LeadStatus
    )(implicit c: Connection, monad: JMonad[F]): F[Long] = monad.tryCatchNonFatal {
        rapportIds.grouped(32766) // Postgres and JDBC have a limit of 32767 parameters for a query.
            .foldLeft(0L)( (sum, group) =>
                sum + SQL"""
                    SELECT COUNT(l.id) FROM eap.opportunities o
                    JOIN eap.lead_cohorts lc ON (lc.opportunity_id = o.id)
                    JOIN eap.leads l ON (l.id = lc.lead_id AND l.status_id = ${status.id})
                    WHERE o.rapport_id IN (${group.toList})
                """.as(SqlParser.scalar[Long].single)
            )

    }

    /** Retrieves a map of lead ids from a list of client references. */
    def mapFromClientRef[F[_]](
        clientRefs: NonEmptyList[String]
    )(implicit c: Connection, monad: JMonad[F]): F[Map[String, Long]] = monad.tryCatchNonFatal {
        SQL"""
            SELECT id, client_ref FROM eap.leads
            WHERE client_ref IN (${clientRefs.toList})
            ORDER BY id DESC
        """.as(
            (SqlParser.get[String]("client_ref") ~ SqlParser.get[Long]("id"))
                .map { case clientRef ~ id => (clientRef, id) }
                .*
        ).toMap
    }

    /** Deletes relations between two leads.
     *  @param from the From-Lead
     *  @param to the To-Lead
     *  @return Nothing if everything succeeds.
     */
    def deleteRelation[F[_]](from: Lead, to: Lead)(
        implicit t: Transaction,
        relation: Relation[Lead],
        monad: JMonad[F]
    ): F[Unit] = {
        relationshipService.delete[F, Lead](from, to)
    }

    // scalastyle:off method.length
    /** Creates a lead entry in database.
     *  @param lead The lead information to be created.
     *  @return The newly-created lead information.
     */
    private def simpleCreate[F[_]](
        lead: Lead,
        created: OffsetDateTime = OffsetDateTime.now
    )(implicit t: Transaction, monad: JMonad[F]): F[Lead] =
        monad.tryOption("Could not create lead.") {
            SQL"""
                INSERT INTO eap.leads (
                    created,
                    product_type_id,
                    agent_id,
                    status_id,
                    first_name,
                    middle_name,
                    last_name,
                    birthday,
                    street,
                    street2,
                    city,
                    state_id,
                    zip,
                    campaign_id,
                    gender_id,
                    client_ref,
                    client_ip,
                    referring_url,
                    tcpa_token,
                    tcpa_flag_id,
                    tcpa_rejection_reason,
                    mortgage_id,
                    dialer_list_id,
                    api_user_id,
                    s3_user_id,
                    organization_id,
                    session_id,
                    trusted_form_cert
                ) VALUES (
                    $created,
                    ${lead.product.map(_.id)},
                    ${lead.agentId},
                    ${lead.status.id},
                    ${lead.contact.firstName},
                    ${lead.contact.middleName},
                    ${lead.contact.lastName},
                    ${lead.contact.birthday},
                    ${lead.contact.street},
                    ${lead.contact.street2},
                    ${lead.contact.city},
                    ${lead.contact.state.map(_.id)},
                    ${lead.contact.zip},
                    ${lead.campaignId},
                    ${lead.contact.gender.map(_.id)},
                    ${lead.clientRef},
                    ${lead.clientIp},
                    ${lead.referringUrl},
                    ${lead.tcpaToken},
                    ${lead.tcpaFlag.map(_.id)},
                    ${lead.tcpaRejectionReason},
                    ${lead.mortgageDetails.flatMap(_.id)},
                    ${lead.dialerListId},
                    ${lead.apiUserId},
                    ${lead.s3UserId},
                    ${lead.orgId},
                    ${lead.sessionId},
                    ${lead.trustedFormCert}
                )
            """.executeInsert().map { id =>
                lead.withId(id)
                    .copy(contact = lead.contact.copy(state = lead.contact.state.flatMap(state => State.fromId(state.id))))
            }
        }
    // scalastyle:on method.length

    /** Determine if a new lead already exists in the database.
     *  @param lead A new lead
     *  @return True when any of the lead's phone numbers or e-mail addresses are already present in the database.
     */
    def isDuplicate(lead: Lead)(implicit c: Connection): JResult[Boolean] = {
        lead.campaignId match {
            case None =>
                logger.warn(s"Lead with clientRef '${lead.clientRef.getOrElse("N/A")}' and contact '${lead.contact.firstName.getOrElse("")} ${lead.contact.lastName.getOrElse("")}' has no campaignId. Defaulting to strict deduplication.")
                defaultDuplicateCheck(lead)
            case Some(cId) =>
                campaignService.read[JResult](cId).flatMap { (campaignOpt: Option[Campaign]) => 
                    campaignOpt match {
                        case None =>
                            logger.warn(s"Campaign $cId not found for lead with clientRef '${lead.clientRef.getOrElse("N/A")}'. Defaulting to strict deduplication.")
                            defaultDuplicateCheck(lead)
                        case Some(campaign) => 
                            if (campaign.alwaysDedupe) {
                                defaultDuplicateCheck(lead)
                            } else {
                                campaign.dedupeDays match {
                                    case None => 
                                        defaultDuplicateCheck(lead)
                                    case Some(days) => 
                                        timeBasedDuplicateCheck(lead, days)
                                }
                            }
                    }
                }
        }
    }

    // Helper method to implement the original duplicate check logic
    private def defaultDuplicateCheck(lead: Lead)(implicit c: Connection): JResult[Boolean] = {
        for {
            existingPhones <- lead.contact.phones.map(_.phoneNumber).toNel.map(phoneEntryService.list(_, lead.orgId)).sequence
            existingEmails <- lead.contact.emails.map(_.email).toNel.map(emailEntryService.list(_, lead.orgId)).sequence
            dupe = (existingPhones, existingEmails) match {
                case (Some(phones), Some(emails)) => phones.nonEmpty || emails.nonEmpty
                case (Some(phones), _) => phones.nonEmpty
                case (_, Some(emails)) => emails.nonEmpty
                case (None, None) => false
            }
        } yield dupe
    }

    // Helper method for time-based duplicate check
    private def timeBasedDuplicateCheck(lead: Lead, days: Int)(implicit c: Connection): JResult[Boolean] = {
        if (days <= 0) {
            Right(false)
        } else {
            val cutOffDate = OffsetDateTime.now().minusDays(days.toLong)
            val phoneNumbersNel = lead.contact.phones.map(_.phoneNumber).toNel
            val emailsNel = lead.contact.emails.map(_.email).toNel

            val checkPhonesRecentlyAssociated = phoneNumbersNel.map { nums =>
                hasLeadPhoneSince(nums, lead.orgId, cutOffDate)
            }.getOrElse(Right(false)) // No phone numbers, no duplicate by phone

            val checkEmailsRecentlyAssociated = emailsNel.map { emls =>
                hasLeadEmailSince(emls, lead.orgId, cutOffDate)
            }.getOrElse(Right(false)) // No email addresses, no duplicate by email

            for {
                isRecentDuplicateByPhone <- checkPhonesRecentlyAssociated
                isRecentDuplicateByEmail <- checkEmailsRecentlyAssociated
            } yield isRecentDuplicateByPhone || isRecentDuplicateByEmail
        }
    }

    // scalastyle:off method.length
    /** Creates a lead entry in database plus associated phone numbers and email addresses.
     *  @param lead The lead information to be created, along with phone numbers and email addresses.
     *  @return The newly-created lead information.
     */
    def create(
        lead: Lead,
        created: OffsetDateTime = OffsetDateTime.now
    )(implicit t: Transaction): JResult[Lead] = {
        for {
            created <- simpleCreate[JResult](lead, created)
            leadPhones = lead.contact.phones
            // Gets a list of existing phone entries in the database based on the phone numbers provided with the lead.
            existingPhones <- leadPhones.map(_.phoneNumber).toNel match {
                case Some(phones) => phoneEntryService.list(phones, lead.orgId)
                case None => Right(Nil)
            }
            // For existing phones, upsert the phones with new information such as TCPA status. For new phones, those should be
            // inserted into the database. Finally for all phones, add the phone ids to the lead-phone join table.
            updatedExistingPhones = existingPhones.map { phone =>
                val newTcpaStatus = leadPhones.find(_.phoneNumber === phone.phoneNumber).map(_.tcpaStatus)
                phone.copy(tcpaStatus = newTcpaStatus.getOrElse(phone.tcpaStatus))
            }
            upsertedPhones <- phoneEntryService.upsert(
                leadPhones.map(phone => updatedExistingPhones.find(_.phoneNumber === phone.phoneNumber).getOrElse(phone)),
                created.orgId
            )
            syncedPhones <- leadPhoneSynchronizer.synchronize(upsertedPhones, created.id.get)
            leadEmails = lead.contact.emails
            // Gets a list of existing phone entries in the database based on the phone numbers provided with the lead.
            existingEmails <- leadEmails.map(_.email).toNel match {
                case Some(emails) => emailEntryService.list(emails, lead.orgId)
                case None => Right(Nil)
            }
            // For existing emails, upsert those with new information such as doNotContact information and NeverBounce validation
            // results. New emails should simply be inserted into the database.
            updatedEmails = leadEmails.map { email =>
                existingEmails.find(_.email === email.email)
                    .map(existing => existing.copy(
                        // If the existing email is Valid and doNotContact is set to true, do not overwrite it.
                        doNotContact = existing.doNotContact || email.doNotContact,
                        validationResult = email.validationResult
                    ))
                    .getOrElse(email)
            }
            emails <- emailEntryService.upsert(updatedEmails, lead.orgId)
            syncedEmails <- leadEmailSynchronizer.synchronize(emails, created.id.get)
            // Pull the head off of our emails and phones lists to set a default primary e-mail and phone for a lead.
            primaryEmail = emails.headOption.flatMap(_.id)
            primaryPhone = upsertedPhones.headOption.flatMap(_.id)
            leadDetails <- leadDetailsService.update[JResult](created.leadDetails)
            mortgageDetails <- lead.mortgageDetails.map( md =>
                mortgageDetailsService.upsert[JResult](md)
            ).sequence
            // Updates the new contact with the new primary phone, email, and new mortgage details.
            _ <- simpleUpdate[JResult](
                created.copy(
                    contact = created.contact.copy(
                        primaryPhoneId = primaryPhone, primaryEmailId = primaryEmail
                    ),
                    mortgageDetails = mortgageDetails
                )
            )
        } yield created.copy(
            contact = created.contact.copy(
                phones = syncedPhones,
                emails = syncedEmails,
                primaryPhoneId = primaryPhone,
                primaryEmailId = primaryEmail
            ),
            mortgageDetails = mortgageDetails
        )
    }
    // scalastyle:on method.length

    /** Updates a lead entry in database.
     *  @param lead The lead information to be updated.
     *  @return The newly-updated lead information.
     */
    private def simpleUpdate[F[_]](lead: Lead)(implicit t: Transaction, monad: JMonad[F]): F[Lead] = {
        SQL"""
            UPDATE eap.leads
            SET agent_id = ${lead.agentId},
                status_id = ${lead.status.id},
                first_name = ${lead.contact.firstName},
                middle_name = ${lead.contact.middleName},
                last_name = ${lead.contact.lastName},
                birthday = ${lead.contact.birthday},
                street = ${lead.contact.street},
                street2 = ${lead.contact.street2},
                city = ${lead.contact.city},
                state_id = ${lead.contact.state.map(_.id)},
                zip = ${lead.contact.zip},
                campaign_id = ${lead.campaignId},
                notes = ${lead.notes},
                primary_phone_id = ${lead.contact.primaryPhoneId},
                primary_email_id = ${lead.contact.primaryEmailId},
                gender_id = ${lead.contact.gender.map(_.id)},
                client_ref = ${lead.clientRef},
                client_ip = ${lead.clientIp},
                referring_url = ${lead.referringUrl},
                tcpa_token = ${lead.tcpaToken},
                tcpa_flag_id = ${lead.tcpaFlag.map(_.id)},
                tcpa_rejection_reason = ${lead.tcpaRejectionReason},
                mortgage_id = ${lead.mortgageDetails.flatMap(_.id)},
                dialer_list_id = ${lead.dialerListId},
                session_id = ${lead.sessionId},
                trusted_form_cert = ${lead.trustedFormCert}
            WHERE id = ${lead.id}
        """.executeSingleUpdate().map(_ => lead)
    }

    /** Updates a lead entry in database plus associated phone numbers and email addresses.
     *  @param lead The lead information to be updated, along with phone numbers and email addresses.
     *  @return The newly-updated lead information.
     */
    def update(lead: Lead)(implicit t: Transaction): JResult[Lead] = {
        for {
            _ <- simpleUpdate[JResult](lead)
            phones <- phoneEntryService.upsert(lead.contact.phones, lead.orgId)
            primaryPhone <- lead.contact.primaryPhoneId
                .map(id => phoneEntryService.read(id))
                .sequence
            // Make a Map, using the phone number as the key. This will guarantee
            // only unique phone numbers are kept.
            phoneMap = phones.map((pe: PhoneEntry) => pe.phoneNumber -> pe).toMap
            // Now, append the primary phone number if necessary, then take only
            // the values to get back a List[PhoneEntry] containing only unique
            // phone numbers.
            uniquePhones = primaryPhone
                .map(p => phoneMap + (p.phoneNumber -> p))
                .getOrElse(phoneMap)
                .values
                .toList
            syncedPhones <- leadPhoneSynchronizer.synchronize(uniquePhones, lead.id.get)
            emails <- emailEntryService.upsert(lead.contact.emails, lead.orgId)
            primaryEmail <- lead.contact.primaryEmailId
                .map(id => emailEntryService.read(id))
                .sequence
            // Make a Map, using the email address as the key. This will guarantee
            // only unique emails are kept.
            emailMap = emails.map((ee: EmailEntry) => ee.email -> ee).toMap
            // Now, append the primary email address if necessary, then take only
            // the values to get back a List[EmailEntry] containing only unique
            // email addresses.
            uniqueEmails = primaryEmail
                .map(e => emailMap + (e.email -> e))
                .getOrElse(emailMap)
                .values
                .toList
            syncedEmails <- leadEmailSynchronizer.synchronize(uniqueEmails, lead.id.get)
            leadDetails <- leadDetailsService.update[JResult](lead.leadDetails)
            mortgageDetails <- lead.mortgageDetails.map(mortgageDetailsService.upsert[JResult](_)).sequence
        } yield lead.copy(contact = lead.contact.copy(phones = syncedPhones, emails = syncedEmails))
    }

    /**
     * Update only the dialer list ID for the given list of leads in batches of 500.
     *
     * @param leads The leads whose dialer list ID is being updated
     */
    def updateDialerList[F[_]](leads: List[Lead])(implicit t: Transaction, monad: JMonad[F]): F[Unit] = {
        monad.tryCatchNonFatal {
            leads.grouped(500).toList.map { leads =>
                NonEmptyList.fromList(leads) match {
                    case None => ()
                    case Some(leads) => {
                        val params = leads.map { lead =>
                            Seq[NamedParameter](
                                "leadId" -> lead.id.get,
                                "dialerListId" -> lead.dialerListId
                            )
                        }

                        BatchSql(
                            """
                                UPDATE eap.leads
                                SET dialer_list_id = {dialerListId}
                                WHERE id = {leadId}
                            """,
                            params.head,
                            params.tail: _*
                        ).execute()

                        ()
                    }
                }
            }
        }.map(_ => ())
    }

    /**
     * Sets active and unreachable AAA Leads with existing offer text and a not null customer priority to ready to dial.
     * @return Count of updated leads
     */
    def setAAALeadsReadyToDial[F[_]](
        rapportIds: NonEmptyList[String]
    )(implicit t: Transaction, monad: JMonad[F]): F[List[Long]] = monad.tryCatchNonFatal {
        rapportIds.toList
            .grouped(32764) // Postgres and JDBC have a limit of 32767 parameters for a query.
            .foldLeft(List.empty[Long]) { (sum, ids) =>
                val query = if (allowNewPRLeadColumns) {
                    SQL"""
                        UPDATE eap.leads SET status_id = ${LeadStatus.ReadyToDial.id}
                        WHERE rapport_id IN ($ids)
                        AND customer_priority IS NOT NULL
                        AND offer_text IS NOT NULL
                        AND status_id IN (${LeadStatus.Active.id}, ${LeadStatus.Unreachable.id})
                        RETURNING id;
                    """
                } else {
                    SQL"""
                        UPDATE eap.leads SET status_id = ${LeadStatus.ReadyToDial.id}
                        WHERE id IN (
                            SELECT lc.lead_id
                            FROM eap.lead_cohorts lc
                            JOIN eap.opportunities o ON o.id = lc.opportunity_id
                            JOIN aaa.main_data aaa ON aaa.opportunity_id = lc.opportunity_id
                            WHERE o.rapport_id IN ($ids)
                            AND aaa.customer_priority IS NOT NULL
                            AND aaa.offer_text IS NOT NULL
                        ) AND status_id IN (${LeadStatus.Active.id}, ${LeadStatus.Unreachable.id})
                        RETURNING id;
                    """
                }

                sum ::: query.as(scalar[Long].*)
            }
    }

    /**
     * For all current Ready to Dial AAA policy review leads that :
     *  1. Were set to RTD more than 4 days ago
     *  2. Don't have entries in eap.lead_status_changes.
     * Reset their status to Active.
     * @return The number of leads that are set back to 'Active'.
     */
    def setAAALeadActive[F[_]]()(implicit t: Transaction, monad: JMonad[F]): F[List[Long]] = monad.tryCatchNonFatal {
        val query = if (allowNewPRLeadColumns) {
            SQL"""
                WITH last_RTD_change AS (
                    SELECT lsc.lead_id, lsc.new_status_id, max(lsc.changed) as "last_changed"
                    FROM eap.lead_status_changes lsc
                    WHERE lsc.new_status_id = ${LeadStatus.ReadyToDial.id}
                    AND lsc.changed > NOW() - INTERVAL '4 DAYS'
                    GROUP BY lsc.lead_id, lsc.new_status_id
                )
                UPDATE eap.leads SET status_id = ${LeadStatus.Active.id}
                WHERE id IN (
                    SELECT l.id
                    FROM eap.leads l
                    LEFT JOIN last_RTD_change lrc ON l.id = lrc.lead_id
                    WHERE lrc.last_changed IS NULL
                )
                AND rapport_id IS NOT NULL
                AND customer_priority IS NOT NULL
                AND offer_text IS NOT NULL
                AND status_id = ${LeadStatus.ReadyToDial.id}
                RETURNING id;
            """
        } else {
            SQL"""
                WITH last_RTD_change AS (
                    SELECT lsc.lead_id, lsc.new_status_id, max(lsc.changed) as "last_changed"
                    FROM eap.lead_status_changes lsc
                    WHERE lsc.new_status_id = ${LeadStatus.ReadyToDial.id}
                    AND lsc.changed > NOW() - INTERVAL '4 DAYS'
                    GROUP BY lsc.lead_id, lsc.new_status_id
                )
                UPDATE eap.leads SET status_id = ${LeadStatus.Active.id}
                WHERE id IN (
                    SELECT lc.lead_id
                    FROM eap.lead_cohorts lc
                    JOIN aaa.main_data aaa ON aaa.opportunity_id = lc.opportunity_id
                    LEFT JOIN last_RTD_change lrc ON lc.lead_id = lrc.lead_id
                    WHERE aaa.customer_priority IS NOT NULL
                    AND aaa.offer_text IS NOT NULL
                    AND lrc.last_changed IS NULL
                ) AND status_id = ${LeadStatus.ReadyToDial.id}
                RETURNING id;
            """
        }
        query.as(scalar[Long].*)
    }

    /** Creates a bi-directional relationships between two leads.
     *  @param a The first lead of the relationship. This is also the source lead of a uni-directional relation.
     *  @param b The second lead of the relationship. This is also the destination lead of a uni-directional relation.
     *  @param bidirectional Tells whether the new relationship between the first and second leads are bi-directional.
     *  @return Nothing unless there is an error with the inserts.
     */
    def createRelationship[F[_]](
        a: Lead,
        b: Lead,
        bidirectional: Boolean = false
    )(implicit t: Transaction, monad: JMonad[F]): F[Unit] = {
        if (bidirectional) relationshipService.createBidirectional[F, Lead](a, b)
        else relationshipService.create[F, Lead](a, b)
    }

    /** Updates multiple leads to another agent.
     *  @param leadIds The leads that will be reassigned.
     *  @param agent The agent that the leads will be reassigned to.
     *  @return The number reassigned leads.
     */
    private def simpleReassign[F[_]](
        leadIds: NonEmptyList[Long],
        agent: Account
    )(implicit t: Transaction, monad: JMonad[F]): F[Long] = monad.tryCatchNonFatal {
        SQL"UPDATE eap.leads SET agent_id = ${agent.id.get} WHERE id IN (${leadIds.toList})"
            .executeUpdate().toLong
    }

    /** Updates multiple leads to belong to another agent as well as any tasks the old agent might have had for the lead.
     *
     * @param leadIds A non-empty list of ids of leads to reassign the new agent to.
     * @param agent The agent that the leads will be reassigned to.
     * @param orgId The id of the organization of the current user.
     * @param assignerId The id of the user assigning the task to the agent (System update is None)
     * @return The number of reassigned leads.
     */
    def reassign[F[_]](
        leadIds: NonEmptyList[Long],
        agent: Account,
        orgId: Long,
        assignerId: Option[Long]
    )(implicit t: Transaction, monad: JMonad[F]): F[Long] = {
        for {
            leads <- list(leadIds, orgId)
            reassignedTasks <- leads.toNel.traverse(taskService.reassignLeads(_, agent, assignerId))
            reassignedLeads <- simpleReassign(leadIds, agent)
        } yield reassignedLeads
    }

    /** Takes the lead information and puts it into a new Opportunity object while also setting the lead status to 'Converted'.
     *
     *  @param id The id of the lead to be used to create the opportunity.
     *  @param agentId The agent that converted the lead to a new opportunity/contact.
     *  @param createContact If true, a new contact will be created along with the opportunity.
     *  @return The newly-created opportunities and related opportunities as a non-empty list with the main opportunity
     *      as the head.
     */
    def convert(
        id: Long,
        agentId: Long,
        createContact: Boolean = false
    )(implicit t: Transaction): JResult[NonEmptyList[Opportunity]] = {
        for {
            lead <- read[JResult](id).ensure(FailedValidation(id, "The lead has already been converted."))(
                _.status.id =!= LeadStatus.Converted.id
            )
            updatedLead <- update(lead.copy(status = LeadStatus.Converted))

            companionLeads <- listRelated[JResult](updatedLead)
            // If a related lead is not active, then do not convert it.
            nonConvertedCompanions = companionLeads.filter(_.status === LeadStatus.Active)

            // Convert active companions
            convertedCompanions <- nonConvertedCompanions.map(lead => update(lead.copy(status = LeadStatus.Converted))).sequence

            // TODO: When we add more relationships later on, we should change the opportunity and contact creates to a
            // batch create.
            allLeads = NonEmptyList(updatedLead, convertedCompanions)
            oppsAndContacts <- allLeads.map(createNewContactOpportunity(_, agentId, createContact)).sequence
            (headOpportunity, headContact) = oppsAndContacts.head

            // Create relationships for the newly create companion contacts and opportunities to their main counterparts.
            _ <- headContact.map(head =>
                oppsAndContacts.tail.map {
                    case(_, Some(contact)) => contactService.createRelationship(head, contact, bidirectional = true)
                    case(_, None) => Right(())
                }.sequence
            ).sequence

            allOpps = oppsAndContacts.map { case (opp, _) => opp }
            _ <- allOpps.tail.map(opportunityService.createRelationship[JResult](headOpportunity, _, bidirectional = true))
                .sequence
        } yield allOpps
    }

    /**
     * Converts policy review leads. If any opportunity is being copied over and is attached to an active PR
     * leads, update the lead status to Converted and return the lead so it can be attached to the new opportunity.
     *
     * NOTE: The upload process ensures that no more than one active lead exists on an opportunity at a time, but if there
     * does happen to be more than one, this function converts all of them.
     *
     * @param opportunity The opportunity that might have generated a PR lead
     * @return The old PR lead data and the newly-converted PR lead data.
     */
    def convertPRLead(opportunity: Opportunity)(implicit t: Transaction): JResult[Option[(Lead, Lead)]] = (for {
        leadCohorts <- OptionT(cohortService.listLeadCohorts[JResult](opportunity).map(_.toNel))
        leads <- OptionT.liftF(list[JResult](leadCohorts.map(_.leadId), opportunity.orgId))
        convertedLeads = leads.map(_.copy(status = LeadStatus.Converted))
        _ <- OptionT.liftF(convertedLeads.traverse(update))
        oldLead <- OptionT.fromOption[JResult](leads.headOption)
        leadToLink <- OptionT.fromOption[JResult](convertedLeads.headOption)
    } yield (oldLead, leadToLink))
        .value

    // scalastyle:off method.length
    /** For lead, create a new opportunity and contact in the database.
     *  @param lead The original lead to base the opportunity and contact from.
     *  @param agentId The agent that converted the lead to a new opportunity/contact.
     *  @param createContact If true, a new contact will be created along with the opportunity.
     *  @return The newly-create opportunity and contact (if applicable).
     */
    private def createNewContactOpportunity(
        lead: Lead,
        agentId: Long,
        createContact: Boolean
    )(implicit t: Transaction): JResult[(Opportunity, Option[Contact])] = for {
        contact <- {
            if (createContact) {
                val newContact = Contact(
                    id = None,
                    created = OffsetDateTime.now,
                    status = ContactStatus.Active,
                    agentId = agentId,
                    firstName = lead.contact.firstName,
                    middleName = lead.contact.middleName,
                    lastName = lead.contact.lastName,
                    birthday = lead.contact.birthday,
                    homeAddress = ContactAddress(
                        street = lead.contact.street,
                        street2 = lead.contact.street2,
                        city = lead.contact.city,
                        state = lead.contact.state,
                        zip = lead.contact.zip
                    ),
                    mailingAddress = ContactAddress(),
                    phones = lead.contact.phones,
                    emails = lead.contact.emails,
                    notes = lead.notes,
                    gender = lead.contact.gender,
                    primaryPhoneId = lead.contact.primaryPhoneId,
                    primaryEmailId = lead.contact.primaryEmailId,
                    contactType = lead.product match {
                        case Some(ProductType.HomeBridgeSITerm) => ContactType.HomeBridge
                        case _ => ContactType.AccuQuote
                    },
                    residencyStatus = None,
                    orgId = lead.orgId
                )
                contactService.create(newContact, lead.orgId).map(Some(_))
            } else Right(None)
        }
        productType <- lead.product.toRight(MiscError("Missing product type on lead. This is probably a Policy Review lead."))
        opportunity = Opportunity.empty.copy(
            product = productType,
            agentId = agentId,
            leadId = lead.id,
            insuredId = contact.flatMap(_.id),
            ownerId = contact.flatMap(_.id),
            payorId = contact.flatMap(_.id),
            mortgageId = lead.mortgageDetails.flatMap(_.id),
            orgId = lead.orgId
        )
        createdOpportunity <- opportunityService.create[JResult](opportunity)
        details = lead.leadDetails.toOpportunityDetails(createdOpportunity.id.get)
        updatedDetails <- opportunityDetailsService.update[JResult](details)
    } yield (createdOpportunity, contact)
    // scalastyle:on method.length

    /** Adds new phone entries to the lead
     *  @param phones The new phone entries to be added to the leads.
     *  @param leadId The id of the lead that phone entries will be added to.
     *  @return The phone entries that were added to the lead.
     */
    def addPhones(phones: List[PhoneEntry], leadId: Long)(implicit t: Transaction): JResult[List[PhoneEntry]] = {
        leadPhoneSynchronizer.assign(phones, leadId)
    }

    /** Deletes the relationship between a phone number and a lead.
     *  @param leadId The id of the lead.
     *  @param phoneId The id of the phone entry.
     *  @return Whether the lead-phone relationship was deleted.
     */
    def deletePhone[F[_]](leadId: Long, phoneId: Long)(implicit t: Transaction, monad: JMonad[F]): F[Boolean] = {
        for {
            lead <- read[F](leadId)
            _ <-
                SQL"""
                DELETE FROM eap.lead_phone_entries
                WHERE lead_id = $leadId
                AND phone_entry_id = $phoneId
            """.executeSingleUpdate[F]()
            _ <- if (lead.contact.primaryPhoneId.exists(_ === phoneId))
                simpleUpdate[F](lead.copy(contact = lead.contact.copy(primaryPhoneId = None)))
            else monad.unit
        } yield true
    }

    /** Adds new email entries to the lead
     *  @param emails The new email entries to be added to the leads.
     *  @param leadId The id of the lead that email entries will be added to.
     *  @return The email entries that were added to the lead.
     */
    def addEmails(emails: List[EmailEntry], leadId: Long)(implicit t: Transaction): JResult[List[EmailEntry]] = {
        leadEmailSynchronizer.assign(emails, leadId)
    }

    /** Deletes the relationship between a email address and a lead.
     *  @param leadId The id of the lead.
     *  @param emailId The id of the email entry.
     *  @return Whether the lead-email relationship was deleted.
     */
    def deleteEmail[F[_]](leadId: Long, emailId: Long)(implicit t: Transaction, monad: JMonad[F]): F[Boolean] = {
        for {
            lead <- read[F](leadId)
            _ <- SQL"""
                DELETE FROM eap.lead_email_entries
                WHERE lead_id = $leadId
                AND email_entry_id = $emailId
            """.executeSingleUpdate[F]()
            _ <- if (lead.contact.primaryEmailId.exists(_ === emailId))
                simpleUpdate[F](lead.copy(contact = lead.contact.copy(primaryEmailId = None)))
            else monad.unit
        } yield true
    }

    /** Retrieves the partner for a lead based on its campaign. */
    def getPartner[F[_]](lead: Lead)(implicit c: Connection, monad: JMonad[F]): F[Option[Partner]] = for {
        maybeCampaign <- lead.campaignId match {
            case Some(cid) => campaignService.read(cid)
            case None => monad.pure[Option[Campaign]](None)
        }
        maybePartner <- maybeCampaign match {
            case Some(campaign) => partnerService.read(campaign.partnerId, lead.orgId)
            case None => monad.pure[Option[Partner]](None)
        }
    } yield maybePartner

    // scalastyle:off method.length
    /** Returns a paginated list of leads.
     *  @param options The options used to filter, sort, and paginate the results. Typically derived from a GraphQL request.
     *  @param orgId The id of the organization to search for leads.
     *  @return A list of fully parsed Leads.
     */
    def paginatedList[F[_]](options: LeadOptions, orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[List[Lead]] =
        monad.tryCatchNonFatal {
            // Because each lead may be associated with multiple phone numbers and email addresses,
            // we have to do the filtering first to determine which leads meet the criteria.
            // Then we order and paginate on the result of that query, since we can't mix
            // DISTINCT and ORDER BY on different columns.
            // Finally, the lead IDs for the requested page are joined with the leads table so that we get
            // multiple rows for each lead (one per phone number and one per email address) in order for
            // the RelationalParser to work correctly and return the full lead data.
            // IF YOU MODIFY THIS, MAKE SURE TO UPDATE THE COUNT QUERY AS WELL.
            val filteredLeads =
                select(
                    "DISTINCT ON (l.id) l.id AS lead_id",
                    "l.created AS created",
                    "l.first_name AS first_name",
                    "l.last_name AS last_name",
                    "ee.email AS email",
                    "pe.phone_number AS phone_number",
                    "s.abbreviation AS state",
                    "pt.display_name AS product_type"
                )
                .from(
                    """eap.leads l
                        |LEFT JOIN eap.product_types pt ON (pt.id = l.product_type_id)
                        |LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
                        |LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
                        |LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
                        |LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
                        |LEFT JOIN eap.states s ON (l.state_id = s.id)
                        |LEFT JOIN authentication.accounts agent ON (agent.id = l.agent_id)
                        |""".stripMargin
                )
                .where("status_id".in("statuses", options.includeStatuses: _*))
                .whereOptional(options.lastName.map(ln => "lower(l.last_name)".isEqualTo("lastName" -> ln.toLowerCase)))
                .whereOptional(options.phoneNumber.map(pn => "pe.phone_number".isEqualTo("phoneNumber" -> pn)))
                .whereOptional(options.dateOfBirth.map(dob => "l.birthday".isEqualTo("dateOfBirth" -> dob)))
                .ilikeSearch(
                    options,
                    "l.first_name || ' ' || l.last_name",
                    "pe.phone_number",
                    "ee.email",
                    "agent.first_name || ' ' || agent.last_name",
                    "l.street || ' ' || l.street2 || ' '  || l.city || ' ' || s.display_name || ' ' || l.zip",
                    "l.street || ' ' || l.street2 || ' '  || l.city || ' ' || s.abbreviation || ' ' || l.zip"
                )

            val leadPage = select("*")
                .from("filtered_leads")
                .orderBy(
                    options,
                    Map(
                        "firstName" -> "first_name",
                        "lastName" -> "last_name",
                        "email" -> "email",
                        "phone" -> "phone_number",
                        "state" -> "state",
                        "productType" -> "product_type_id",
                        "created" -> "created"
                    )
                )
                .orderBy("created DESC, lead_id DESC") // Break ties by age (newest to oldest) and ID
                .withPagination(options)

            withQuery("filtered_leads", filteredLeads)
                .andWith("lead_page", leadPage)
                .select("*")
                .from(
                    """lead_page lp
                        |JOIN eap.leads l ON (l.id = lp.lead_id)
                        |LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
                        |LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
                        |LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
                        |LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
                        |LEFT JOIN eap.states s ON (s.id = l.state_id)
                        |LEFT JOIN eap.product_types p ON (p.id = l.product_type_id)
                        |LEFT JOIN eap.lead_details ld ON (ld.lead_id = l.id)
                        |LEFT JOIN eap.risk_classes rc ON (ld.risk_class = rc.id)
                        |LEFT JOIN eap.family_statuses fs ON (ld.family_status = fs.id)
                        |LEFT JOIN eap.mortgage_details md ON (md.id = l.mortgage_id)
                    """.stripMargin
                )
                .where(
                    "product_type_id".in("productTypes", options.includeProductTypes: _*)
                        .or("product_type_id IS NULL")
                )
                .where("l.organization_id".isEqualTo("organization_id" -> orgId))
                .whereOptional(if (!options.includePolicyReview) Some("product_type_id IS NOT NULL") else None)
                // Repeat the ORDER BY here so that the final results retain the original order.
                .orderBy(
                    options,
                    Map(
                        "firstName" -> "l.first_name",
                        "lastName" -> "l.last_name",
                        "email" -> "ee.email",
                        "phone" -> "pe.phone_number",
                        "state" -> "s.abbreviation",
                        "productType" -> "l.product_type_id",
                        "created" -> "l.created"
                    )
                )
                .orderBy("l.created DESC", "l.id DESC", "ee.id", "pe.id") // Break ties by age, lead ID, email, phone
                .execute(Lead.relationalParser.*)
        }
    // scalastyle:on method.length

    /**
     * Returns the number of leads matching the options in the query, without pagination.
     */
    def count[F[_]](
        options: LeadOptions, orgId: Long
    )(implicit c: Connection, monad: JMonad[F]): F[Long] = monad.tryCatchNonFatal {
        // IF YOU MODIFY THIS, MAKE SURE TO UPDATE THE PAGINATED LIST QUERY AS WELL.
        select("COUNT(DISTINCT(l.id))")
            .from(
                """eap.leads l
                    |LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
                    |LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
                    |LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
                    |LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
                    |LEFT JOIN eap.states s ON (l.state_id = s.id)
                    |LEFT JOIN authentication.accounts agent ON (agent.id = l.agent_id)
                    |""".stripMargin
            )
            .where("status_id".in("statuses", options.includeStatuses: _*))
            .where(
                "product_type_id".in("productTypes", options.includeProductTypes: _*)
                    .or("product_type_id IS NULL")
            )
            .where("l.organization_id".isEqualTo("organization_id" -> orgId))
            .whereOptional(if (!options.includePolicyReview) Some("product_type_id IS NOT NULL") else None)
            .ilikeSearch(
                options,
                "l.first_name || ' ' || l.last_name",
                "pe.phone_number",
                "ee.email",
                "agent.first_name || ' ' || agent.last_name",
                "l.street || ' ' || l.street2 || ' '  || l.city || ' ' || s.display_name || ' ' || l.zip",
                "l.street || ' ' || l.street2 || ' '  || l.city || ' ' || s.abbreviation || ' ' || l.zip"
            )
            .execute(scalar[Long].single)
    }

    /** Lists all leads with an assigned dialer list */
    def listDialingLeads[F[_]](orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[List[Lead]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT * FROM eap.leads l
                LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
                LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
                LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
                LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
                LEFT JOIN eap.states s ON (l.state_id = s.id)
                JOIN eap.lead_statuses ls ON (l.status_id = ls.id)
                LEFT JOIN eap.product_types pt ON (l.product_type_id = pt.id)
                LEFT JOIN eap.lead_details ld ON (ld.lead_id = l.id)
                LEFT JOIN eap.family_statuses fs ON (ld.family_status = fs.id)
                LEFT JOIN eap.risk_classes rc ON (ld.risk_class = rc.id)
                LEFT JOIN eap.mortgage_details md ON (md.id = l.mortgage_id)
                WHERE l.dialer_list_id IS NOT NULL AND l.status_id = ${LeadStatus.ReadyToDial.id}
                AND l.organization_id = $orgId
                ORDER BY l.id, pe.id, ee.id
            """.asRelational(Lead.relationalParser.*)
        }

    /** Updates a lead in the database with offer data.
     *  This function performs an SQL update to modify the fields of a lead record.
     *  The fields updated are: rapport_id, customer_priority, offer_text, offer_expiry_date, and offer_code.
     *  The record to be updated is determined by the id field of the passed AAALead instance.
     *  @param aaaLead The AAALead instance containing the new data for the lead.
     *  @return A Future that resolves to the number of database rows affected (should be 1 if the update is successful).
     */
    def updateLeadWithOfferData[F[_]](offerLeads: List[OfferLead])(implicit c: Connection, monad: JMonad[F]): F[Unit] = monad.tryCatchNonFatal {
        offerLeads.grouped(5000) // JDBC Named Parameter limit (32767)
            .toList
            .map { offerBundle =>
                offerBundle.toNel match {
                    case None => ()
                    case Some(offers) => {
                        val params = offers.map { offer =>
                            Seq[NamedParameter](
                                "rapportId" -> offer.rapportId,
                                "customerPriority" -> offer.customerPriority,
                                "offerText" -> offer.offerText,
                                "offerExpiryDate" -> offer.offerExpiryDate,
                                "offerCode" -> offer.offerCode,
                                "id" -> offer.leadId,
                            )
                        }
                        BatchSql(
                            """
                            UPDATE eap.leads
                            SET
                            rapport_id = {rapportId},
                            customer_priority = {customerPriority},
                            offer_text = {offerText},
                            offer_expiry_date = {offerExpiryDate},
                            offer_code = {offerCode}
                            WHERE id = {id}
                        """,
                            params.head,
                            params.tail: _*
                        ).execute()
                        ()
                    }
                }
            }
        ()
    }

    /**
     * Fetches the lead ID associated with a given rapport ID from the database.
     *
     * This function retrieves the lead ID associated with the provided rapport ID from the 'opportunities' table
     * in the database.
     *
     * @param rapportId The rapport ID for which to fetch the associated lead ID.
     * @return The lead ID corresponding to the provided rapport ID.
     */
    def fetchLeadIdForRapportId(rapportId: String)(implicit c: Connection): List[Long] = {
        val leadIds = SQL"""
            SELECT lc.lead_id
            FROM eap.lead_cohorts lc
            JOIN eap.opportunities o ON o.id = lc.opportunity_id
            WHERE o.rapport_id = $rapportId
        """.as(scalar[Long].*).toList
        if (leadIds.isEmpty) {
            throw new Exception(s"No leads found for rapportId $rapportId")
        }
        leadIds
    }

    /** Reads a lead with Offer Data from the database.
    *  @param id The id of the lead.
    *  @return The lead offer information from the database.
    */
    def readLeadWithOfferData[F[_]](id: Long)(implicit c: Connection, monad: JMonad[F]): F[OfferLead] = monad.tryOption(s"Could not find lead $id") {
        SQL"""
            SELECT
                l.id,
                l.rapport_id,
                l.customer_priority,
                l.offer_text,
                l.offer_expiry_date,
                l.offer_code
            FROM eap.leads l
            WHERE l.id = $id
        """.as(OfferLead.parser.singleOpt)
    }

    /** Checks that a lead's organization id is the same as the user's organization.
     *  @param lead The lead being accessed.
     *  @param userOrgId The id of the organization that the user belongs to.
     *  @return `PermissionLevel.Edit` if the user can access the lead, otherwise `NoView`.
     */
    private def canViewEdit[F[_]](lead: Lead, userOrgId: Long)(implicit monad: JMonad[F]): F[PermissionLevel] =
        if (lead.orgId === userOrgId) monad.pure(PermissionLevel.Edit)
        else monad.pure(PermissionLevel.NoView)

    /** Checks that a lead can be viewed by the account attempting to access it.
     *  @param lead The lead being accessed.
     *  @param account The account attempting to access the lead.
     *  @return `PermissionLevel.Edit` if the account can access the lead, otherwise `NoView`.
     */
    def canViewEdit[F[_]](lead: Lead, account: Account)(implicit monad: JMonad[F]): F[PermissionLevel] =
        canViewEdit(lead, account.orgId)

    /** Checks that a lead can be viewed by the api user attempting to access it.
     *  @param lead The lead being accessed.
     *  @param apiUser The api user attempting to access the lead.
     *  @return `PermissionLevel.Edit` if the api user can access the lead, otherwise `NoView`.
     */
    def canViewEdit[F[_]](lead: Lead, apiUser: ApiUser)(implicit monad: JMonad[F]): F[PermissionLevel] =
        canViewEdit(lead, apiUser.orgId)

    /**
     * Checks if any lead is associated with the given phone numbers for the specified organization
     * and was created on or after the provided cut-off date.
     *
     * @param phoneNumbers A non-empty list of phone numbers to check.
     * @param orgId The ID of the organization.
     * @param cutOffDate The cut-off date (inclusive) for the lead creation timestamp.
     * @param c Implicit database connection.
     * @return A JResult[Boolean] indicating true if such a lead exists, false otherwise, or an error.
     */
    def hasLeadPhoneSince(
        phoneNumbers: NonEmptyList[PhoneNumber],
        orgId: Long,
        cutOffDate: OffsetDateTime
    )(implicit c: Connection): JResult[Boolean] = Try {
        SQL"""
            SELECT EXISTS (
                SELECT 1
                FROM eap.leads l
                JOIN eap.lead_phone_entries lpe ON l.id = lpe.lead_id
                JOIN eap.phone_entries pe ON lpe.phone_entry_id = pe.id
                WHERE l.organization_id = $orgId
                AND pe.phone_number IN (${phoneNumbers.toList})
                AND l.created >= $cutOffDate
            );
        """.as(SqlParser.scalar[Boolean].single)
    }.toEither.left.map(e => MiscError(s"Error checking for leads with phone numbers: ${e.getMessage}"))

    /**
     * Checks if any lead is associated with the given email addresses for the specified organization
     * and was created on or after the provided cut-off date.
     *
     * @param emails A non-empty list of email addresses to check.
     * @param orgId The ID of the organization.
     * @param cutOffDate The cut-off date (inclusive) for the lead creation timestamp.
     * @param c Implicit database connection.
     * @return A JResult[Boolean] indicating true if such a lead exists, false otherwise, or an error.
     */
    def hasLeadEmailSince(
        emails: NonEmptyList[EmailAddress],
        orgId: Long,
        cutOffDate: OffsetDateTime
    )(implicit c: Connection): JResult[Boolean] = Try {
        SQL"""
            SELECT EXISTS (
                SELECT 1
                FROM eap.leads l
                JOIN eap.lead_email_entries lee ON l.id = lee.lead_id
                JOIN eap.email_entries ee ON lee.email_entry_id = ee.id
                WHERE l.organization_id = $orgId
                AND ee.email IN (${emails.toList})
                AND l.created >= $cutOffDate
            );
        """.as(SqlParser.scalar[Boolean].single)
    }.toEither.left.map(e => MiscError(s"Error checking for leads with email addresses: ${e.getMessage}"))
}

/** The options that can be passed to [[LeadService#paginatedList]] via GraphQL.
 *  @inheritdoc
 */
case class LeadOptions(
    @GraphQLDefault(LeadStatus.All) includeStatuses: List[LeadStatus],
    @GraphQLDefault(0L) offset: Option[Long],
    @GraphQLDefault(10L) pageSize: Option[Long],
    searchText: Option[String],
    @GraphQLDefault(Option.empty[String]) lastName: Option[String],
    @GraphQLDefault(Option.empty[PhoneNumber]) phoneNumber: Option[PhoneNumber],
    @GraphQLDefault(Option.empty[LocalDate]) dateOfBirth: Option[LocalDate],
    @GraphQLDefault(LeadOptions.defaultOrder) orderBy: List[Orderable],
    @GraphQLDefault(ProductType.All) includeProductTypes: List[ProductType],
    @GraphQLDefault(true) includePolicyReview: Boolean
) extends PaginationOptions with SearchOptions with OrderingOptions

object LeadOptions {
    def default: LeadOptions = LeadOptions(
        offset = Some(0L),
        pageSize = Some(10L),
        searchText = None,
        lastName = None,
        phoneNumber = None,
        dateOfBirth = None,
        includeStatuses = LeadStatus.All,
        orderBy = defaultOrder,
        includeProductTypes = ProductType.All,
        includePolicyReview = true
    )

    val allowedKeys: Set[String] = Set("firstName", "lastName", "email", "phone", "state", "productType", "created")

    val defaultOrder = List(Orderable("lastName", ascending = true), Orderable("firstName", ascending = true))

    implicit val reads: Reads[LeadOptions] = Json.reads[LeadOptions]
        .filter(JsonValidationError(s"Allowed values for orderBy are ${allowedKeys.mkString(", ")}")) { options =>
            options.orderBy.forall(o => allowedKeys.contains(o.key))
        }
    implicit val writes: Writes[LeadOptions] = Json.writes[LeadOptions]

    implicit lazy val sangriaType: InputObjectType[LeadOptions] = deriveInputObjectType()
}

/** A sub-model of the Lead storing related information about its contact. Includes a list of phone numbers and
 *  email addresses.
 *
 *  @param id The id of the lead this contact belongs to.
 *  @param firstName The first name of the primary contact for the lead.
 *  @param middleName The middle name of the primary contact for the lead.
 *  @param lastName The last name of the primary contact for the lead.
 *  @param birthday The birth date of the primary contact.
 *  @param street The primary street address information of the contact.
 *  @param street2 The secondary street address information of the contact.
 *  @param city The city where the contact is from.
 *  @param state The state where the contact is from.
 *  @param zip The zip code of the city of the contact.
 *  @param phones A list of phone numbers that the contact uses.
 *  @param emails A list of the email addresses that the contact uses.
 *  @param primaryPhoneId The id of the primary phone number entry that the lead uses.
 *  @param primaryEmailId The id of the primary email entry that the lead uses.
 *  @param gender The gender of the lead.
 */
case class LeadContact(
    id: Option[Long],
    created: OffsetDateTime = OffsetDateTime.now(),
    firstName: Option[String] = None,
    middleName: Option[String] = None,
    lastName: Option[String] = None,
    birthday: Option[LocalDate] = None,
    street: Option[String] = None,
    street2: Option[String] = None,
    city: Option[String] = None,
    state: Option[State] = None,
    zip: Option[String] = None,
    phones: List[PhoneEntry] = Nil,
    emails: List[EmailEntry] = Nil,
    primaryPhoneId: Option[Long] = None,
    primaryEmailId: Option[Long] = None,
    gender: Option[Gender] = None
) {
    /** The primary phone number that the LeadContact uses. */
    val primaryPhone: Option[PhoneEntry] = primaryPhoneId.flatMap(pid => phones.find(_.id.contains(pid)))

    /** The primary phone number that the LeadContact uses. */
    @GraphQLField val primaryEmail: Option[EmailEntry] = primaryEmailId.flatMap(pid => emails.find(_.id.contains(pid)))
}

object LeadContact {
    import play.api.libs.functional.syntax._ // scalastyle:ignore

    def empty = LeadContact(
        id = None,
        created = OffsetDateTime.now,
        firstName = None,
        middleName = None,
        lastName = None,
        birthday = None,
        street = None,
        street2 = None,
        city = None,
        state = None,
        zip = None,
        phones = Nil,
        emails = Nil,
        primaryPhoneId = None,
        primaryEmailId = None,
        gender = None
    )

    implicit val sangriaType: ObjectType[Unit, LeadContact] = deriveObjectType(
        ObjectTypeDescription("Contact information for a lead."),
        DocumentField("id", "The ID of the parent lead."),
        DocumentField("created", "When the lead was created in the AgentCloud system."),
        DocumentField("primaryEmail", "The primary email address that the Lead contact uses."),
        AddFields(
            Field(
                name = "primaryPhone",
                description = Some("The primary phone number that the Lead contact uses."),
                fieldType = OptionType(PhoneEntry.sangriaType),
                resolve = implicit context => context.value.primaryPhone match {
                    // If the primary phone is not resolved in the object, then try to resolve it with a fetcher
                    case None => Fetchers.PhoneEntries.deferOpt(context.value.primaryPhoneId)
                    case phone => phone
                }
            )
        )
    )

    implicit val notFoundMsg: NotFoundMessage[LeadContact] = NotFoundMessage[LeadContact]("Could not find the lead contact.")

    implicit val reads: Reads[LeadContact] = (
        (__ \ "id").readNullable[Long] and
        (__ \ "created").read[OffsetDateTime] and
        (__ \ "firstName").readNullable[String] and
        (__ \ "middleName").readNullable[String] and
        (__ \ "lastName").readNullable[String] and
        // Some old data was serialized using OffsetDateTime, but new stuff should be in LocalDate format.
        // We have to support both for old history records to continue to work among other things.
        (__ \ "birthday").readNullable[OffsetDateTime].map(_.map(_.toLocalDate()))
            .orElse((__ \ "birthday").readNullable[LocalDate]) and
        (__ \ "street").readNullable[String] and
        (__ \ "street2").readNullable[String] and
        (__ \ "city").readNullable[String] and
        (__ \ "state").readNullable[State] and
        (__ \ "zip").readNullable[String] and
        (__ \ "phones").read[List[PhoneEntry]] and
        (__ \ "emails").read[List[EmailEntry]] and
        (__ \ "primaryPhoneId").readNullable[Long] and
        (__ \ "primaryEmailId").readNullable[Long] and
        (__ \ "gender").readNullable[Gender]
    )(LeadContact.apply _)
    implicit val writes: Writes[LeadContact] = Json.writes[LeadContact]
        .withValue("primaryPhone", _.primaryPhone)
        .withValue("primaryEmail", _.primaryEmail)

    val parser: RowParser[LeadContact] = {
        get[Option[Long]]("leads.id") ~
        get[OffsetDateTime]("leads.created") ~
        get[Option[String]]("leads.first_name") ~
        get[Option[String]]("leads.middle_name") ~
        get[Option[String]]("leads.last_name") ~
        get[Option[LocalDate]]("leads.birthday") ~
        get[Option[String]]("leads.street") ~
        get[Option[String]]("leads.street2") ~
        get[Option[String]]("leads.city") ~
        State.parser.? ~
        get[Option[String]]("leads.zip") ~
        get[Option[Long]]("leads.primary_phone_id") ~
        get[Option[Long]]("leads.primary_email_id") ~
        get[Option[Gender]]("leads.gender_id") map {
            case id~created~firstName~middleName~lastName~birthday~street~street2~city~state~zip~
                primaryPhoneId~primaryEmailId~gender =>
                LeadContact.empty.copy(
                    id = id, created = created, firstName = firstName, middleName = middleName, lastName = lastName,
                    birthday = birthday, street = street, street2 = street2, city = city, state = state, zip = zip,
                    primaryPhoneId = primaryPhoneId, primaryEmailId = primaryEmailId, gender = gender
                )
        }
    }

    implicit val rf: RowFlattener2[LeadContact, PhoneEntry, EmailEntry] = RowFlattener[LeadContact, PhoneEntry, EmailEntry] {
        (leadContact, phones, emails) => leadContact.copy(phones = phones, emails = emails)
    }
    val relationalParser = RelationalParser(parser, PhoneEntry.parser, EmailEntry.parser)
}

/**
 *  @param leadId The id of the Lead.
 *  @param rapportId The id of the rapport related to the Lead.
 *  @param customerPriority The customer priority of the Lead.
 *  @param offerText The text of the offer sent to the Lead.
 *  @param offerExpiryDate The expiry date of the offer sent to the Lead.
 *  @param offerCode The code of the offer sent to the Lead.
 **/
case class OfferLead(
    leadId: Long,
    rapportId: Option[String] = None,
    customerPriority: Option[Int] = None,
    offerText: Option[String] = None,
    offerExpiryDate: Option[LocalDate] = None,
    offerCode: Option[String] = None
)

 object OfferLead {
    val parser: RowParser[OfferLead] = {
        get[Long]("leads.id") ~
        get[Option[String]]("leads.rapport_id") ~
        get[Option[Int]]("leads.customer_priority") ~
        get[Option[String]]("leads.offer_text") ~
        get[Option[LocalDate]]("leads.offer_expiry_date") ~
        get[Option[String]]("leads.offer_code") map to(OfferLead.apply _)
    }
}

@Singleton
class LeadContactService @Inject()(
    @NamedDatabase("default") val db: Database
) extends DatabaseHandler {
    val dbErrorMap = PartialFunction.empty

    /** Reads a LeadContact from the database.
     *  @param id The id of the LeadContact.
     *  @return The LeadContact or an error.
     */
    def read(id: Long)(implicit c: Connection): JResult[LeadContact] = SQLFunction.find {
        SQL"""
            SELECT * FROM eap.leads l
            LEFT JOIN eap.lead_phone_entries lpe ON (lpe.lead_id = l.id)
            LEFT JOIN eap.phone_entries pe ON (pe.id = lpe.phone_entry_id)
            LEFT JOIN eap.lead_email_entries lee ON (lee.lead_id = l.id)
            LEFT JOIN eap.email_entries ee ON (ee.id = lee.email_entry_id)
            LEFT JOIN eap.states s ON (l.state_id = s.id)
            WHERE l.id = $id
            ORDER BY pe.id, ee.id
        """.asRelational(LeadContact.relationalParser.singleOpt)
    }
}

object LeadDispositionToStatusMapping {
    /** Map of Accuquote dispositions to lead statuses. */
    lazy val mapping: Map[String, LeadStatus] = Map(
        "Bad Telemarketing" -> LeadStatus.BogusBadTMTransfer,
        "Bogus Lead" -> LeadStatus.BogusBadTMTransfer,
        "DNC" -> LeadStatus.NotInterested,
        "Do Not Call" -> LeadStatus.NotInterested,
        "Ineligible" -> LeadStatus.Ineligible,
        "Invalid Number" -> LeadStatus.BogusBadTMTransfer,
        "Not Interested" -> LeadStatus.NotInterested,
        "PI - Contact Reached" -> LeadStatus.PIContactReached,
        "Contact in Future" -> LeadStatus.NotInterested
    )
}
