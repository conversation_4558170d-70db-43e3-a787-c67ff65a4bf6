package com.elagy.models
package api

import cats.data.{ EitherT, Validated }
import cats.implicits._
import com.elagy.agentcloud.api
import com.elagy.integrations.neverbounce.{ NeverBounceResponseSuccess, ValidationResult, Gateway => NeverBounceGateway }
import com.elagy.integrations.GatewayException
import com.elagy.models.api.ApiError._
import com.elagy.models.jornaya.{ Profile, ProfileService }
import com.elagy.models.medicare.{ LeadMedicareDetails, LeadMedicareDetailsService, MedicareCoverage, MedicareType }
import com.elagy.tcpa.{ Flag, TcpaGateway }
import com.elagy.util.FunctionalImplicits._
import com.jaroop.blocks.authentication.models.InsufficientAuthorityException
import com.jaroop.core.auth.basic.ApiUser
import com.jaroop.core.datatypes.EmailAddress
import com.jaroop.core.errors._
import com.jaroop.core.libs.sql.DatabaseOps._
import java.sql.Connection
import java.time.OffsetDateTime
import javax.inject.{ Inject, Singleton }
import play.api.{ Configuration, Logger }
import play.api.db.{ Database, NamedDatabase }
import scala.concurrent.{ ExecutionContext, Future }
import scala.language.implicitConversions
import scala.util.Try

/**
 * Handles transformations between API leads and the standard lead model.
 * @todo Migrate more of the controller logic and legacy AQ API validation to this class.
 */
@Singleton
class ApiLeadService @Inject() (
    blacklistService: BlacklistService,
    campaignService: CampaignService,
    config: Configuration,
    @NamedDatabase("default") db: Database,
    leadService: LeadService,
    leadMedicareDetailsService: LeadMedicareDetailsService,
    neverbounceGateway: NeverBounceGateway,
    profileService: ProfileService,
    tcpaGateway: TcpaGateway
)(implicit ec: ExecutionContext) {

    import ApiLeadConversions._ // scalastyle:ignore

    /** The username of the HomeBridge api user to match on. */
    private val homebridgeUser = config.get[String]("com.elagy.integrations.agentcloud.api.homebridgeUser")

    private val logger = Logger(this.getClass().getCanonicalName())

    /**
     * Converts an API lead into a regular [[com.elagy.models.Lead]] and saves it in the database.
     * @param apiLead The submitted lead.
     * @param user The API user that submitted the lead.
     * @return A tuple containing the main lead in the first position and, for Homebridge leads,
     *         the companion in the second position as an `Option`.
     */
    def create(apiLead: api.Lead, user: ApiUser): EitherT[Future, ApiError, (Lead, Option[Lead])] =
        for {
            campaign <- EitherT(db.asyncTransaction(implicit c => getCampaign[Try](apiLead, user).value))
            flag <- campaign.defaultTcpaStatus match {
                case Some(_) => EitherT.pure[Future, ApiError](None)
                case None => checkTcpa(apiLead, campaign)
            }
            isHomebridge = user.username === homebridgeUser
            // Check the main and companion emails to see if they are valid.
            emailsToValidate = List(apiLead.contact.email, apiLead.companion.flatMap(_.email)).flatten
            validatedEmails <- EitherT.liftF(checkEmails(emailsToValidate))
            converted <- EitherT.fromEither[Future](
                convert(apiLead, campaign, flag, user, isHomebridge, validatedEmails).toEither
            )
            validated <- validateCreate(converted, campaign, isHomebridge)
            result <- EitherT.liftF {
                db.asyncTransaction { implicit c =>
                    for {
                        mainLead <- leadService.create(validated)
                        companion = apiLead.companion.map(convertCompanion(_, mainLead, validatedEmails))
                        createdCompanion <- companion.map(leadService.create(_)).sequence
                        _ <- createdCompanion.map(companion =>
                            leadService.createRelationship[JResult](mainLead, companion, bidirectional = true)
                        ).sequence
                        medicareDetailsOpt = apiLead.leadMedicareDetails.map(convertMedicare(mainLead.id.get, _))
                        _ <- medicareDetailsOpt.traverse(leadMedicareDetailsService.upsert[JResult])
                    } yield (mainLead, createdCompanion)
                }
            }
        } yield result

    /**
     * Converts an API lead into a regular [[com.elagy.models.Lead]] and uses it to update an existing lead.
     *
     * Note: The TCPA token and flag cannot be updated. Any passed-in values will be overwritten by what's already
     * in the database.
     *
     * @param lead The submitted lead with updated information.
     * @param user The API user that submitted the lead.
     * @return A tuple containing the original lead in the first position and the updated lead in the second position.
     */
    def update(lead: api.Lead, user: ApiUser, id: Long): EitherT[Future, ApiError, (Lead, Lead)] =
        for {
            campaign <- EitherT(db.asyncTransaction(implicit c => getCampaign[Try](lead, user).value))
            original <- EitherT.liftF(db.asyncTransaction(implicit c => for {
                lead <- leadService.read[Try](id)
                _ <- leadService.canViewEdit[Try](lead, user)
                    .ensure(InsufficientAuthorityException("You do not have permission to perform this action."))(
                        _ === PermissionLevel.Edit
                    )
            } yield lead))
                .ensure(LeadClosed("The lead was already reached."))(lead => !LeadStatus.ClosedStatuses.contains(lead.status))
            isHomebridge = user.username === homebridgeUser
            // Check the main email to see if it is valid.
            emailsToValidate = List(lead.contact.email).flatten
            validatedEmails <- EitherT.liftF(checkEmails(emailsToValidate))
            converted <- EitherT.fromEither[Future](
                convert(lead, campaign, original.tcpaFlag, user, isHomebridge, validatedEmails).toEither
            )
            merged = mergeLeadOptionalFields(original, converted)
            validated <- validateUpdate(merged, campaign, isHomebridge)
            updated <- EitherT.liftF(db.asyncTransaction { implicit c => for {
                updated <- leadService.update(validated)
                oldMedicareDetails <- leadMedicareDetailsService.read[JResult](id)
                medicareDetailsOpt = lead.leadMedicareDetails.map(convertMedicare(id, _))
                mergedDetailsOpt = medicareDetailsOpt.map(mergeMedicareDetails(oldMedicareDetails, _))
                _ <- mergedDetailsOpt.traverse(leadMedicareDetailsService.upsert[JResult])
            } yield updated})
        } yield (original, updated)

    /**
     * Determines the campaign that should be associated with a lead.
     *
     * If the lead has an explicit campaign name, then this function validates that the user has permission to associate
     * leads with that campaign.
     *
     * If the lead does not have an explicit campaign name, then this function looks up the default campaign for the user.
     * If the user does not have a default campaign, then validation fails and a [[com.elagy.models.api.ApiError]] is returned.
     *
     * @param lead The lead to validate. May or may not have the name of a campaign set in the `campaignName` field.
     * @param user The user that submitted the lead to the API. Must have permission to associated leads with the requested
     *             campaign. If no specific campaign name was sent, then the user must have a default campaign configured.
     * @return The campaign for the lead, if found.
     */
    private[api] def getCampaign[F[_]](
        lead: api.Lead,
        user: ApiUser
    )(implicit c: Connection, monad: JMonad[F]): EitherT[F, ApiError, Campaign] = {
        // Try to get the campaign from the campaign name in the lead, if one is present.
        // Fail if the campaign was not found.
        // Fail if the user does not have permission to access the campaign.
        EitherT {
            lead.campaignName match {
                // If no name was provided, attempt to look up the default campaign.
                case None => campaignService.getDefault[F](user).map {
                    case Some(default) => Right(default)
                    case None => Left[ApiError, Campaign](NotValid(s"No default campaign configured for user ${user.username}."))
                }
                // If a name was provided, attempt to read it
                case Some(name) => campaignService.read[F](name, user.orgId) flatMap {
                    // If the campaign exists, check that the user has access to the campaign.
                    case Some(campaign) => campaignService.hasAccess[F](user, campaign) map {
                        case true => Right(campaign)
                        case _ => Left[ApiError, Campaign](NotAllowed(s"User does not have access to campaign $name"))
                    }
                    // If the campaign does not exist, return an error indicating that.
                    case None => monad.pure(Left[ApiError, Campaign](NotValid(s"""No campaign found for name "$name".""")))
                }
            }
        }
    }

    /**
     * Validate a lead being created through the API. Checks whether the lead is a duplicate, whether its default phone
     * number is blacklisted, and makes sure it includes a mortgage details section if and only if it is a Homebridge
     * lead.
     *
     * @param lead The lead being validated
     * @param isHomebridge Whether the lead is a Homebridge lead
     * @return The lead if it is valid, an ApiError if it is invalid
     */
    private[api] def validateCreate(lead: Lead, campaign: Campaign, isHomebridge: Boolean): EitherT[Future, ApiError, Lead] =
        for {
            isDuplicate <- EitherT.liftF(db.asyncTransaction(implicit t => leadService.isDuplicate(lead)))
            isPhoneBlacklisted <- EitherT.liftF(db.asyncTransaction { implicit t =>
                lead.defaultPhone
                    .map(blacklistService.isPhoneNumberBlacklisted[JResult])
                    .sequence
                    .map(_.getOrElse(false))
            })
            _ <- validate[Future](
                lead = lead,
                isHomebridge = isHomebridge,
                isDuplicate = isDuplicate,
                isPhoneBlacklisted = isPhoneBlacklisted,
                campaign = campaign
            )
        } yield lead

    /**
     * Validate a lead being updated through the API. Checks whether its default phone number is blacklisted and makes
     * sure it includes a mortgage details section if and only if it is a Homebridge lead.
     *
     * @param lead The lead being validated
     * @param isHomebridge Whether the lead is a Homebridge lead
     * @return The lead if it is valid, an ApiError if it is invalid
     */
    private[api] def validateUpdate(lead: Lead, campaign: Campaign, isHomebridge: Boolean): EitherT[Future, ApiError, Lead] =
        for {
            isPhoneBlacklisted <- EitherT.liftF(db.asyncTransaction { implicit t =>
                lead.defaultPhone
                    .map(blacklistService.isPhoneNumberBlacklisted[JResult])
                    .sequence
                    .map(_.getOrElse(false))
            })
            _ <- validate[Future](
                lead = lead,
                isHomebridge = isHomebridge,
                isDuplicate = false,
                isPhoneBlacklisted = isPhoneBlacklisted,
                campaign = campaign
            )
        } yield lead

    /** Maps invalid conditions to error messages and returns the final validation. */
    private[api] def validate[F[_]](
        lead: Lead,
        isHomebridge: Boolean,
        isDuplicate: Boolean,
        isPhoneBlacklisted: Boolean,
        campaign: Campaign
    )(implicit monad: JMonad[F]): EitherT[F, ApiError, Lead] = {
        lazy val rejectedFlag = lead.tcpaFlag match {
            case None => true // Reject if there's no TCPA Flag
            case Some(flag) if flag.id === Flag.Green.id || flag.id === Flag.Yellow.id => false // Accept if Green or Yellow
            case Some(_) => true // Reject if anything else (i.e. Red)
        }
        val errorMessages = List(
            (isHomebridge && lead.mortgageDetails.isEmpty).option("Homebridge leads must include mortgage details."),
            (!isHomebridge && lead.mortgageDetails.isDefined).option("Non-Homebridge leads must not include mortgage details."),
            isDuplicate.option("A lead with matching phone number or email address already exists."),
            isPhoneBlacklisted.option("The primary phone number on the lead is blacklisted."),
            // Reject leads if campaign is set up to reject leads and the TCPA flag is missing or non-Green/Yellow
            (campaign.rejectOnTcpaFailure && rejectedFlag && campaign.defaultTcpaStatus.isEmpty)
                .option("Missing or failed TCPA audit for the lead based on campaign.")
        ).flatten

        errorMessages.toNel match {
            case None => EitherT.fromEither[F](Right(lead))
            case Some(messages) => EitherT.fromEither[F](Left(NotValid(messages)))
        }
    }

    /**
     * If the campaign requires TCPA auditing, this method submits the lead's token to Jornaya for auditing, returning a green,
     * yellow, or red flag indicating the result of the audit. If the lead has no token, an audit is not performed.
     *
     * @param lead The lead to validate.
     * @param campaign The campaign the lead is associated with.
     * @return The result of the TCPA audit, if one was performed.
     */
    private[api] def checkTcpa(lead: api.Lead, campaign: Campaign)
        (implicit ec: ExecutionContext): EitherT[Future, ApiError, Option[Flag]] =
        lead.tcpaToken match {
            case Some(token) => audit(token, campaign).map(Some(_))
            case None => EitherT.pure(None)
        }

    /**
     * Audits a lead for TCPA compliance.
     * @param id The Jornaya LeadiD provided in the request.
     * @param campaign The lead's campaign. Must have a Jornaya profile ID. May or may not have a provider entity code.
     * @return The Jornaya audit flag.
     */
    private def audit(id: String, campaign: Campaign)
        (implicit ec: ExecutionContext): EitherT[Future, ApiError, Flag] = {
        for {
            profileId <- campaign.jornayaProfileId match {
                case Some(jid) => EitherT.pure[Future, ApiError](jid)
                case None => EitherT.leftT[Future, Long] {
                    AuditError(s"No Jornaya profile configured for campaign ${campaign.name}. Contact AccuQuote for assistance.")
                }
            }
            profile <- EitherT {
                db.asyncTransaction(implicit c => profileService.read[Try](profileId)).map {
                    case Some(profile) => profile.asRight[ApiError]
                    case None =>
                        AuditError(
                            s"No Jornaya profile configured for campaign ${campaign.name}. Contact AccuQuote for assistance."
                        ).asLeft[Profile]
                }
            }
            flag <- EitherT.liftF(tcpaGateway.audit(
                profile = profile.toJornayaProfile,
                leadId = id,
                provider = campaign.providerEntityCode
            ))
        } yield flag
    }

    /**
     * Converts a lead from the Lead API schema to the Lead model for storing into the database.
     *
     * @param lead The
     * @param tcpaFlag The TCPA flag that was evaluated for the lead.
     * @param isHomebridge Tells whether the API Lead came from Homebridge. Among other things, this forces the product
     *                     type to be Homebridge SI term.
     * @param validatedEmails A map between the email address and NeverBounce validation responses.
     * @return The lead model to be created in the database.
     */
    private[api] def convert(
        lead: api.Lead,
        campaign: Campaign,
        tcpaFlag: Option[Flag],
        user: ApiUser,
        isHomebridge: Boolean,
        validatedEmails: Map[EmailAddress, NeverBounceResponseSuccess]
    ): Validated[NotValid, Lead] = {
        val product = getLeadProduct(lead.product, isHomebridge, user.orgId)
        val status = getStatus(lead, tcpaFlag, campaign, isHomebridge)
        // Use the campaign's default TCPA status or derive it from the flag, falling back to "No" as the global default
        val tcpaStatus = campaign.defaultTcpaStatus
            .orElse(tcpaFlag.map(getTCPAStatus))
            .getOrElse(TCPAStatus.No)
        val contact = convertContact(lead.contact, tcpaStatus, validatedEmails, user.orgId)
        product.map { product =>
            Lead(
                id = None,
                status = status,
                contact = contact,
                product = Some(product),
                agentId = None,
                campaignId = Some(campaign.id),
                notes = None,
                clientRef = Some(lead.clientRef),
                clientIp = lead.clientIp,
                referringUrl = lead.referringUrl,
                tcpaToken = lead.tcpaToken,
                tcpaFlag = tcpaFlag,
                tcpaRejectionReason = None,
                leadDetails = lead.policyDetails
                    .map(convertDetails(_, lead.mortgageDetails.map(_.borrowerId)))
                    .getOrElse(LeadDetails.empty),
                mortgageDetails = lead.mortgageDetails.map(convertMortgage),
                dialerListId = None,
                apiUserId = user.id,
                orgId = user.orgId,
                sessionId = lead.sessionId,
                trustedFormCert = lead.trustedFormCert
            )
        }
    }

    private[api] def getTCPAStatus(flag: Flag): TCPAStatus = flag match {
        case Flag.Green => TCPAStatus.Yes
        case Flag.Yellow => TCPAStatus.Yes
        case _ => TCPAStatus.No
    }

    /**
     * If we have a lead from HomeBridge, we need to return the lead product as HomeBridge-SITERM. Otherwise, we return the
     * product based on what was given by the lead.
     *
     * @param isHomebridge Tells whether the lead is from HomeBridge
     * @return A valid [[ProductType]] if the lead was given a product.
     */
    private[api] def getLeadProduct(
        apiProduct: Option[api.Product],
        isHomebridge: Boolean,
        orgId: Long
    ): Validated[NotValid, ProductType] = {
        if (isHomebridge) Validated.valid(ProductType.HomeBridgeSITerm)
        else {
            (orgId, apiProduct) match {
                case (_, Some(api.Product.FE)) => Validated.valid(ProductType.FinalExpense)
                case (1L, Some(api.Product.SITERM)) => Validated.valid(ProductType.SimplifiedIssueTerm)
                case (1L, Some(api.Product.TERM)) => Validated.valid(ProductType.Term)
                case (_, Some(api.Product.MEDICARE)) => Validated.valid(ProductType.Medicare)
                case (_, Some(api.Product.U65)) => Validated.valid(ProductType.Under65)
                case (_, Some(productType)) => Validated.invalid(NotValid(s"""Leads product type is invalid: $productType"""))
                case (_, None) => Validated.invalid(NotValid("""Leads must have a valid "product" field."""))
            }
        }
    }

    /**
     * If we have a lead from Homebridge, then the lead status is based on the borrower status. If we do not get the
     * lead status from the borrower status, set the lead status to Active.
     *
     * For non-Homebridge leads, check the TCPA flag. If we got a green flag, then the
     * lead status is Ready to Dial. If we got a yellow or red flag, then the lead status is Active.
     *
     * For all other leads, set the status to `Ready to Dial`
     *
     * @param auditFlag The TCPA audit flag result.
     * @param isHomebridge Tells whether the API Lead is from HomeBridge.
     */
    private[api] def getStatus(lead: api.Lead, auditFlag: Option[Flag], campaign: Campaign, isHomebridge: Boolean): LeadStatus = {
        if (isHomebridge) {
            lead.mortgageDetails.map(_.borrowerStatus.toLeadStatus)
                .getOrElse(LeadStatus.Active)
        } else {
            LeadStatus.ReadyToDial
        }
    }

    /** Converts this api lead contact object to a LeadContact sub-model.
     *
     * @param tcpaStatus The status of the tcpa for the phone entries.
     * @param validatedEmails A map between the email address and NeverBounce validation responses.
     * @param orgId The id of the organization the lead is created with.
     * @return The lead contact sub-model to be created in the database.
     */
    private[api] def convertContact(
        contact: api.LeadContact,
        tcpaStatus: TCPAStatus,
        validatedEmails: Map[EmailAddress, NeverBounceResponseSuccess],
        orgId: Long
    ): LeadContact = {
        LeadContact(
            id = None,
            created = OffsetDateTime.now(),
            firstName = Some(contact.firstName),
            middleName = contact.middleName,
            lastName = Some(contact.lastName),
            birthday = contact.dateOfBirth,
            street = contact.street,
            street2 = contact.street2,
            city = contact.city,
            state = contact.state,
            zip = contact.zip,
            phones = contact.phones.map(_.toPhoneEntry(tcpaStatus, orgId)).toList,
            emails = (for {
                email <- contact.email
                neverBounce = validatedEmails.get(email)
                entry = convertEmail(email, neverBounce, orgId)
            } yield entry).toList,
            primaryPhoneId = None,
            primaryEmailId = None,
            gender = contact.gender.map(_.toGender)
        )
    }

    /**
     * Converts an api lead policy details object to a model that can be stored in the database.
     *
     * @param borrowerId The id of the borrower from the mortgage details if they were given.
     * @return The lead policy details model to be created in the database.
     */
    private[api] def convertDetails(details: api.LeadDetails, borrowerId: Option[String]): LeadDetails = LeadDetails(
        leadId = None,
        termLength = details.termLength,
        tobacco = details.tobacco,
        riskClass = details.riskClass.map(_.toRiskClass),
        optInDateTime = details.optInDateTime,
        faceAmount = details.faceAmount,
        height = details.height,
        weight = details.weight,
        usCitizen = details.usCitizen,
        familyStatus = details.familyStatus.map(_.toFamilyStatus),
        incomeMonthly = details.incomeMonthly,
        borrowerId = borrowerId
    )

    // scalastyle:off method.length
    /** Converts a Lead Companion object to a separate lead model.
     *
     * @param companion The API companion object to convert
     * @param mainLead The primary lead to which the companion was attached.
     * @param validatedEmails A map between the email address and NeverBounce validation responses.
     * @return A newly-converted lead model.
     */
    private[api] def convertCompanion(
        companion: api.LeadCompanion,
        mainLead: Lead,
        validatedEmails: Map[EmailAddress, NeverBounceResponseSuccess]
    ): Lead = {
        val tcpaStatus = mainLead.tcpaFlag.map(getTCPAStatus).getOrElse(TCPAStatus.No)
        Lead(
            id = None,
            status = LeadStatus.Active,
            contact = LeadContact(
                id = None,
                created = OffsetDateTime.now,
                firstName = Some(companion.firstName),
                middleName = None,
                lastName = Some(companion.lastName),
                birthday = companion.dateOfBirth,
                street = companion.street,
                street2 = companion.street2,
                city = companion.city,
                state = companion.state,
                zip = companion.zip,
                phones = companion.phones
                    .map(_.toPhoneEntry(tcpaStatus, mainLead.orgId))
                    .toList,
                emails = (for {
                    email <- companion.email
                    neverBounce = validatedEmails.get(email)
                    entry = convertEmail(email, neverBounce, mainLead.orgId)
                } yield entry).toList,
                primaryPhoneId = None,
                primaryEmailId = None,
                gender = companion.gender.map(_.toGender)
            ),
            product = mainLead.product,
            agentId = mainLead.agentId,
            campaignId = mainLead.campaignId,
            notes = None,
            clientRef = mainLead.clientRef.map(_ + "_companion"),
            clientIp = mainLead.clientIp,
            referringUrl = mainLead.referringUrl,
            tcpaToken = mainLead.tcpaToken,
            tcpaFlag = mainLead.tcpaFlag,
            tcpaRejectionReason = mainLead.tcpaRejectionReason,
            leadDetails = LeadDetails(
                leadId = None,
                termLength = companion.termLength,
                tobacco = None,
                riskClass = None,
                optInDateTime = None,
                faceAmount = companion.faceAmount,
                height = None,
                weight = None,
                usCitizen = None,
                familyStatus = None,
                incomeMonthly = companion.incomeMonthly,
                borrowerId = Some(companion.borrowerId)
            ),
            mortgageDetails = mainLead.mortgageDetails,
            dialerListId = None,
            apiUserId = mainLead.apiUserId,
            orgId = mainLead.orgId
        )
    }

    /** Converts an api mortgage details object to a model that can be stored in the database.
     *
     * @return The mortgage details model to be created in the database.
     */
    private[api] def convertMortgage(mortgageDetails: api.MortgageDetails): MortgageDetails = MortgageDetails(
        id = None,
        borrowerStatus = mortgageDetails.borrowerStatus.toBorrowerStatus,
        loanNumber = mortgageDetails.mortgageId,
        mortgageCloseDate = mortgageDetails.mortgageCloseDate,
        insurancePurpose = mortgageDetails.insurancePurpose,
        faceAmount = mortgageDetails.faceAmount,
        termLength = mortgageDetails.termLength,
        loanOfficerFirstName = mortgageDetails.loanOfficerFirstName,
        loanOfficerLastName = mortgageDetails.loanOfficerLastName,
        loanOfficerPhone = Some(mortgageDetails.loanOfficerPhone),
        loanOfficerEmail = Some(mortgageDetails.loanOfficerEmail),
        processorFirstName = mortgageDetails.processorFirstName,
        processorLastName = mortgageDetails.processorLastName,
        processorPhone = Some(mortgageDetails.processorPhone),
        processorEmail = Some(mortgageDetails.processorEmail),
        tpoFirm = None
    )

    /** When a lead is updated, we parse it from the JSON and read it from the database. In order to properly updated it, we need
     *  to fill in the parsed lead with information that only exists in the version read from database.
     *
     *  Copy the lead, replacing missing optional fields with the value of the corresponding field as read from the database.
     *  This only operates on specific fields such as IDs, otherwise the API users wouldn't be able to clear fields that were
     *  erroneously provided.
     *
     *  @param oldLead The lead as read from the database.
     *  @param newLead The lead as parsed from JSON/ApiLead.
     *  @return the Lead with its missing fields corrected.
     */
    private[api] def mergeLeadOptionalFields(oldLead: Lead, newLead: Lead): Lead = newLead.copy(
        id = newLead.id.map(_.some).getOrElse(oldLead.id),
        // Never change the TCPA token after a lead is initially created.
        tcpaToken = oldLead.tcpaToken,
        contact = newLead.contact.copy(
            id = newLead.contact.id.map(_.some).getOrElse(oldLead.contact.id),
            primaryPhoneId = newLead.contact.primaryPhoneId.map(_.some).getOrElse(oldLead.contact.primaryPhoneId),
            primaryEmailId = newLead.contact.primaryEmailId.map(_.some).getOrElse(oldLead.contact.primaryEmailId)
        ),
        agentId = newLead.agentId.map(_.some).getOrElse(oldLead.agentId),
        leadDetails = newLead.leadDetails.copy(
            leadId = newLead.leadDetails.leadId.map(_.some).getOrElse(oldLead.leadDetails.leadId),
            borrowerId = newLead.leadDetails.borrowerId.map(_.some).getOrElse(oldLead.leadDetails.borrowerId)
        ),
        mortgageDetails = newLead.mortgageDetails.map(_.copy(
            id = newLead.mortgageDetails.flatMap(_.id).map(_.some).getOrElse(oldLead.mortgageDetails.flatMap(_.id))
        ))
    )

    /** Converts an email address with a NeverBounce result to an email entry. */
    private[api] def convertEmail(
        email: EmailAddress,
        neverBounceResponse: Option[NeverBounceResponseSuccess],
        orgId: Long
    ): EmailEntry = {
        /** If we have a suggested email from NeverBounce, set the validation result to `Valid`. */
        val validationResult = neverBounceResponse.map(response => response.suggestedCorrection match {
            case Some(_) => ValidationResult.Valid
            case None => response.result
        })

        val doNotContact = neverBounceResponse
            .map(response => !ValidationResult.safeSendResults.contains(response.result))
            .getOrElse(true)

        EmailEntry(
            id = None,
            email = neverBounceResponse.flatMap(response => response.result match {
                case ValidationResult.Valid => Some(email)
                case _ => response.suggestedCorrection
            }).getOrElse(email),
            created = OffsetDateTime.now(),
            updated = OffsetDateTime.now(),
            doNotContact = doNotContact,
            validationResult = validationResult,
            orgId = orgId
        )
    }

    /** For a list of email addresses, make a call to the NeverBounce API and validate all of them.
     *
     *  @param emails The emails to be validated.
     *  @return A map of the email addresses to the NeverBounce results if the API calls succeeded.
     */
    private[api] def checkEmails(emails: List[EmailAddress]): Future[Map[EmailAddress, NeverBounceResponseSuccess]] = {
        emails.map { email =>
            neverbounceGateway.validateEmail(email)
                .map(result => Some(email -> result))
                .recover {
                    case GatewayException(_, 404, _, _, _) =>
                        logger.warn(s"NeverBounce returned a 404 for email: [${email.value}]")
                        None
                }
        }
            .sequence
            .map { recoveredResults: List[Option[(EmailAddress, NeverBounceResponseSuccess)]] =>
                (for {
                    maybeResult <- recoveredResults
                    result <- maybeResult
                } yield result).toMap
            }
    }

    /** Converts the details for Medicare into a Medicare details model for the lead.
     *  @param leadId The id of the lead.
     *  @param details The details
     */
    private[api] def convertMedicare(leadId: Long, details: api.LeadMedicareDetails): LeadMedicareDetails = LeadMedicareDetails(
        leadId = leadId,
        medicareType = details.medicareType.flatMap(MedicareType.fromApiMedicareType.get(_)),
        medicareCoverage = details.medicareCoverage.flatMap(MedicareCoverage.fromApiMedicareCoverage.get(_))
    )

    /** Merges old LeadMedicareDetails in the database with the new details from the API request. */
    private[api] def mergeMedicareDetails(
        oldDetails: Option[LeadMedicareDetails],
        newDetails: LeadMedicareDetails
    ) = LeadMedicareDetails(
        leadId = newDetails.leadId,
        medicareType = newDetails.medicareType.map(_.some)
            .getOrElse(oldDetails.flatMap(_.medicareType)),
        medicareCoverage = newDetails.medicareCoverage.map(_.some)
            .getOrElse(oldDetails.flatMap(_.medicareCoverage))
    )

}

/** This object contains conversions from the AgentCloud-API Lead model and sub-models to the Lead model defined in
 * the AgentCloud project proper.
 */
private[api] object ApiLeadConversions {

    implicit final class ApiPhoneExtension(phone: api.Phone) {
        /** Converts an api phone object to a phone entry model that is associated with a lead.
         *
         * @param tcpaStatus The status of the tcpa for the phone entries.
         * @return The phone entry model to be associated with the lead.
         */
        def toPhoneEntry(tcpaStatus: TCPAStatus, orgId: Long): PhoneEntry = PhoneEntry(
            id = None,
            created = OffsetDateTime.now(),
            updated = OffsetDateTime.now(),
            phoneNumber = phone.phoneNumber,
            doNotContact = false,
            tcpaStatus = tcpaStatus,
            phoneType = phone.phoneType.toPhoneType,
            tcpaText = TCPAText.Blank,
            orgId = orgId
        )
    }

    implicit final class ApiRiskClassExtensions(rc: api.RiskClass) {
        /** Converts the ApiRiskClass SealedEnum to the RiskClass SealedEnumLike. */
        def toRiskClass: RiskClass = rc match {
            case api.RiskClass.Standard => RiskClass.Standard
            case api.RiskClass.Preferred => RiskClass.Preferred
            case api.RiskClass.PreferredPlus => RiskClass.PreferredPlus
        }
    }

    implicit final class ApiFamilyStatusExtensions(fs: api.FamilyStatus) {
        /** Converts the ApiFamilyStatus SealedEnum to the FamilyStatus SealedEnumLike. */
        def toFamilyStatus: FamilyStatus = fs match {
            case api.FamilyStatus.Single => FamilyStatus.Single
            case api.FamilyStatus.Couple => FamilyStatus.Couple
            case api.FamilyStatus.SingleWithKids => FamilyStatus.SingleWithKids
            case api.FamilyStatus.CoupleWithKids => FamilyStatus.CoupleWithKids
        }
    }

    implicit final class ApiPhoneTypeExtensions(phoneType: api.PhoneType) {
        /** Converts the ApiPhoneType SealedEnum to the PhoneType SealedEnumLike. */
        def toPhoneType: PhoneType = phoneType match {
            case api.PhoneType.Cell => PhoneType.Cell
            case api.PhoneType.Home => PhoneType.Home
            case api.PhoneType.Work => PhoneType.Work
            case api.PhoneType.Other => PhoneType.Other
        }
    }

    implicit final class ApiGenderExtensions(gender: api.Gender) {
        /** Converts the ApiGender SealedEnum to the Gender SealedEnumLike. */
        def toGender: Gender = gender match {
            case api.Gender.Male => Gender.Male
            case api.Gender.Female => Gender.Female
        }
    }

    /** A extension of the [[ApiBorrowerStatus]] class with methods for */
    implicit final class ApiBorrowerStatusExtensions(borrowerStatus: api.BorrowerStatus) {
        /** Converts the borrower status to a lead status. */
        def toLeadStatus: LeadStatus = borrowerStatus match {
            case api.BorrowerStatus.Open => LeadStatus.Active
            case api.BorrowerStatus.AutoDial => LeadStatus.ReadyToDial
            case api.BorrowerStatus.Inactive => LeadStatus.NotInterested
        }

        /** Converts the ApiBorrowerStatus SealedEnum to the main application's BorrowerStatus SealedEnumLike. */
        def toBorrowerStatus: BorrowerStatus = borrowerStatus match {
            case api.BorrowerStatus.Open => BorrowerStatus.Open
            case api.BorrowerStatus.AutoDial => BorrowerStatus.AutoDial
            case api.BorrowerStatus.Inactive => BorrowerStatus.Inactive
        }
    }
}
