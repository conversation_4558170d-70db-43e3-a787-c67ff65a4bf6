package com.elagy.models

import cats.data.{ <PERSON><PERSON>, NonEmpty<PERSON>ist, OptionT }
import cats.implicits._
import com.elagy.util.JsImplicits.jsResultApplicative
import com.jaroop.utils.{ DetailedLogger, DetailedLoggerDataInput }
import com.elagy.util.SimpleCsvParser
import com.jaroop.core.errors.{ Does<PERSON>otExist, GenError, InvalidError, InvalidException, JResult, MiscError }
import com.jaroop.core.libs.sql.DatabaseOps._

import javax.inject.{ Inject, Singleton }
import play.api.{ Configuration, Logger }
import play.api.db.{ Database, NamedDatabase }
import play.api.libs.json.{ JsObject, Json }

import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }
import scala.util.Try

/** Service for handling the creation of cohorts and upload of policy review leads. */
@Singleton
class PolicyReviewUploadService @Inject() (
    campaignService: CampaignService,
    cohortService: CohortService,
    configuration: Configuration,
    contactService: ContactService,
    @NamedDatabase("default") db: Database,
    leadService: LeadService,
    opportunityService: OpportunityService,
    partnerService: PartnerService,
    detailedLogger: DetailedLogger
) {
    // Policy review partner name.
    private val partnerName = configuration.get[String]("eap.partnerNames.policyReview")
    implicit val logger: Logger = Logger(this.getClass.getCanonicalName)
    implicit val uuid: UUID = UUID.randomUUID()

    /** Creates a cohort and its associated campaign. */
    def createPolicyReviewCohort(
        input: InitialCohortInput,
        uploader: Option[Long],
        orgId: Long,
        partnerStr: String = partnerName
    )(implicit t: Transaction): JResult[Cohort] = for {
            partner <- partnerService.read[JResult](partnerStr, orgId)
                .flatMap(_.fold[JResult[Partner]](Left(MiscError("No Policy Review partner found.")))(Right(_)))
            campaignInput = CampaignInput(
                name = input.campaignName,
                partnerId = partner.id,
                requiresTcpa = false,
                default = false,
                orgId = orgId,
                dedupeDays = None
            )
            // Check if the campaign name already exists. If it doesn't, then use the input to create one.
            existingCampaign <- campaignService.read[JResult](input.campaignName, orgId)
            campaign <- existingCampaign match {
                case Some(c) => Right(c)
                case None => campaignService.create[JResult](campaignInput, orgId)
            }
            cohortInput = CohortInput(
                uploader = uploader,
                name = input.name,
                campaignId = campaign.id,
                description = input.description
            )
            existingCohort <- cohortService.read[JResult](input.name)
            cohort <- existingCohort match {
                case Some(_) => Left(InvalidError(s"A Cohort with the name ${input.name} already exists."))
                case None => cohortService.create[JResult](cohortInput)
            }
        } yield cohort

    /** Reads a list of opportunity ID's from a CSV file, then creates PR leads for each opportunity.
     *  NOTE: Should use the ExecutionContext of it's caller.
     */
    def uploadForPolicyReview(
        cohortId: Long,
        fileContents: String,
        user: ApplicationUser
    )(implicit uuid : UUID, ec: ExecutionContext): Future[List[Lead]] = (for {
        oppIds <-
            SimpleCsvParser
                .parse(fileContents)
                .drop(1) // Ignore the CSV header.
                .flatMap(_.headOption) // Only pull the first cell, if a value is there.
                .collect {
                    case LongExtractor(oppId) => oppId
                } // Only keep values that can be parsed as a long.
                .distinct
                .toNel match {
                case Some(oppIds) => Future.successful(oppIds)
                case None => Future.failed(InvalidException("The policy review file have no Opportunity ids"))
            }

        leads <- (db.asyncTransaction { implicit t =>
            (for {
                cohortOpt <- cohortService.read[JResult](cohortId)
                cohort <- cohortOpt.toRight(DoesNotExist(s"Cohorts of id ${cohortId} selected for policy review upload does not exist"))
                batchedOppIds = oppIds.toList.grouped(900).flatMap(_.toNel).toList

                _ = detailedLogger.info(DetailedLoggerDataInput(
                module = "CSV Data Upload",
                dataset = Some("Policy Review"),
                message = s"${oppIds.length} Opportunity ids were found in the csv file for Cohorts id ${cohortId} and Request id ${uuid}"
                ))

                opportunities <- batchedOppIds
                    .map(opportunityService.list[JResult](_, user.account.orgId))
                    .sequence
                    .map(_.flatten)

                _ = if (opportunities.length < oppIds.length) {
                    val inValidOpportunities =  oppIds.toList diff opportunities.flatMap(_.id)
                    detailedLogger.warn(DetailedLoggerDataInput(
                        module = "CSV Data Upload",
                        dataset = Some("Policy Review"),
                        message = s"The number of invalid Opportunities in the csv files was ${inValidOpportunities.size}",
                        customFields = Some(JsObject(Map("Opportunity Ids" -> Json.toJson(inValidOpportunities))))
                    ))
                }

                seed = Ior.right[NonEmptyList[(GenError, Option[Long])], List[Option[Lead]]](Nil)
                leadsAndErrors = opportunities.foldLeft(seed){ (sum, opportunity) =>
                       val convertedLead = Ior.fromEither(checkConvertToLead(opportunity, cohort, user.account.orgId).value)
                       sum |+| convertedLead.bimap(error => NonEmptyList.one((error,opportunity.id)) , lead => List(lead))
                    }
                _ = leadsAndErrors.left.map( errors => errors.map{ case (error, oppId) =>
                    detailedLogger.error(DetailedLoggerDataInput(
                        module = "CSV Data Upload",
                        dataset = Some("Policy Review"),
                        message = s"Error received when creating pr lead for Opportunity id [${oppId.get}].",
                        throwable = Some(error.throwable))
                    )
                })

            } yield leadsAndErrors.right.map(_.flatten)).map(_.getOrElse(Nil))
        })

        prUploadStatusMessage = if (leads.size > 0) {
           s"[${leads.size}] pr leads have been created successfully"
        } else {
            s"no pr lead has been created"
        }
        _ = detailedLogger.info(DetailedLoggerDataInput(
        module = "CSV Data Upload",
        dataset = Some("Policy Review"),
        message = prUploadStatusMessage + s" for Cohorts id ${cohortId} and Request id ${uuid}"
        ))

    } yield leads)


    /** Extracts a long from a string value optionally, suppressing any formatting errors. */
    private object LongExtractor {
        def unapply(str: String): Option[Long] = Try(str.toLong).toOption
    }

    /**
     * Check that an opportunity that doesn't have an active PR lead, creating it if there is not one already.
     *
     * @param opportunity The opportunity generating the lead
     * @param cohort The cohort to which the PR lead belongs
     * @param orgId The id of the organization that the lead should belong to.
     * @return The created lead
     */
    private def checkConvertToLead(
        opportunity: Opportunity,
        cohort: Cohort,
        orgId: Long
    )(implicit t: Transaction): OptionT[JResult, Lead] =
        for {
            _ <- OptionT.liftF[JResult, Unit](
                if (opportunity.product === ProductType.Term || opportunity.product === ProductType.Conversion) Right(())
                else Left(InvalidError(s"The Opportunity with id : ${opportunity.id.get} is not associated with either Term or Conversion product type")))

            opportunityWithActiveCohorts <- OptionT.liftF(cohortService.hasActiveLeadCohort[JResult](opportunity))

            _ <- OptionT.liftF[JResult, Unit](
                if (!opportunityWithActiveCohorts) Right(())
                else Left(InvalidError(s"Existing Cohort found for the Opportunity id : ${opportunity.id.get}")))

            lead <- convertToLead(opportunity, cohort, orgId)
        } yield lead

    /**
     * Create a PR lead for an opportunity and join it to the cohort and the opportunity that spawned it.
     *
     * @param opportunity The opportunity generating the lead
     * @param cohort The cohort to which the PR lead belongs
     * @param orgId The id of the organization that the lead should belong to.
     * @return The created lead
     */
    def convertToLead(
        opportunity: Opportunity,
        cohort: Cohort,
        orgId: Long,
        status: LeadStatus = LeadStatus.ReadyToDial
    )(implicit t: Transaction): OptionT[JResult, Lead] = for {

        partnerOpt <- OptionT.liftF(partnerService.getPartnerForOpportunity[JResult](opportunity))
        partnerAllowPr = partnerOpt.fold(true)(_.allowPr)

        _ <- OptionT.fromOption[JResult](if (partnerAllowPr) {
            Option(())
            } else {
            detailedLogger.info(DetailedLoggerDataInput(
                module = "PR Opportunity to Lead Creation",
                dataset = Option("Policy Review"),
                message = s"No PR Lead created from opportunity ${opportunity.id} because partner is not allowed to create PR leads."
            ))
            None
        })

        contact <- OptionT.liftF(opportunity.ownerId.toRight({
            detailedLogger.warn(DetailedLoggerDataInput(
                module = "PR Opportunity to Lead Creation",
                dataset = Option("Policy Review"),
                message = s"No owner contact exists for opportunity ${opportunity.id}."
            ))
            InvalidError(s"Could not find an owner for Opportunity ${opportunity.id.get}")
        }).flatMap(contactService.read))

        leadInsert = buildLead(contact, cohort.campaignId, status, orgId)
        lead <- OptionT.liftF(leadService.create(leadInsert))
        leadCohort = LeadCohort(
            leadId = lead.id.get,
            cohortId = cohort.id,
            opportunityId = opportunity.id.get
        )
        _ <- OptionT.liftF(cohortService.createLeadCohort[JResult](leadCohort))

        _ = detailedLogger.info(DetailedLoggerDataInput(
            module = "PR Opportunity to Lead Creation",
            dataset = Option("Policy Review"),
            message = s"PR Lead ${lead.id} created off of opportunity ${opportunity.id} in Cohort ${cohort.id}"
        ))
    } yield lead

    /**
     * Build a new PR lead from the provided information.
     *
     * @param contact The Contact used to build the LeadContact
     * @param campaignId The lead's campaign
     * @param orgId The id of the organization the lead should belong to.
     * @return The new lead object, ready to be saved in the database
     */
    private def buildLead(contact: Contact, campaignId: Long, status: LeadStatus, orgId: Long): Lead = {
        // Reorder phone and email lists to put the primary at the head, so that LeadService.create will retain them
        // as primary.
        val (primaryPhone, otherPhones) =
        contact.phones.partition(_.id.exists(phoneId => contact.primaryPhoneId.exists(_ === phoneId)))
        val phones = primaryPhone ::: otherPhones

        val (primaryEmail, otherEmails) =
            contact.emails.partition(_.id.exists(emailId => contact.primaryEmailId.exists(_ === emailId)))
        val emails = primaryEmail ::: otherEmails

        Lead(
            id = None,
            status = status,
            product = None,
            contact = LeadContact(
                id = None,
                firstName = contact.firstName,
                middleName = contact.middleName,
                lastName = contact.lastName,
                birthday = contact.birthday,
                street = contact.homeAddress.street,
                street2 = contact.homeAddress.street2,
                city = contact.homeAddress.city,
                state = contact.homeAddress.state,
                zip = contact.homeAddress.zip,
                phones = phones,
                emails = emails,
                gender = contact.gender
            ),
            leadDetails = LeadDetails(
                leadId = None
            ),
            campaignId = Some(campaignId),
            orgId = orgId
        )
    }
}
