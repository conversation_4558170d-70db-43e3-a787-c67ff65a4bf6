package com.elagy.models

import anorm._
import SqlParser._
import cats.data.NonEmptyList
import cats.implicits._
import com.elagy.util.AnormImplicits.SqlUpdateOps
import com.jaroop.blocks.authentication.models.Account
import com.jaroop.core.auth.basic.ApiUser
import com.jaroop.core.errors.JMonad
import com.jaroop.core.libs.sql.AnormImplicits._
import com.jaroop.core.libs.sql.DatabaseOps.Transaction

import java.sql.Connection
import java.time.OffsetDateTime
import javax.inject.{Inject, Singleton}
import play.api.libs.functional.syntax.toFunctionalBuilderOps
import play.api.libs.json._

/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 *
 * This is different from a [[com.elagy.integrations.five9.soap.models.Campaign]], which represents a dialer campaign.
 * A dialer campaign contains rules for dialing customers.
 *
 * @param id The unique ID of the campaign, assigned by the application.
 * @param name The unique, case-sensitive name of the campaign.
 * @param partnerId The name of the partner to whom the campaign belongs.
 * @param created When the campaign was created in the database.
 * @param requiresTcpa Whether leads created under this campaign must have TCPA consent with a successful audit.
 *                     If this is true and the lead does not have TCPA consent or the audit fails,
 *                     then the lead is rejected. If it's false, then the lead may be dialed in the MTM domain.
 * @param default Whether this is the partner's default campaign. When leads are submitted to the application,
 *                the campaign may be specified explicitly. If a partner has a default campaign, then any leads
 *                submitted by that partner without an explicit campaign are associated with the default campaign.
 *                A partner may have at most one default campaign, which is enforced by a database constraint.
 * @param orgId The id of the organization that the campaign belongs to.
 * @param jornayaProfileId The AgentCloud ID of the Jornaya profile to use when auditing this campaign. There
 *                         are different profiles with different configurations for first-party and third-party leads.
 * @param providerEntityCode The Jornaya provider entity code to use when auditing leads for this campaign.
 *                           Generally unique to a campaign. This is optional for auditing but should be included
 *                           when possible.
 * @param rejectOnTcpaFailure When leads are attempted to be created under this campaign, if the TCPA audit fails, then we would
 *                            want to reject the lead instead of storing it in the system.
 * @param defaultTcpaStatus When defined, Jornaya auditing is circumvented entirely and the contents of this field are used as
 *      the TCPA status for all imported telephone numbers.
 * @param alwaysDedupe alwaysDedupe If true, any matching lead (by phone/email) is considered a duplicate, regardless of age.
 * @param dedupeDays The time window (in days) within which matching leads are considered duplicates.
 */
case class Campaign(
    id: Long,
    name: String,
    partnerId: Long,
    created: OffsetDateTime,
    requiresTcpa: Boolean,
    default: Boolean,
    orgId: Long,
    jornayaProfileId: Option[Long] = None,
    providerEntityCode: Option[String] = None,
    rejectOnTcpaFailure: Boolean = false,
    defaultTcpaStatus: Option[TCPAStatus] = None,
    alwaysDedupe: Boolean = true,
    dedupeDays: Option[Int] = None
)

object Campaign {
    implicit lazy val reads: Reads[Campaign] = (
        (__ \ "id").read[Long] and
        (__ \ "name").read[String] and
        (__ \ "partnerId").read[Long] and
        (__ \ "created").read[OffsetDateTime] and
        (__ \ "requiresTcpa").read[Boolean] and
        (__ \ "default").read[Boolean] and
        (__ \ "orgId").readWithDefault[Long](1L) and //
        (__ \ "jornayaProfileId").readNullable[Long] and
        (__ \ "providerEntityCode").readNullable[String] and
        (__ \ "rejectOnTcpaFailure").read[Boolean] and
        (__ \ "defaultTcpaStatus").readNullable[TCPAStatus] and
        (__ \ "alwaysDedupe").readWithDefault[Boolean](true) and
        (__ \ "dedupeDays").readNullable[Int]
    )(Campaign.apply _)
    implicit lazy val writes: Writes[Campaign] = Json.writes[Campaign]

    val parser: RowParser[Campaign] = {
        get[Long]("campaigns.id") ~
        get[String]("campaigns.name") ~
        get[Long]("campaigns.partner_id") ~
        get[OffsetDateTime]("campaigns.created") ~
        get[Boolean]("campaigns.requires_tcpa") ~
        get[Boolean]("campaigns.default_campaign") ~
        get[Long]("campaigns.organization_id") ~
        get[Option[Long]]("campaigns.jornaya_profile_id") ~
        get[Option[String]]("campaigns.provider_entity_code") ~
        get[Boolean]("campaigns.reject_on_tcpa_failure") ~
        get[Option[TCPAStatus]]("campaigns.default_tcpa_status_id") ~
        get[Option[Boolean]]("campaigns.alwaysDedupe").map(_.getOrElse(true)) ~
        get[Option[Int]]("campaigns.dedupeDays") map to(apply _)
    }
}

/** Input class for creating a new campaign. */
case class CampaignInput(
    name: String,
    partnerId: Long,
    requiresTcpa: Boolean,
    default: Boolean,
    orgId: Long,
    jornayaProfileId: Option[Long] = None,
    providerEntityCode: Option[String] = None,
    rejectOnTcpaFailure: Boolean = false,
    defaultTcpaStatus: Option[TCPAStatus] = None,
    alwaysDedupe: Boolean = true,
    dedupeDays: Option[Int]
)

object CampaignInput {
    implicit lazy val reads: Reads[CampaignInput] = Json.reads[CampaignInput]
    implicit lazy val writes: Writes[CampaignInput] = Json.writes[CampaignInput]

}

/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A full lead campaign object includes additional fields to be displayed the partner record page.
 *
 * @param name The unique, case-sensitive name of the campaign.
 * @param parentCampaign The unique, case-sensitive name of the parent campaign.
 * @param requiresTcpa Whether leads created under this campaign must have TCPA consent with a successful audit.
 *                     If this is true and the lead does not have TCPA consent or the audit fails,
 *                     then the lead is rejected. If it's false, then the lead may be dialed in the MTM domain.
 * @param default Whether this is the partner's default campaign. When leads are submitted to the application,
 *                the campaign may be specified explicitly. If a partner has a default campaign, then any leads
 *                submitted by that partner without an explicit campaign are associated with the default campaign.
 *                A partner may have at most one default campaign, which is enforced by a database constraint.
 * @param jornayaProfileId Jornaya is a third-party service which is hit to determine whether having the TCPA is valid.
 * @param rejectOnTcpaFailure Relates with the requiresTcpa and jornayaProfileId fields to determine whether a request should be rejected.
 * @param defaultTcpaStatus The default TCPA status for this campaign
 * @param srOutToPaid Sales Request Out to Paid, determines whether this campaign is counted for the application (for non-PR campaigns).
 * @param prOutToPaid Policy Review Out to Paid, determines whether this campaign is counted for the application (for PR campaigns).
 */

/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A full call campaign object includes additional fields to be displayed the partner record page.
 *
 * @param id represents the unique id of the table five9_campaigns.
 * @param campaign_name The name of the campaign.
 * @param campaignAllowableTime Represents the campaign allowable time.
 * @param twoMinuteThreshold Represents how many seconds must pass before a call is considered to be a 2 minute call.
 * @param dedupeDay Represents how long AccuQuote has for a call to be considered a duplicate and received in the same day,
 *                  and will not count towards the app.
 * @param dedupeWeek Represents how long AccuQuote has for a call to be considered a duplicate and received in the same week,
 *                  and will not count towards the app.
 * @param autoAnswerConnect Represents the automatic answer connect.
 * @param trueTimeBillableDuration The number of seconds it takes for AccuQuote to pay for the call.
 * @param agentBillableTime The number of seconds it takes for a call to count for an Agent.
 * @param orgId the organization's id.
 */
case class Five9CampaignInput(
    id: Long,
    campaignName: String,
    campaignAllowableTime: Long,
    twoMinuteThreshold: Long,
    dedupeDay: Option[Boolean],
    dedupeWeek: Option[Boolean],
    autoAnswerConnect: Option[Boolean],
    trueTimeBillableDuration: Long,
    agentBillableTime: Long,
    orgId: Long
)
object Five9CampaignInput {
    implicit lazy val reads: Reads[Five9CampaignInput] = Json.reads[Five9CampaignInput]
    implicit lazy val writes: Writes[Five9CampaignInput] = Json.writes[Five9CampaignInput]

    val parser: RowParser[Five9CampaignInput] = {
        get[Long]("five9_campaigns.id") ~
        get[String]("five9_campaigns.campaign_name") ~
        get[Long]("five9_campaigns.campaign_allowable_time") ~
        get[Long]("five9_campaigns.two_minute_threshold") ~
        get[Option[Boolean]]("five9_campaigns.dedupe_day") ~
        get[Option[Boolean]]("five9_campaigns.dedupe_week") ~
        get[Option[Boolean]]("five9_campaigns.automatic_answer_connect") ~
        get[Long]("five9_campaigns.true_time_billable_duration") ~
        get[Long]("five9_campaigns.agent_billable_time") ~
        get[Long]("five9_campaigns.organization_id") map to(apply _)
    }
}

/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A full call campaign object includes additional fields to be displayed the partner record page.
 *
 * @param id represents the unique id of the table campaign_v2v_desgination.
 * @param campaignName Represents the name of a campaign.
 * @param campaignId The unique ID of the campaign.
 * @param domain_id Represents the id of the domain.
 * @param domain_name Indicates whether a call campaign is associated with the Elagy1 or Elagy MTM domains.
 * @param sr_v2v_eligible Sales Request Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for non-PR campaigns).
 * @param pr_v2v_eligible Policy Review Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for PR campaigns).
 * @param five9_campaign_id represents the foriegn key relationship with five9_campaigns table.
 */

case class ReportingCallCampaignInput(
    id: Long,
    campaignName: String,
    campaignId: Option[String],
    domainId: String,
    domainName: Option[String],
    srV2VEligible: Option[Boolean],
    prV2VEligible: Option[Boolean],
    five9CampaignId: Long
)
object ReportingCallCampaignInput {
    implicit lazy val reads: Reads[ReportingCallCampaignInput] = Json.reads[ReportingCallCampaignInput]
    implicit lazy val writes: Writes[ReportingCallCampaignInput] = Json.writes[ReportingCallCampaignInput]

    val parser: RowParser[ReportingCallCampaignInput] = {
        get[Long]("campaign_v2v_designation.id_pkey") ~
        get[String]("campaign_v2v_designation.campaign_name") ~
        get[Option[String]]("campaign_v2v_designation.campaign_id") ~
        get[String]("campaign_v2v_designation.domain_id") ~
        get[Option[String]]("campaign_v2v_designation.domain_name") ~
        get[Option[Boolean]]("campaign_v2v_designation.sr_v2v_eligible") ~
        get[Option[Boolean]]("campaign_v2v_designation.pr_v2v_eligible") ~
        get[Long]("campaign_v2v_designation.five9_campaign_id") map to(apply _)
    }
}
case class FullLeadCampaign(
    id:Long,
    name: String,
    parentCampaign: Option[String],
    requiresTcpa: Boolean,
    default: Boolean,
    partnerId: Long,
    jornayaProfileId: Option[Long],
    rejectOnTcpaFailure: Boolean,
    defaultTcpaStatus: Option[TCPAStatus],
    srOutToPaid: Option[Boolean],
    prOutToPaid: Option[Boolean]
)

object FullLeadCampaign {
    implicit lazy val reads: Reads[FullLeadCampaign] = Json.reads[FullLeadCampaign]
    implicit lazy val writes: Writes[FullLeadCampaign] = Json.writes[FullLeadCampaign]

    val parser: RowParser[FullLeadCampaign] = {
        get[Long]("campaigns.id") ~
        get[String]("campaigns.name") ~
        get[Option[String]]("campaign_reconciliation.reconciled_name") ~
        get[Boolean]("campaigns.requires_tcpa") ~
        get[Boolean]("campaigns.default_campaign") ~
        get[Long]("campaigns.partner_id") ~
        get[Option[Long]]("campaigns.jornaya_profile_id") ~
        get[Boolean]("campaigns.reject_on_tcpa_failure") ~
        get[Option[TCPAStatus]]("campaigns.default_tcpa_status_id") ~
        get[Option[Boolean]]("campaign_out_to_paid_designation.sr_out_to_paid") ~
        get[Option[Boolean]]("campaign_out_to_paid_designation.pr_out_to_paid") map to(apply _)
    }
}


/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A  Lead campaign object includes  fields to be displayed the partner record page for edit operation.
 * @param id   The unique ID of the campaign, assigned by the application.
 * @param name The unique, case-sensitive name of the campaign.
 * @param partnerId The name of the partner to whom the campaign belongs.
 * @param requiresTcpa Whether leads created under this campaign must have TCPA consent with a successful audit.
 *                     If this is true and the lead does not have TCPA consent or the audit fails,
 *                     then the lead is rejected. If it's false, then the lead may be dialed in the MTM domain.
 * @param default Whether this is the partner's default campaign. When leads are submitted to the application,
 *                the campaign may be specified explicitly. If a partner has a default campaign, then any leads
 *                submitted by that partner without an explicit campaign are associated with the default campaign.
 *                A partner may have at most one default campaign, which is enforced by a database constraint.
 * @param orgId The id of the organization that the campaign belongs to.
 * @param jornayaProfileId The AgentCloud ID of the Jornaya profile to use when auditing this campaign. There
 *                         are different profiles with different configurations for first-party and third-party leads.
 * @param providerEntityCode The Jornaya provider entity code to use when auditing leads for this campaign.
 *                           Generally unique to a campaign. This is optional for auditing but should be included
 *                           when possible.
 * @param rejectOnTcpaFailure When leads are attempted to be created under this campaign, if the TCPA audit fails, then we would
 *                            want to reject the lead instead of storing it in the system.
 * @param defaultTcpaStatus  When defined, Jornaya auditing is circumvented entirely and the contents of this field are used as
 *                           the TCPA status for all imported telephone numbers.
 */
case class EditLeadCampaignInput(
    id:Long,
    name: String,
    partnerId: Long,
    requiresTcpa: Boolean,
    default: Boolean,
    orgId: Long,
    jornayaProfileId: Option[Long] = None,
    providerEntityCode: Option[String] = None,
    rejectOnTcpaFailure: Boolean = false,
    defaultTcpaStatus: Option[TCPAStatus] = None,
    alwaysDedupe: Boolean = true,
    dedupeDays: Option[Int]
)
object EditLeadCampaignInput {
    implicit lazy val reads: Reads[EditLeadCampaignInput] = Json.reads[EditLeadCampaignInput]
    implicit lazy val writes: Writes[EditLeadCampaignInput] = Json.writes[EditLeadCampaignInput]
}

/**
 *  A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 *  A full lead campaign object includes additional fields to be displayed the partner record page.
 *
 * @param campaignName The unique, case-sensitive name of the campaign.
 * @param parentCampaignName The unique, case-sensitive name of the parent-campaign.
 * @param campaignId The unique ID of the campaign.
 * @param orgId the organization's id.
 */
case class LeadCampaignParent(
        campaignName: String,
        parentCampaignName: String,
        campaignId: Long,
        orgId: Long,
        reconciled_name_id: Option[Long]
)
object LeadCampaignParent {
    implicit lazy val reads: Reads[LeadCampaignParent] = Json.reads[LeadCampaignParent]
    implicit lazy val writes: Writes[LeadCampaignParent] = Json.writes[LeadCampaignParent]

    val parser: RowParser[LeadCampaignParent] = {
            get[String]("campaign_reconciliation.campaign_name") ~
            get[String]("campaign_reconciliation.reconciled_name") ~
            get[Long]("campaign_reconciliation.campaign_name_id") ~
            get[Long]("campaign_reconciliation.organization_id") ~
            get[Option[Long]]("campaign_reconciliation.reconciled_name_id")  map to(apply _)
    }
}

/**
 *  A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 *  A full lead campaign object includes additional fields to be displayed the partner record page.
 *
 * @param srOutToPaid The name of the partner to whom the campaign belongs.
 * @param prOutToPaid When the campaign was created in the database.
 * @param campaignId The unique ID of the campaign.
 */
case class LeadCampaignDesignation(
      srOutToPaid: Boolean,
      prOutToPaid: Boolean,
      campaignId: Long
                                )
object LeadCampaignDesignation {
    implicit lazy val reads: Reads[LeadCampaignDesignation] = Json.reads[LeadCampaignDesignation]
    implicit lazy val writes: Writes[LeadCampaignDesignation] = Json.writes[LeadCampaignDesignation]

    val parser: RowParser[LeadCampaignDesignation] = {
        get[Boolean]("campaign_out_to_paid_designation.sr_out_to_paid") ~
        get[Boolean]("campaign_out_to_paid_designation.pr_out_to_paid") ~
        get[Long]("campaign_out_to_paid_designation.campaign_id") map to(apply _)

    }
}
/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A full call campaign object includes additional fields to be displayed the partner record page.
 *
 * @param name The unique, case-sensitive name of the campaign.
 * @param campaignId The unique ID of the campaign.
 * @param campaignAllowableTime Represents the campaign allowable time.
 * @param twoMinuteThreshold Represents how many seconds must pass before a call is considered to be a 2 minute call.
 * @param dedupeDay Represents how long AccuQuote has for a call to be considered a duplicate and received in the same day,
 *                  and will not count towards the app.
 * @param dedupeWeek Represents how long AccuQuote has for a call to be considered a duplicate and received in the same week,
 *                  and will not count towards the app.
 * @param autoAnswerConnect Represents the automatic answer connect.
 * @param trueTimeBillableDuration The number of seconds it takes for AccuQuote to pay for the call.
 * @param agentBillableTime The number of seconds it takes for a call to count for an Agent.
 * @param domain Indicates whether a call campaign is associated with the Elagy1 or Elagy MTM domains.
 * @param srV2vEligible Sales Request Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for non-PR campaigns).
 * @param prV2vEligible Policy Review Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for PR campaigns).
 */


case class FullCallCampaign(
    id: Long,
    name: String,
    campaignId: Option[String],
    campaignAllowableTime: Long,
    twoMinuteThreshold: Long,
    dedupeDay: Option[Boolean],
    dedupeWeek: Option[Boolean],
    autoAnswerConnect: Option[Boolean],
    trueTimeBillableDuration: Long,
    agentBillableTime: Long,
    domain: Option[String],
    srV2vEligible: Option[Boolean],
    prV2vEligible: Option[Boolean]
)


object FullCallCampaign {
    implicit lazy val reads: Reads[FullCallCampaign] = Json.reads[FullCallCampaign]
    implicit lazy val writes: Writes[FullCallCampaign] = Json.writes[FullCallCampaign]

    val parser: RowParser[FullCallCampaign] = {
        get[Long]("five9_campaigns.id") ~
        get[String]("five9_campaigns.campaign_name") ~
        get[Option[String]]("campaign_v2v_designation.campaign_id") ~
        get[Long]("five9_campaigns.campaign_allowable_time") ~
        get[Long]("five9_campaigns.two_minute_threshold") ~
        get[Option[Boolean]]("five9_campaigns.dedupe_day") ~
        get[Option[Boolean]]("five9_campaigns.dedupe_week") ~
        get[Option[Boolean]]("five9_campaigns.automatic_answer_connect") ~
        get[Long]("five9_campaigns.true_time_billable_duration") ~
        get[Long]("five9_campaigns.agent_billable_time") ~
        get[Option[String]]("campaign_v2v_designation.domain_name") ~
        get[Option[Boolean]]("campaign_v2v_designation.sr_v2v_eligible") ~
        get[Option[Boolean]]("campaign_v2v_designation.pr_v2v_eligible") map to(apply _)
    }
}

/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A full call campaign object includes additional fields to be displayed the partner record page.
 *
 * @param name The unique, case-sensitive name of the campaign.
 * @param campaignId The unique ID of the campaign.
 * @param campaignAllowableTime Represents the campaign allowable time.
 * @param twoMinuteThreshold Represents how many seconds must pass before a call is considered to be a 2 minute call.
 * @param dedupeDay Represents how long AccuQuote has for a call to be considered a duplicate and received in the same day,
 *                  and will not count towards the app.
 * @param dedupeWeek Represents how long AccuQuote has for a call to be considered a duplicate and received in the same week,
 *                  and will not count towards the app.
 * @param autoAnswerConnect Represents the automatic answer connect.
 * @param trueTimeBillableDuration The number of seconds it takes for AccuQuote to pay for the call.
 * @param agentBillableTime The number of seconds it takes for a call to count for an Agent.
 * @param domain Indicates whether a call campaign is associated with the Elagy1 or Elagy MTM domains.
 * @param srV2vEligible Sales Request Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for non-PR campaigns).
 * @param prV2vEligible Policy Review Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for PR campaigns).
 */

case class FullCallCampaignInput(
     name: String,
     campaignId: String,
     campaignAllowableTime: String,
     twoMinuteThreshold: String,
     dedupeDay: Option[Boolean],
     dedupeWeek: Option[Boolean],
     autoAnswerConnect: Option[Boolean],
     trueTimeBillableDuration: String,
     agentBillableTime: String,
     domain: String,
     srV2vEligible: Option[Boolean],
     prV2vEligible: Option[Boolean]
)


object FullCallCampaignInput {
    implicit lazy val reads: Reads[FullCallCampaignInput] = Json.reads[FullCallCampaignInput]
    implicit lazy val writes: Writes[FullCallCampaignInput] = Json.writes[FullCallCampaignInput]

    val parser: RowParser[FullCallCampaignInput] = {
        get[String]("five9_campaigns.campaign_name") ~
          get[String]("campaign_v2v_designation.campaign_id") ~
          get[String]("five9_campaigns.campaign_allowable_time") ~
          get[String]("five9_campaigns.two_minute_threshold") ~
          get[Option[Boolean]]("five9_campaigns.dedupe_day") ~
          get[Option[Boolean]]("five9_campaigns.dedupe_week") ~
          get[Option[Boolean]]("five9_campaigns.automatic_answer_connect") ~
          get[String]("five9_campaigns.true_time_billable_duration") ~
          get[String]("five9_campaigns.agent_billable_time") ~
          get[String]("campaign_v2v_designation.domain_name") ~
          get[Option[Boolean]]("campaign_v2v_designation.sr_v2v_eligible") ~
          get[Option[Boolean]]("campaign_v2v_designation.pr_v2v_eligible") map to(apply _)
    }
}

/**
 * A campaign represents an individual marketing effort by a partner. Each partner should have at least one campaign.
 * A full call campaign object includes additional fields to be displayed the partner record page.
 *
 * @param id The five9 campaign ID.
 * @param name The unique, case-sensitive name of the campaign.
 * @param campaignId The unique ID of the campaign.
 * @param campaignAllowableTime Represents the campaign allowable time.
 * @param twoMinuteThreshold Represents how many seconds must pass before a call is considered to be a 2 minute call.
 * @param dedupeDay Represents how long AccuQuote has for a call to be considered a duplicate and received in the same day,
 *                  and will not count towards the app.
 * @param dedupeWeek Represents how long AccuQuote has for a call to be considered a duplicate and received in the same week,
 *                  and will not count towards the app.
 * @param autoAnswerConnect Represents the automatic answer connect.
 * @param trueTimeBillableDuration The number of seconds it takes for AccuQuote to pay for the call.
 * @param agentBillableTime The number of seconds it takes for a call to count for an Agent.
 * @param domainId Indicates whether a call campaign is associated with the Elagy1 or Elagy MTM domains.
 * @param domainName The domain name associated with Campaign.
 * @param srV2vEligible Sales Request Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for non-PR campaigns).
 * @param prV2vEligible Policy Review Voice to Voice Eligible, determines whether the formula is used to
 *                      calculate the agent's pay (for PR campaigns).
 * @param partnerIdNewCall The Partner ID.
 */
case class EditCallCampaignInput(
     id: Long,
     name: String,
     campaignId: String,
     campaignAllowableTime: String,
     twoMinuteThreshold: String,
     dedupeDay: Option[Boolean],
     dedupeWeek: Option[Boolean],
     autoAnswerConnect: Option[Boolean],
     trueTimeBillableDuration: String,
     agentBillableTime: String,
     domainName: String,
     domainId: Long,
     srV2vEligible: Option[Boolean],
     prV2vEligible: Option[Boolean],
     partnerIdNewCall: Long)

object EditCallCampaignInput {
    implicit lazy val reads: Reads[EditCallCampaignInput] = Json.reads[EditCallCampaignInput]
    implicit lazy val writes: Writes[EditCallCampaignInput] = Json.writes[EditCallCampaignInput]
}

/**
 * Represents Campaign V2V details
 *
 * @param five9CampaignId The five9 campaign ID.
 * @param campaignId The unique ID of the campaign.
 * @param domainName The domain name associated to the campaign
 * @param domainId The domain id associated to the campaign.
 */
case class CampaignV2V(
    campaignName: String,
    five9CampaignId: Long,
    campaignId: String,
    domainName: String,
    domainId: String)

object CampaignV2V {
    implicit lazy val parser: RowParser[CampaignV2V] =
        get[String]("five9_campaigns.campaign_name") ~
        get[Long]("campaign_v2v_designation.five9_campaign_id") ~
        get[String]("campaign_v2v_designation.campaign_id") ~
        get[String]("campaign_v2v_designation.domain_name") ~
        get[String]("campaign_v2v_designation.domain_id") map to(CampaignV2V.apply _)
}

case class JornayaProfile(
    id: Long,
    name: String
)

object JornayaProfile {
    implicit lazy val parser: RowParser[JornayaProfile] =
        get[Long]("profiles.id") ~
        get[String]("profiles.name") map to(JornayaProfile.apply _)
}

@Singleton
class CampaignService @Inject() () {

    /** Reads a [[Campaign]] by ID, returning `F[None]` if not found. */
    def read[F[_]](id: Long)(implicit c: Connection, monad: JMonad[F]): F[Option[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""SELECT * FROM eap.campaigns WHERE id = $id""".as(Campaign.parser.singleOpt)
        }

    /** Reads a [[Campaign]] by name, returning `F[None]` if not found. */
    def read[F[_]](name: String, orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[Option[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""SELECT * FROM eap.campaigns WHERE name = $name AND organization_id = $orgId""".as(Campaign.parser.singleOpt)
        }

    /** Reads a [[LeadCampaignParent]] by campaign_id returning `F[None]` if not found. */
    def readLeadCampaignParent[F[_]](campaign_name_id: Long, orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[Option[LeadCampaignParent]] = {
        monad.tryCatchNonFatal {
            SQL"""
                SELECT c.campaign_name,
                c.reconciled_name,
                c.campaign_name_id,
                c.organization_id,
                c.reconciled_name_id
                FROM reporting.campaign_reconciliation c
                WHERE campaign_name_id = $campaign_name_id AND organization_id = $orgId
            """.as(LeadCampaignParent.parser.singleOpt)
        }
    }

    /** Reads a [[Campaign]] by name from dialer.five9_campaigns, returning `F[None]` if not found. */
    def readFive9[F[_]](name: String, domainName: Long)(implicit c: Connection, monad: JMonad[F]): F[Option[Five9CampaignInput]] =
        monad.tryCatchNonFatal {
            SQL"""SELECT c.id,
                  c.campaign_name,
                  c.campaign_allowable_time,
                  c.two_minute_threshold,
                  c.dedupe_day,
                  c.dedupe_week,
                  c.automatic_answer_connect,
                  c.true_time_billable_duration,
                  c.agent_billable_time,
                  c.organization_id,
                  c.domain_id
              FROM dialer.five9_campaigns c inner join dialer.domains d on d.id = c.domain_id WHERE c.campaign_name = $name
                 and d.id = $domainName
             """
              .as(Five9CampaignInput.parser.singleOpt)
        }

    /** Reads a [[ReportingCallCampaignInput]] by id from reporting.campaign_v2v_designation, returning `F[None]` if not found. */
    def readCampaignV2V[F[_]](name: String, domainName: Long)(implicit c: Connection, monad: JMonad[F]): F[Option[ReportingCallCampaignInput]] =
        monad.tryCatchNonFatal {
            SQL"""SELECT c.id_pkey,
                    c.campaign_name,
                    c.campaign_id,
                    c.domain_id,
                    c.domain_name,
                    c.sr_v2v_eligible,
                    c.pr_v2v_eligible,
                    c.five9_campaign_id
                  FROM reporting.campaign_v2v_designation c inner join dialer.domains d on d.id = CAST(c.domain_id AS INT) WHERE c.campaign_id = $name
                    and d.id = $domainName
                 """
              .as(ReportingCallCampaignInput.parser.singleOpt)
        }

    /** Lists all campaigns in alphabetical order. */
    def list[F[_]](orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[List[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""SELECT * FROM eap.campaigns WHERE organization_id = $orgId ORDER BY name""".as(Campaign.parser.*)
        }

    /** Lists all campaigns belonging to multiple [[Partner]] models. Results are in alphabetical order.
     *
     *  NOTE: The DummyImplicit is there in order to avoid type erasure between NonEmptyList[Partner] and NonEmptyList[Long].
     */
    def list[F[_]](partners: NonEmptyList[Partner], orgId: Long)
        (implicit c: Connection, monad: JMonad[F], dummy1: DummyImplicit): F[List[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT *
                FROM eap.campaigns
                WHERE partner_id IN (${partners.map(_.id).toList})
                AND organization_id = $orgId
                ORDER BY name
            """
                .as(Campaign.parser.*)
        }

    /** Lists all campaigns belonging to a [[Partner]]. Results are in alphabetical order. */
    def list[F[_]](partner: Partner, orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[List[Campaign]] =
        list(NonEmptyList.one(partner), orgId)

    /** Lists of campaigns in alphabetical order for certain ids.
     *
     *  @param ids A non-empty list of campaign ids.
     */
    def list[F[_]](ids: NonEmptyList[Long], orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[List[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT *
                FROM eap.campaigns
                WHERE id IN (${ids.toList})
                AND organization_id = $orgId
                ORDER BY name, id
            """.as(Campaign.parser.*)
        }

    /** Lists of campaigns in alphabetical order for certain ids.
     *
     *  @param names A non-empty list of campaign names.
     */
    def listByName[F[_]](names: NonEmptyList[String], orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[List[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT *
                FROM eap.campaigns
                WHERE name IN (${names.toList})
                AND organization_id = $orgId
                ORDER BY name, id
            """
                .as(Campaign.parser.*)
        }

    /** Reads the default [[Campaign]] for a [[Partner]] from the database. */
    def getDefault[F[_]](partner: Partner, orgId: Long)(implicit c: Connection, monad: JMonad[F]): F[Option[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT *
                FROM eap.campaigns
                WHERE partner_id = ${partner.id}
                AND default_campaign
                AND organization_id = $orgId
            """
                .as(Campaign.parser.singleOpt)
        }

    /**
     * Reads the default [[Campaign]] for an HTTP basic auth `ApiUser`.
     *
     * To find the default campaign, the query first looks up the API user's default [[Partner]]. If one is found,
     * the default partner's default campaign is then retrieved.
     */
    def getDefault[F[_]](user: ApiUser)(implicit c: Connection, monad: JMonad[F]): F[Option[Campaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT c.*
                FROM eap.partners_api_users pau
                JOIN eap.campaigns c ON (c.partner_id = pau.partner_id)
                WHERE pau.api_user_id = ${user.id}
                AND pau.default_partner
                AND c.default_campaign
                AND organization_id = ${user.orgId}
            """.as(Campaign.parser.singleOpt)
        }

    /**
     * Determines whether an API user should have access to a particular [[Campaign]] (in other words, whether the campaign's
     * partner is associated with the API user).
     */
    def hasAccess[F[_]](user: ApiUser, campaign: Campaign)(implicit c: Connection, monad: JMonad[F]): F[Boolean] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT EXISTS (
                    SELECT c.*
                    FROM eap.partners_api_users pau
                    JOIN eap.campaigns c ON (c.partner_id = pau.partner_id)
                    WHERE pau.api_user_id = ${user.id}
                    AND c.id = ${campaign.id}
                    AND c.organization_id = ${user.orgId}
                )
            """.as(scalar[Boolean].single)
        }

    /** Creates a new campaign based on the given input. */
    def create[F[_]](input: CampaignInput, orgId: Long)(implicit t: Transaction, monad: JMonad[F]): F[Campaign] = monad.tryCatchNonFatal {
        SQL"""
            INSERT INTO eap.campaigns (
                name,
                partner_id,
                requires_tcpa,
                default_campaign,
                jornaya_profile_id,
                provider_entity_code,
                reject_on_tcpa_failure,
                default_tcpa_status_id,
                organization_id,
                alwaysDedupe,
                dedupeDays
            ) VALUES (
                ${input.name},
                ${input.partnerId},
                ${input.requiresTcpa},
                ${input.default},
                ${input.jornayaProfileId},
                ${input.providerEntityCode},
                ${input.rejectOnTcpaFailure},
                ${input.defaultTcpaStatus.map(_.id)},
                ${orgId},
                ${input.alwaysDedupe},
                ${input.dedupeDays}
            ) RETURNING *
        """.as(Campaign.parser.single)
    }

    /** Creates a new row with campaign_name and parent_name based on the given input. */
    def createParentCampaign[F[_]](parentCampaignName: String, campaignName: String, orgId: Long, campaignId: Long, reconciled_name_id: Option[Long])(implicit t: Transaction, monad: JMonad[F]): F[LeadCampaignParent] = monad.tryCatchNonFatal {
        SQL"""
            INSERT INTO reporting.campaign_reconciliation (
                reconciled_name,
                campaign_name,
                organization_id,
                campaign_name_id,
                reconciled_name_id
            ) VALUES (
                ${parentCampaignName},
                ${campaignName},
                ${orgId},
                ${campaignId},
                ${reconciled_name_id}
            ) RETURNING reconciled_name, campaign_name, organization_id, campaign_name_id, reconciled_name_id
        """.as(LeadCampaignParent.parser.single)
    }

    /** Update row with campaign_name and parent_name based on the given input. */
    def updateParentCampaign[F[_]](parentCampaignName: String, campaignName: String, orgId: Long, campaignId: Long, reconciled_name_id: Option[Long])(implicit t: Transaction, monad: JMonad[F]): F[LeadCampaignParent] = monad.tryCatchNonFatal {
        SQL"""
            UPDATE reporting.campaign_reconciliation SET
            reconciled_name=${parentCampaignName},
            organization_id=${orgId},
            campaign_name=${campaignName},
            reconciled_name_id=${reconciled_name_id}
            WHERE campaign_name_id=${campaignId}
            RETURNING reconciled_name, organization_id, campaign_name_id,campaign_name, reconciled_name_id
        """.as(LeadCampaignParent.parser.single)
    }

    /** update reconcile name of all children as campaign name has been modified */
    def updateReconcileName[F[_]](reconcileName: String, campaignId: Long)(implicit t: Transaction, monad: JMonad[F]): F[LeadCampaignParent] = monad.tryCatchNonFatal {
        SQL"""
            UPDATE reporting.campaign_reconciliation SET
            reconciled_name=${reconcileName}
            WHERE reconciled_name_id=${campaignId}
            RETURNING reconciled_name, organization_id, campaign_name_id,campaign_name, reconciled_name_id
        """.as(LeadCampaignParent.parser.single)
    }

    /**  Delete the row from reporting.campaign_reconciliation table  */
    def removeParentCampaign[F[_]](campaignId: Long, orgId: Long)(implicit t: Transaction, monad: JMonad[F]): F[Unit] = {
        SQL"""
            DELETE from reporting.campaign_reconciliation
            WHERE campaign_name_id=${campaignId} and organization_id=${orgId}
        """.executeSingleUpdate()
    }

    /** Updates the sr_out_to_paid and pr_out_to_paid for a new campaign based on the given input. */
    def updateDesignation[F[_]](srOutToPaid: Boolean, prOutToPaid: Boolean, campaignId: Long)(implicit t: Transaction, monad: JMonad[F]): F[LeadCampaignDesignation] = monad.tryCatchNonFatal {
        SQL"""
            UPDATE reporting.campaign_out_to_paid_designation
            SET sr_out_to_paid=${srOutToPaid}, pr_out_to_paid=${prOutToPaid}
            WHERE campaign_id = ${campaignId} RETURNING sr_out_to_paid, pr_out_to_paid, campaign_id
        """.as(LeadCampaignDesignation.parser.single)
    }

    /** Checks that the user can access the campaign. */
    def canViewEdit[F[_]](campaign: Campaign, userOrgId: Long)(implicit monad: JMonad[F]): F[PermissionLevel] =
        if (campaign.orgId === userOrgId) monad.pure(PermissionLevel.Edit)
        else monad.pure(PermissionLevel.NoView)

    /** Checks that the user can access the campaign. */
    def canViewEdit[F[_]](campaign: Campaign, account: Account)(implicit monad: JMonad[F]): F[PermissionLevel] =
        canViewEdit(campaign, account.orgId)

    /** Lists all full lead campaigns belonging to multiple partner ids. Results are in alphabetical order.
     *
     */
    def listFullLeadCampaigns[F[_]](partnerIds: List[Long], orgId: Long)
                  (implicit c: Connection, monad: JMonad[F]): F[List[FullLeadCampaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT
                    c.id,
                    c.name,
                    cr.reconciled_name,
                    c.requires_tcpa,
                    c.default_campaign,
                    c.partner_id,
                    c.jornaya_profile_id,
                    c.reject_on_tcpa_failure,
                    c.default_tcpa_status_id,
                    cotpd.sr_out_to_paid,
                    cotpd.pr_out_to_paid
                FROM eap.campaigns c
                LEFT JOIN reporting.campaign_out_to_paid_designation cotpd ON c.id = cotpd.campaign_id
                LEFT JOIN reporting.campaign_reconciliation cr on c.id = cr.campaign_name_id
                WHERE c.partner_id IN ($partnerIds)
                AND c.organization_id = $orgId
                ORDER BY c.name
            """
                .as(FullLeadCampaign.parser.*)
        }

    /** Edit update campaign based on the given input. */
    def updateLeadCampaign[F[_]](input: EditLeadCampaignInput, orgId: Long)(implicit t: Transaction, monad: JMonad[F]): F[Campaign] = monad.tryCatchNonFatal {
        SQL"""
           UPDATE eap.campaigns SET
               name=${input.name},
               partner_id=${input.partnerId},
               requires_tcpa=${input.requiresTcpa},
               default_campaign=${input.default},
               jornaya_profile_id=${input.jornayaProfileId},
               provider_entity_code=${input.providerEntityCode},
               reject_on_tcpa_failure=${input.rejectOnTcpaFailure},
               default_tcpa_status_id=${input.defaultTcpaStatus.map(_.id)},
               organization_id=${orgId},
               alwaysDedupe=${input.alwaysDedupe},
               dedupeDays=${input.dedupeDays}
               WHERE id =${input.id}
                RETURNING *
          """.as(Campaign.parser.single)
    }

    /** Lists all full call campaigns belonging to multiple partner ids. Results are in alphabetical order.
     *
     */
    def listFullCallCampaigns[F[_]](partnerIds: List[Long], orgId: Long)
                                   (implicit c: Connection, monad: JMonad[F]): F[List[FullCallCampaign]] =
        monad.tryCatchNonFatal {
            SQL"""
                SELECT
                    c.id,
                    c.campaign_name,
                    v2v.campaign_id,
                    c.campaign_allowable_time,
                    c.two_minute_threshold,
                    c.dedupe_day,
                    c.dedupe_week,
                    c.automatic_answer_connect,
                    c.true_time_billable_duration,
                    c.agent_billable_time,
                    v2v.domain_name,
                    v2v.sr_v2v_eligible,
                    v2v.pr_v2v_eligible
                FROM dialer.five9_campaigns c
                LEFT JOIN reporting.campaign_v2v_designation v2v ON c.id = v2v.five9_campaign_id
                WHERE c.partner_id IN ($partnerIds)
                AND c.organization_id = $orgId
                ORDER BY c.campaign_name
            """
                .as(FullCallCampaign.parser.*)
        }

    /** Creates a new campaign in dialer.five9_campaigns table.
     *
     */
    def createFive9[F[_]](input: FullCallCampaignInput, orgId: Long, partnerId: Long, domainName: Long)(implicit t: Transaction, monad: JMonad[F]): F[Five9CampaignInput] =
        monad.tryCatchNonFatal{
            SQL"""
              INSERT INTO dialer.five9_campaigns(
                    campaign_name,
                    campaign_allowable_time,
                    two_minute_threshold,
                    dedupe_day,
                    dedupe_week,
                    automatic_answer_connect,
                    true_time_billable_duration,
                    agent_billable_time,
                    organization_id,
                    domain_id,
                    partner_id
                ) VALUES (
                    ${input.name},
                    CASE WHEN ${input.campaignAllowableTime} = '' THEN null ELSE CAST(${input.campaignAllowableTime} AS INT) END,
                    CASE WHEN ${input.twoMinuteThreshold} = '' THEN 120 ELSE CAST(${input.twoMinuteThreshold} AS INT) END,
                    ${input.dedupeDay},
                    ${input.dedupeWeek},
                    ${input.autoAnswerConnect},
                    CASE WHEN ${input.trueTimeBillableDuration} = '' THEN 30 ELSE CAST(${input.trueTimeBillableDuration} AS INT) END,
                    CASE WHEN ${input.agentBillableTime} = '' THEN 15 ELSE CAST(${input.agentBillableTime} AS INT) END,
                    $orgId,
                    ${domainName},
                    $partnerId )
              RETURNING id,
                    campaign_name,
                    campaign_allowable_time,
                    two_minute_threshold,
                    dedupe_day,
                    dedupe_week,
                    automatic_answer_connect,
                    true_time_billable_duration,
                    agent_billable_time,
                    organization_id
            """.as(Five9CampaignInput.parser.single)
        }

    /** updates the five9 campaign values.
     *
     */
    def updateFive9[F[_]](input: EditCallCampaignInput, orgId: Long)(implicit t: Transaction, monad: JMonad[F]): F[Five9CampaignInput] =
        monad.tryCatchNonFatal {
            SQL"""
                UPDATE dialer.five9_campaigns SET
                    campaign_name=${input.name},
                    campaign_allowable_time=CASE WHEN ${input.campaignAllowableTime} = '' THEN null ELSE CAST(${input.campaignAllowableTime} AS INT) END,
                    two_minute_threshold=CASE WHEN ${input.twoMinuteThreshold} = '' THEN 120 ELSE CAST(${input.twoMinuteThreshold} AS INT) END,
                    dedupe_day=${input.dedupeDay},
                    dedupe_week=${input.dedupeWeek},
                    automatic_answer_connect=${input.autoAnswerConnect},
                    true_time_billable_duration=CASE WHEN ${input.trueTimeBillableDuration} = '' THEN 30 ELSE CAST(${input.trueTimeBillableDuration} AS INT) END,
                    agent_billable_time=CASE WHEN ${input.agentBillableTime} = '' THEN 15 ELSE CAST(${input.agentBillableTime} AS INT) END,
                    organization_id=$orgId,
                    domain_id=${input.domainId},
                    partner_id=${input.partnerIdNewCall}
                    WHERE id =${input.id}
                RETURNING id,
                    campaign_name,
                    campaign_allowable_time,
                    two_minute_threshold,
                    dedupe_day,
                    dedupe_week,
                    automatic_answer_connect,
                    true_time_billable_duration,
                    agent_billable_time,
                    organization_id
        """.as(Five9CampaignInput.parser.single)
    }

    /** Creates a new campaign in reporting.campaign_v2v_designation table.
     *
     */
    def createCampaignV2V[F[_]](input: FullCallCampaignInput, id: Long, domainName: String)(implicit t: Transaction, monad: JMonad[F]): F[ReportingCallCampaignInput] =
        monad.tryCatchNonFatal {
            SQL"""
              INSERT INTO reporting.campaign_v2v_designation(
                campaign_name,
                campaign_id,
                domain_id,
                domain_name,
                sr_v2v_eligible,
                pr_v2v_eligible,
                five9_campaign_id
            ) VALUES (
                ${input.name},
                ${input.campaignId},
                ${domainName},
                ${input.domain} ,
                ${input.srV2vEligible},
                ${input.prV2vEligible},
                $id )
              RETURNING id_pkey,
                campaign_name,
                campaign_id,
                domain_id,
                domain_name,
                sr_v2v_eligible,
                pr_v2v_eligible,
                five9_campaign_id
            """.as(ReportingCallCampaignInput.parser.single)
        }

    /** Updates campaign in reporting.campaign_v2v_designation table.
     *
     */
    def updateCampaignV2V[F[_]](input: EditCallCampaignInput)(implicit t: Transaction, monad: JMonad[F]): F[ReportingCallCampaignInput] =
        monad.tryCatchNonFatal {
          SQL"""
          UPDATE reporting.campaign_v2v_designation SET
            campaign_name=${input.name},
            campaign_id=${input.campaignId},
            domain_id=${input.domainId},
            domain_name=${input.domainName},
            sr_v2v_eligible=${input.srV2vEligible},
            pr_v2v_eligible=${input.prV2vEligible}
            WHERE five9_campaign_id=${input.id}
          RETURNING id_pkey,
            campaign_name,
            campaign_id,
            domain_id,
            domain_name,
            sr_v2v_eligible,
            pr_v2v_eligible,
            five9_campaign_id
        """.as(ReportingCallCampaignInput.parser.single)
    }

    /** Retrievs the five9 campaign campaign id and domain details from
    *   the reporting.campaign_v2v_designation table.
    */
    def listCampaignV2V[F[_]]()(implicit c: Connection, monad: JMonad[F]): F[List[CampaignV2V]] =
        monad.tryCatchNonFatal {
            SQL"""
                 SELECT
                    f.campaign_name,
                    c.five9_campaign_id,
                    c.campaign_id,
                    c.domain_name,
                    c.domain_id
                 FROM reporting.campaign_v2v_designation c
                 JOIN dialer.five9_campaigns f ON c.five9_campaign_id = f.id
               """.as(CampaignV2V.parser.*)
        }

    def listJornayaProfiles[F[_]]()(implicit c: Connection, monad: JMonad[F]): F[List[JornayaProfile]] =
        monad.tryCatchNonFatal {
            SQL"""
                 SELECT
                    p.id,
                    p.name
                 FROM jornaya.profiles p
                 where name != 'healthcare.com'
               """.as(JornayaProfile.parser.*)
        }
}
