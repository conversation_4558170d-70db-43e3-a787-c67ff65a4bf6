// scalastyle:off file.size.limit
package com.elagy.models

import anorm._
import cats.data.NonEmptyList
import cats.implicits._
import com.elagy.integrations.neverbounce.ValidationResult
import com.elagy.tcpa.Flag
import com.elagy.test.MockClientConfiguration
import com.jaroop.blocks.authentication.models.Account
import com.jaroop.core.datatypes.{ EmailAddress, IpAddress, PhoneNumber, State }
import com.jaroop.core.errors.JResult
import com.jaroop.core.libs.sql.DatabaseOps._
import com.jaroop.core.models.patch._
import com.jaroop.play.test._
import com.elagy.util.AnormImplicits._
import java.sql.Connection
import java.time._
import org.specs2.mock._
import org.specs2.mutable._
import play.api.Application
import play.api.db.Database
import play.api.libs.json._
import play.api.test.WithApplication
import scala.util.Try
import squants.market.MoneyConversions._
import squants.market._
import squants.mass.MassConversions._
import squants.space.LengthConversions._

class LeadSpec extends Specification with <PERSON>cki<PERSON> {

    class WithLeads extends WithSQL("test/resources/models/TestLeads.sql")

    val clientConfig = mock[ConfigProvider]
    clientConfig.config returns MockClientConfiguration.config.copy(allowNewPRLeadColumns = true)

    def contactService(implicit app: Application) = component[ContactService]
    def leadContactService(implicit app: Application) = component[LeadContactService]
    def db(implicit app: Application) = component[Database]
    def phoneEntryService(implicit app: Application) = component[PhoneEntryService]
    def emailEntryService(implicit app: Application) = component[EmailEntryService]
    def taskService(implicit app: Application) = component[TaskService]
    def opportunityService(implicit app: Application) = component[OpportunityService]
    def opportunityDetailsService(implicit app: Application) = component[OpportunityDetailsService]
    def leadDetailsService(implicit app: Application) = component[LeadDetailsService]
    def mortgageDetailsService(implicit app: Application) = component[MortgageDetailsService]
    def leadService(implicit app: Application) = new LeadService(
        campaignService = component[CampaignService],
        cohortService = component[CohortService],
        contactService = contactService,
        phoneEntryService = phoneEntryService,
        emailEntryService = emailEntryService,
        leadDetailsService = leadDetailsService,
        leadPhoneSynchronizer = component[LeadPhoneSynchronizer],
        leadEmailSynchronizer = component[LeadEmailSynchronizer],
        mortgageDetailsService = mortgageDetailsService,
        opportunityService = opportunityService,
        opportunityDetailsService = opportunityDetailsService,
        partnerService = component[PartnerService],
        relationshipService = component[RelationshipService],
        taskService = taskService,
        clientConfig = clientConfig
    )

    val testLeadOfferData = OfferLead(
        leadId = 1010L,
        rapportId = Some("test_rapport_id_1"),
        customerPriority = Some(5),
        offerText = Some("What an offer!"),
        offerExpiryDate = Some(LocalDate.of(1992, 6, 15)),
        offerCode = Some("fakeOffer123")
    )

    val testLeadOfferNullData = OfferLead(
        leadId = 1004L,
        rapportId = None,
        customerPriority = None,
        offerText = None,
        offerExpiryDate = None,
        offerCode = None
    )

    val testLead = Lead.empty.copy(
        id = Some(1001L),
        status = LeadStatus.Converted,
        contact = LeadContact.empty.copy(
            id = Some(1001L),
            created = OffsetDateTime.of(2018, 2, 1, 0, 0, 0, 0, ZoneOffset.UTC),
            firstName = Some("Marcos"),
            middleName = None,
            lastName = Some("Ramos"),
            birthday = Some(LocalDate.of(1992, 6, 15)),
            street = Some("33 State Street"),
            street2 = Some(""),
            city = Some("Berlin"),
            state = Some(State.Connecticut),
            zip = Some("06037"),
            phones = List(
                PhoneEntry.empty.copy(
                    id = Some(1000L),
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    phoneNumber = PhoneNumber.unsafe("2234567890"),
                    doNotContact = false,
                    tcpaStatus = TCPAStatus.Yes,
                    phoneType = PhoneType.Cell,
                    orgId = 1L
                ),
                PhoneEntry.empty.copy(
                    id = Some(1003L),
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    phoneNumber = PhoneNumber.unsafe("2222222222"),
                    doNotContact = false,
                    tcpaStatus = TCPAStatus.Yes,
                    phoneType = PhoneType.Home,
                    orgId = 1L
                )
            ),
            emails = List(
                EmailEntry(
                    id = Some(1003L),
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    email = EmailAddress.unsafe("<EMAIL>"),
                    doNotContact = false,
                    orgId = 1L
                )
            ),
            primaryPhoneId = Some(1003L),
            primaryEmailId = Some(1003L),
            gender = Some(Gender.Male)
        ),
        product = Some(ProductType.FinalExpense),
        agentId = Some(1001L),
        notes = Some("Always has demands"),
        campaignId = None,
        clientRef = Some("lead_2"),
        clientIp = Some(IpAddress.unsafe("***********")),
        referringUrl = Some("someurl.com"),
        tcpaToken = Some("yes"),
        tcpaFlag = Some(Flag.Red),
        tcpaRejectionReason = Some("There was a problem with the TCPA"),
        leadDetails = LeadDetails(
            leadId = Some(1001L),
            termLength = Some(15),
            tobacco = Some(true),
            riskClass = Some(RiskClass.Standard),
            optInDateTime = Some(OffsetDateTime.of(2018, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)),
            faceAmount = Some(100000.USD),
            height = Some(72.inches),
            weight = Some(190.pounds),
            usCitizen = Some(true),
            familyStatus = Some(FamilyStatus.Single),
            incomeMonthly = Some(80000.USD),
            borrowerId = Some("51")
        ),
        mortgageDetails = Some(MortgageDetails(
            id = Some(1001L),
            borrowerStatus = BorrowerStatus.Inactive,
            loanNumber = "example_51",
            mortgageCloseDate = LocalDate.of(2040, 4, 1),
            insurancePurpose = "Mortgage protection",
            faceAmount = 200000.USD,
            termLength = 30,
            loanOfficerFirstName = "Kyle",
            loanOfficerLastName = "Marsden",
            loanOfficerPhone = Some(PhoneNumber.unsafe("2035558888")),
            loanOfficerEmail = Some(EmailAddress.unsafe("<EMAIL>")),
            processorFirstName = "Lauren",
            processorLastName = "Gambino",
            processorPhone = Some(PhoneNumber.unsafe("2035558888")),
            processorEmail = Some(EmailAddress.unsafe("<EMAIL>")),
            tpoFirm = None
        )),
        apiUserId = Some(50000L),
        s3UserId = Some(1L),
        orgId = 1L,
    )

    def expectedWithNewPhoneEmail(leadId: Long) = testLead.copy(
        contact = testLead.contact.copy(
            phones = List(
                PhoneEntry.empty.copy(
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    phoneNumber = PhoneNumber.unsafe("8609999999"),
                    doNotContact = false,
                    orgId = 1L
                )
            ),
            emails = List(
                EmailEntry(
                    id = None,
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    email = EmailAddress.unsafe("<EMAIL>"),
                    doNotContact = false,
                    orgId = 1L
                )
            )
        )
    )

    def retrievePhoneEntryIds(leadId: Long)(implicit app: Application): List[Long] = db.withConnection(implicit c =>
        SQL"SELECT phone_entry_id FROM eap.lead_phone_entries WHERE lead_id = $leadId ORDER BY phone_entry_id"
            .as(SqlParser.long("phone_entry_id").*)
    )

    def retrieveEmailEntryIds(leadId: Long)(implicit app: Application): List[Long] = db.withConnection(implicit c =>
        SQL"SELECT email_entry_id FROM eap.lead_email_entries WHERE lead_id = $leadId ORDER BY email_entry_id"
            .as(SqlParser.long("email_entry_id").*)
    )

    def retrieveLeadDetails(leadId: Long)(implicit app: Application): Option[Long] = db.withConnection(implicit c =>
        SQL"SELECT lead_id FROM eap.lead_details WHERE lead_id = $leadId".as(SqlParser.long("lead_id").singleOpt)
    )

    "The Lead model" should {
        "patcher" >> {
            "patch a lead's status" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "status", Json.toJson(LeadStatus.NotInterested))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(status = LeadStatus.NotInterested)
                )
            }

            "patch a lead contact's first name" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:firstName", Json.toJson("Kimberly"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(firstName = Some("Kimberly")))
                )
            }

            "patch a lead contact's last name" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:lastName", Json.toJson("Williams"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(lastName = Some("Williams")))
                )
            }

            "patch a lead contact's birthday" in new WithLeads {
                val request = PatchRequest(
                    testLead.id.get, "contact:birthday", Json.toJson(LocalDate.of(1986, 4, 3))
                )

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(birthday = Some(LocalDate.of(1986, 4, 3))))
                )
            }

            "patch a lead contact's street" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:street", Json.toJson("14 Main Street"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(street = Some("14 Main Street")))
                )
            }

            "patch a lead contact's street2" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:street2", Json.toJson("Suite 4"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(street2 = Some("Suite 4")))
                )
            }

            "patch a lead contact's city" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:city", Json.toJson("Rutland"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(city = Some("Rutland")))
                )
            }

            "patch a lead contact's state" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:state", Json.toJson(State.Vermont))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(state = Some(State.Vermont)))
                )
            }

            "patch a lead contact's zip" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:zip", Json.toJson("05701"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(zip = Some("05701")))
                )
            }

            "patch a lead's notes" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "notes", Json.toJson("Don't call his wife"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(notes = Some("Don't call his wife"))
                )
            }

            "patch the lead's primary phone id" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "primaryPhoneId", Json.toJson(1000L))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(primaryPhoneId = Some(1000L)))
                )
            }

            "patch the lead's gender" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:gender", Json.toJson(Gender.Female))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(gender = Some(Gender.Female)))
                )
            }

            "patch the lead's middle name" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "contact:middleName", Json.toJson("Anna"))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(contact = testLead.contact.copy(middleName = Some("Anna")))
                )
            }

            "patch the lead's campaign id" in new WithLeads {
                val request = PatchRequest(testLead.id.get, "campaign", Json.toJson(10000))

                component[PatchService].patch[Lead](request) must beRight

                database.withConnection(implicit c => leadService.read[JResult](testLead.id.get)) must beRight(
                    testLead.copy(campaignId = Some(10000))
                )
            }
        }

        tag("read")
        "successfully read the lead" in new WithLeads {
            val result = database.withConnection(implicit c => leadService.read[JResult](1001L))
            result.map(r => r.copy(contact = r.contact.copy(created = testLead.contact.created))) must beRight(testLead)
        }

        tag("read")
        "successfully read the lead offer data" in new WithLeads {
            val result = database.withConnection(implicit c => leadService.readLeadWithOfferData[JResult](1010L))
            result must beRight(testLeadOfferData)
        }

        tag("read")
        "return empty values when the lead does not have offer data" in new WithLeads {
            val result = database.withConnection(implicit c => leadService.readLeadWithOfferData[JResult](1004L))
            result must beRight(testLeadOfferNullData)
        }

        tag("read")
        "give an error message when reading a non existing lead" in new WithLeads {
            val result = database.withConnection(implicit c => leadService.readLeadWithOfferData[JResult](1011L))
            result match {
                case Left(error) => error.message mustEqual "Could not find lead 1011"
                case Right(_) => failure("Expected an error but got a successful result.")
            }
        }

        tag("read")
        "successfully read a lead with no product type" in new WithLeads {
            val result = database.withConnection(implicit c => leadService.read[JResult](1004L))
            result.map(_.product) must beRight[Option[ProductType]](None)
        }

        tag("list")
        "read a list of leads" in new WithLeads {
            database.withConnection { implicit c =>
                val result = leadService.list[JResult](NonEmptyList.of(1000L, 1002L, 1003L), 1L)
                result.map(_.flatMap(_.id)) must beRight(List(1000L, 1002L, 1003L))
            }
        }

        tag("listByStatus")
        "read a list of leads by status" in new WithLeads {
            val activeResult = database.syncTransaction(implicit c =>
                leadService.listByStatus[JResult](LeadStatus.Active, 1L)
            )
            activeResult.map(_.flatMap(_.id)) must beRight(List(1000L, 1004L, 1005L, 1007L))

            val convertedResult = database.syncTransaction(implicit c =>
                leadService.listByStatus[JResult](LeadStatus.Converted, 1L))
            convertedResult.map(_.flatMap(_.id)) must beRight(List(1001L, 1003L))
        }

        tag("mapFromClientRef")
        "read a map of lead client references to the id in AgentCloud" in new WithLeads {
            database.withConnection { implicit c =>
                val Right(result) = leadService.mapFromClientRef[Either[Throwable, *]](
                    NonEmptyList.of("lead_1", "lead_2")
                )
                result must beEqualTo(Map(
                    "lead_1" -> 1000L,
                    "lead_2" -> 1001L
                ))
            }
        }

        tag("mapFromClientRef")
        "fail to read a map of lead client reference to lead id if the client references do not refer to leads" in new WithLeads {
            database.withConnection { implicit c =>
                val Right(result) = leadService.mapFromClientRef[Either[Throwable, *]](
                    NonEmptyList.of("asdf", "qwer")
                )
                result must beEqualTo(Map())
            }
        }

        "create" >> {
            def getCreated(id: Long)(implicit c: Connection) =
                SQL"""
                        SELECT created
                        FROM eap.leads
                        WHERE id = $id
                        ORDER BY id DESC
                    """.as(SqlParser.scalar[OffsetDateTime].single)

            tag("create")
            "successfully create a lead with new phone and email entries plus a new mortgage details" in new WithLeads {
                val newLead = expectedWithNewPhoneEmail(1011L)
                val result = database.syncTransaction(implicit c => leadService.create(newLead.copy(
                    mortgageDetails = Some(newLead.mortgageDetails.get.copy(id = None, loanNumber = "12345678"))
                )))
                result.map(_.id) must beRight(Some(1011L))
                result.map(_.contact.phones.flatMap(_.id)) must beRight(List(1007L))
                result.map(_.contact.emails.flatMap(_.id)) must beRight(List(1007L))
                // Should create a new mortgage details entry
                result.map(_.mortgageDetails.flatMap(_.id)) must beRight(Some(1002L))
                result.map(_.apiUserId) must beRight(Some(50000L))

                retrievePhoneEntryIds(1011L) must equalTo(List(1007L))
                retrieveEmailEntryIds(1011L) must equalTo(List(1007L))

                result.map(_.contact.primaryPhoneId) must beRight(Some(1007L))
                result.map(_.contact.primaryEmailId) must beRight(Some(1007L))

                val leadDetailsResult = database.withConnection(implicit c => leadService.read[JResult](1011L))
                    .map(_.leadDetails)
                leadDetailsResult must beRight(testLead.leadDetails.copy(leadId = Some(1011L)))

                database.withConnection(implicit c => mortgageDetailsService.read[JResult](1002L)) must beRight(
                    testLead.mortgageDetails.get.withId(1002L).copy(loanNumber = "12345678")
                )
            }

            tag("create")
            "successfully create a lead at a specific time and place" in new WithLeads {
                val newLead = expectedWithNewPhoneEmail(1011L)
                val customTimestamp = OffsetDateTime.of(2021, 12, 7, 12, 0, 0, 0, ZoneOffset.UTC)
                val result = database.syncTransaction(implicit c => leadService.create(newLead.copy(
                    mortgageDetails = Some(newLead.mortgageDetails.get.copy(id = None, loanNumber = "12345678"))
                ), customTimestamp))
                result.map(_.id) must beRight(Some(1011L))

                database.withConnection(implicit c => getCreated(1011L)) must beEqualTo(customTimestamp)
            }

            tag("create")
            "successfully create a lead with existing phone and email entries plus an existing mortgage" in new WithLeads {
                val newLead = expectedWithNewPhoneEmail(1011L)
                val existingUpdatedPhone = PhoneEntry.empty.copy(
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    phoneNumber = PhoneNumber.unsafe("4567890123"),
                    doNotContact = true,
                    orgId = 1L
                )
                val existingEmail = EmailEntry(
                    id = Some(1000L),
                    created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                    email = EmailAddress.unsafe("<EMAIL>"),
                    doNotContact = true,
                    validationResult = Option(ValidationResult.Invalid),
                    orgId = 1L
                )
                val mortgage = testLead.mortgageDetails.get.copy(faceAmount = USD(1000000))
                val result = database.syncTransaction { implicit c =>
                    leadService.create(newLead.copy(
                        contact = newLead.contact.copy(
                            phones = List(existingUpdatedPhone),
                            emails = List(
                                existingEmail,
                                existingEmail.copy(
                                    email = EmailAddress.unsafe("<EMAIL>"),
                                    doNotContact = false,
                                    validationResult = Option(ValidationResult.Valid)
                                )
                            )
                        ),
                        mortgageDetails = Some(mortgage)
                    ))
                }
                result.map(_.id) must beRight(Some(1011L))
                result.map(_.contact.phones.flatMap(_.id)) must beRight(List(1001L))
                result.map(_.contact.emails.flatMap(_.id)) must beRight(List(1005L, 1001L))
                // Should update and existing mortgage details entry
                result.map(_.mortgageDetails.flatMap(_.id)) must beRight(Some(1001L))

                // Check that some existing phones and emails fields were not updated. Other fields such as
                // TCPA status for phones and `doNotContact` for emails should be updated if there is new information.
                db.withConnection { implicit c =>
                    val Right(updatedPhone) = phoneEntryService.readByPhone(PhoneNumber.unsafe("4567890123"), 1L)
                    updatedPhone.doNotContact must beEqualTo(false)
                    updatedPhone.tcpaStatus must beEqualTo(TCPAStatus.Blank)

                    val Right(existingEmail) = emailEntryService.read(EmailAddress.unsafe("<EMAIL>"), 1L)
                    existingEmail.doNotContact must beEqualTo(true)
                    existingEmail.validationResult must beSome[ValidationResult](ValidationResult.Invalid)

                    val Right(existingEmail2) = emailEntryService.read(EmailAddress.unsafe("<EMAIL>"), 1L)
                    existingEmail2.doNotContact must beEqualTo(true)
                }

                retrievePhoneEntryIds(1011L) must equalTo(List(1001L))
                retrieveEmailEntryIds(1011L) must equalTo(List(1001L, 1005L))

                database.withConnection { implicit c =>
                    import anorm._, SqlParser._ // scalastyle:ignore

                    mortgageDetailsService.read[JResult](1001L) must beRight(mortgage.withId(1001L))

                    SQL"""SELECT COUNT(*) FROM eap.mortgage_details""".as(scalar[Int].single) must beEqualTo(1)
                }
            }

            tag("create")
            "successfully create a lead without phones or emails" in new WithLeads {
                val newLead = expectedWithNewPhoneEmail(1011L)
                val result = database.syncTransaction { implicit c =>
                    leadService.create(newLead.copy(
                        contact = newLead.contact.copy(phones = Nil, emails = Nil)
                    ))
                }
                result.map(_.id) must beRight(Some(1011L))
                result.map(_.contact.phones.flatMap(_.id)) must beRight[List[Long]](Nil)
                result.map(_.contact.emails.flatMap(_.id)) must beRight[List[Long]](Nil)

                retrievePhoneEntryIds(1011L) must equalTo(Nil)
                retrieveEmailEntryIds(1011L) must equalTo(Nil)
            }

            tag("create")
            "successfully create a lead with no product type" in new WithLeads {
                val newLead = expectedWithNewPhoneEmail(1011L)
                val result = database.syncTransaction { implicit c =>
                    leadService.create(newLead.copy(
                        contact = newLead.contact.copy(phones = Nil, emails = Nil),
                        product = None
                    ))
                }

                result.map(_.id) must beRight(Some(1011L))
                result.map(_.product) must beRight[Option[ProductType]](None)
            }

            tag("create")
            "fail to create a lead when the agent id does not exist" in new WithLeads {
                val result = database.syncTransaction { implicit c =>
                    leadService.create(testLead.copy(agentId = Some(10000L)))
                }

                result must beLeft
            }

            tag("create")
            "fail to create a lead when the state does not exist" in new WithLeads {
                val result = database.syncTransaction { implicit c =>
                    leadService.create(
                        testLead.copy(contact = testLead.contact.copy(state = Some(State(10000L, "Bad", "BA"))))
                    )
                }

                result must beLeft
            }
        }

        "update" >> {
            tag("update")
            "successfully update the lead" in new WithLeads {
                val result = database.syncTransaction { implicit c =>
                    // Overwrite the API user ID and S3 user ID
                    val lead = expectedWithNewPhoneEmail(1001L)
                        .copy(apiUserId = None, s3UserId = None)
                        .withId(1001L)
                    for {
                        _ <- leadService.update(lead)
                        updated <- leadService.read[JResult](lead.id.get)
                    } yield updated
                }
                result.map(_.agentId) must beRight(Some(1001L))
                // This list should include the primary phone ID as well.
                result.map(_.contact.phones.flatMap(_.id)) must beRight(List(1003L, 1007L))
                // This list should include the primary email ID as well.
                result.map(_.contact.emails.flatMap(_.id)) must beRight(List(1003L, 1007L))

                // The API user ID should not have been overwritten
                result.map(_.apiUserId) must beRight(Some(50000L))

                // Both lists should include the primary phone and primary email
                retrievePhoneEntryIds(1001L) must equalTo(List(1003L, 1007L))
                retrieveEmailEntryIds(1001L) must equalTo(List(1003L, 1007L))

                result.map(_.leadDetails) must beRight(testLead.leadDetails.copy(leadId = Some(1001L)))
            }

            tag("update")
            "do not update the primary phone number and email address for a lead if they have not changed" in new WithLeads {
                val leadUpdate = testLead.copy(
                    contact = testLead.contact.copy(
                        phones = List(
                            PhoneEntry.empty.copy(
                                created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                                updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                                // This is the same phone number as primary phone already associated with the lead
                                phoneNumber = PhoneNumber.unsafe("2222222222"),
                                doNotContact = false,
                                orgId = 1L
                            )
                        ),
                        emails = List(
                            EmailEntry(
                                id = None,
                                created = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                                updated = OffsetDateTime.of(2018, 5, 1, 0, 0, 0, 0, ZoneOffset.UTC),
                                // This is the same email address as the primary email already associated with the lead
                                email = EmailAddress.unsafe("<EMAIL>"),
                                doNotContact = false,
                                orgId = 1L
                            )
                        )
                    )
                ).withId(1001L)

                val result = database.syncTransaction(implicit c => leadService.update(leadUpdate))

                result.map(_.agentId) must beRight(Some(1001L))
                // This list should only have 1003L, as that was the existing primary phone id and it has not been changed
                result.map(_.contact.phones.flatMap(_.id)) must beRight(List(1003L))
                // This list should only have 1003L, as that was the existing primary email id and it has not been changed
                result.map(_.contact.emails.flatMap(_.id)) must beRight(List(1003L))

                // Both lists should include the primary phone and primary email
                retrievePhoneEntryIds(1001L) must equalTo(List(1003L))
                retrieveEmailEntryIds(1001L) must equalTo(List(1003L))

                val leadDetailsResult = database.withConnection(implicit c => leadService.read[JResult](1001L))
                    .map(_.leadDetails)
                leadDetailsResult must beRight(testLead.leadDetails.copy(leadId = Some(1001L)))
            }

            tag("update")
            "fail to update the lead when the lead does not exist" in new WithLeads {
                val result = database.syncTransaction(implicit c => leadService.update(testLead.withId(10003L)))
                result must beLeft
            }

            tag("update")
            "fail to update the lead when the agent id does not exist" in new WithLeads {
                val result = database.syncTransaction { implicit c =>
                    leadService.update(testLead.copy(agentId = Some(10000L)))
                }

                result must beLeft
            }

            tag("update")
            "fail to update the lead when the state does not exist" in new WithLeads {
                val result = database.syncTransaction { implicit c =>
                    leadService.update(
                        testLead.copy(contact = testLead.contact.copy(state = Some(State(10000L, "Bad", "BA"))))
                    )
                }

                result must beLeft
            }

            tag("updateOfferData")
            "update the lead offer data when the lead exist" in new WithLeads {
                val result = database.syncTransaction(implicit c => leadService.updateLeadWithOfferData[JResult](List(OfferLead(
                    leadId = 1000L,
                    rapportId = Some("test_rapport_id_1"),
                    customerPriority = Some(5),
                    offerText = Some("What an offer!"),
                    offerExpiryDate = Some(LocalDate.of(1992, 6, 15)),
                    offerCode = Some("fakeOffer123")
                ))))
                result must beRight()
            }

            tag("updateDialerList")
            "successfully update the dialer list ID for a list of leads" in new WithLeads {
                val listToUpdate = List(
                    Lead.empty.copy(id = Option(1000L), dialerListId = Option(2L)),
                    Lead.empty.copy(id = Option(1001L), dialerListId = Option(2L)),
                    Lead.empty.copy(id = Option(1002L), dialerListId = Option(1L)),
                    Lead.empty.copy(id = Option(1003L), dialerListId = Option(1L))
                )

                val result = database.syncTransaction { implicit t =>
                    for {
                        _ <- leadService.updateDialerList[JResult](listToUpdate)
                        updatedLeads <- leadService.list[JResult](NonEmptyList.of(1000L, 1001L, 1002L, 1003L), 1L)
                    } yield updatedLeads
                }

                result must beRight.like { case leads =>
                    leads.flatMap(_.id) must contain(exactly(1000L, 1001L, 1002L, 1003L).inOrder)
                    leads.flatMap(_.dialerListId) must contain(exactly(2L, 2L, 1L, 1L).inOrder)
                }
            }

            tag("setAAALeadsReadyToDial")
            "successfully update active and unreachable leads with offer text and customer priority not null" in new WithLeads {
                val count = 2L
                val rapportIds = NonEmptyList.of("test_rapport_id_2", "test_rapport_id_1") ++ (1 to 100000)
                    .foldLeft(List.empty[String])((sum, ele) => sum :+ s"test_rap_${ele}")

                val result = database.syncTransaction { implicit t =>
                    for {
                        count <- leadService.setAAALeadsReadyToDial[JResult](rapportIds)
                    } yield count.size.toLong
                }

                result must beRight(2L)
            }

            tag("setAAALeadActive")
            "successfully reset the AAA PR leads back to active" in new WithLeads {
                val Right((before, updated, after)) = database.syncTransaction { implicit t =>
                    for {
                        rtdleadsBeforeUpdate <- leadService.listByStatus[JResult](LeadStatus.ReadyToDial, Organization.AccuQuote.id)
                        updated <- leadService.setAAALeadActive[JResult]()
                        // Get remaining Ready-to-Dial leads.
                        rtdleadsAfterUpdate <- leadService.listByStatus[JResult](LeadStatus.ReadyToDial, Organization.AccuQuote.id)
                    } yield (rtdleadsBeforeUpdate, updated, rtdleadsAfterUpdate)
                }

                before.size must beEqualTo(4L)
                before.map(_.id) must beEqualTo(List(Some(1002L), Some(1008L), Some(1009L), Some(1010L)))
                updated.size must beEqualTo(2)
                after.size must beEqualTo(2L)
                after.map(_.id) must beEqualTo(List(Some(1002L), Some(1008L)))
            }
        }

        "reassign" >> {
            val agent = Account.empty.withId(1002L).copy(orgId = 1L)

            tag("reassign")
            "successfully reassign multiple leads along with each of its old agent's tasks to a new agent" in new WithLeads {
                val result = db.syncTransaction { implicit c => leadService.reassign[Either[Throwable, *]](
                        NonEmptyList.of(1001L, 1002L), agent, 1L, Some(1003L)
                )}
                result must beRight(2L)

                // Check that the open tasks of Lead 1001 that were assigned previously assigned to the lead's old agent (1001)
                // have been re-assigned to the new agent (1002).
                val Right(taskResults) = db.withConnection {
                    implicit c => taskService.listFull[Lead, LeadTask](Lead.empty.withId(1001L), 1L)
                }
                taskResults.flatMap(_.id) must equalTo(List(5001L, 5000L, 5002L))
                taskResults.flatMap(_.assignee.flatMap(_.id)) must equalTo(List(1001L, 1000L, 1002L))
            }

            tag("reassign")
            "successfully reassign multiple leads to a new agent but not count any leads that do not exist" in new WithLeads {
                val result = database.syncTransaction { implicit t => leadService.reassign[Either[Throwable, *]](
                        NonEmptyList.of(1001L, 20000L), agent, 1L, Some(1003L)
                )}
                result must beRight(1L)
            }

            tag("reassign")
            "fail to reassign multiple leads to an agent that does not exist" in new WithLeads {
                val result = database.syncTransaction { implicit t => leadService.reassign[Either[Throwable, *]](
                        NonEmptyList.of(1001L, 1002L), Account.empty.withId(20000L), 1L, Some(1003L)
                )}
                result must beLeft
            }
        }

        "convert" >> {
            tag("convert")
            "convert a lead to a new opportunity without making a contact" in new WithLeads {
                val Right(NonEmptyList(result, related)) = database.syncTransaction { implicit c =>
                    leadService.convert(id = 1000L, agentId = 1001L)
                }

                result.id must beSome(1000L)
                result.product must equalTo(ProductType.FinalExpense)
                result.agentId must equalTo(1001L)
                result.leadId must beSome(1000L)
                result.insuredId must beNone
                result.ownerId must beNone
                result.payorId must beNone

                val fromDb = database.withConnection(implicit c => leadService.read[JResult](testLead.id.get))
                fromDb.map(_.status) must beRight[LeadStatus](LeadStatus.Converted)

                related must beEqualTo(Nil)
            }

            tag("convert")
            "convert a lead to a new opportunity with a new contact" in new WithLeads {
                val now = OffsetDateTime.now()
                val Right(NonEmptyList(result, _)) = database.syncTransaction { implicit c =>
                    leadService.convert(
                        id = 1000L,
                        agentId = 1000L,
                        createContact = true
                    )
                }

                result.id must beSome(1000L)
                result.product must equalTo(ProductType.FinalExpense)
                result.agentId must equalTo(1000L)
                result.leadId must beSome(1000L)
                result.insuredId must beSome(1000L)
                result.ownerId must beSome(1000L)
                result.payorId must beSome(1000L)
                result.mortgageId must beNone

                val fromDb = database.withConnection(implicit c => leadService.read[JResult](testLead.id.get))
                fromDb.map(_.status) must beRight[LeadStatus](LeadStatus.Converted)

                val Right(contact) = database.withConnection(implicit c => component[ContactService].read(1000L))
                contact.id must beSome(1000L)
                contact.status must beEqualTo(ContactStatus.Active)
                contact.agentId must beEqualTo(1000L)
                contact.firstName must beSome("Jay")
                contact.middleName must beSome("Test")
                contact.lastName must beSome("Morrow")
                contact.birthday must beSome(LocalDate.of(1991, 1, 5))
                contact.homeAddress.street must beSome("23 State Street")
                contact.homeAddress.street2 must beSome("")
                contact.homeAddress.city must beSome("Hartford")
                contact.homeAddress.state must beSome(State.Connecticut)
                contact.homeAddress.zip must beSome("06101")
                contact.notes must beSome("")
                contact.phones.map(_.copy(created = now, updated = now)) must beEqualTo(List(
                    PhoneEntry.empty.copy(
                        id = Some(1000L),
                        created = now,
                        updated = now,
                        phoneNumber = PhoneNumber.unsafe("2234567890"),
                        doNotContact = false,
                        tcpaStatus = TCPAStatus.Yes,
                        phoneType = PhoneType.Cell,
                        orgId = 1L
                    ),
                    PhoneEntry.empty.copy(
                        id = Some(1002L),
                        created = now,
                        updated = now,
                        phoneNumber = PhoneNumber.unsafe("5555555555"),
                        doNotContact = false,
                        tcpaStatus = TCPAStatus.Yes,
                        phoneType = PhoneType.Cell,
                        orgId = 1L
                    )
                ))
                contact.emails.map(_.copy(created = now, updated = now)) must beEqualTo(List(
                    EmailEntry(
                        id = Some(1000L),
                        created = now,
                        updated = now,
                        email = EmailAddress.unsafe("<EMAIL>"),
                        doNotContact = false,
                        orgId = 1L
                    ),
                    EmailEntry(
                        id = Some(1001L),
                        created = now,
                        updated = now,
                        email = EmailAddress.unsafe("<EMAIL>"),
                        doNotContact = false,
                        orgId = 1L
                    )
                ))
                contact.gender must beSome[Gender](Gender.Male)
                contact.primaryPhoneId must beSome(1002L)
                contact.primaryEmailId must beSome(1000L)
                contact.contactType must beEqualTo(ContactType.AccuQuote)
            }

            tag("convert")
            "convert a lead and a companion to new opportunities and contacts with relationships" in new WithLeads {
                val Right(NonEmptyList(_, related)) = database.syncTransaction { implicit c =>
                    leadService.convert(
                        id = 1002L,
                        agentId = 1001L,
                        createContact = true
                    )
                }

                val Right(relatedLeads) = database.withConnection { implicit c =>
                    leadService.list[JResult](NonEmptyList(1002, List(1001L)), 1L)
                }

                related.flatMap(_.id) must beEqualTo(List(1001L))
                database.withConnection(implicit c => contactService.listRelated(Contact.empty.withId(1000L).copy(orgId = 1), 1L))
                    .map(_.flatMap(_.id)) must beRight(List(1001L))
                relatedLeads.map(_.status must beEqualTo(LeadStatus.Converted))
            }

            tag("convert")
            "fail to convert a lead to a new opportunity when the lead does not exist" in new WithLeads {
                val Left(result) = database.syncTransaction { implicit c =>
                    leadService.convert(id = 10001L, agentId = 1000L)
                }
                result.message must equalTo("Could not find lead 10001")
            }

            tag("convert")
            "copy the lead's details to the opportunity's details" in new WithLeads {
                val Right(NonEmptyList(opp, _)) =
                    database.syncTransaction(implicit c => leadService.convert(id = 1000L, agentId = 1001L))
                val Right(opportunityDetails) =
                    database.withConnection(implicit c => opportunityDetailsService.read[JResult](opp))
                val Right(leadDetails) =
                    database.withConnection(implicit c => leadDetailsService.read[JResult](1000L))

                opportunityDetails.riskClass must beEqualTo(leadDetails.riskClass)
                opportunityDetails.familyStatus must beEqualTo(leadDetails.familyStatus)
                opportunityDetails.optInDateTime must beEqualTo(leadDetails.optInDateTime)
                opportunityDetails.monthlyIncome must beEqualTo(leadDetails.incomeMonthly)
                opportunityDetails.height must beEqualTo(leadDetails.height)
                opportunityDetails.weight must beEqualTo(leadDetails.weight)
                opportunityDetails.termLength must beEqualTo(leadDetails.termLength)
                opportunityDetails.coverageAmount must beEqualTo(leadDetails.faceAmount)
                opportunityDetails.borrowerId must beEqualTo(leadDetails.borrowerId)
            }

            tag("convert")
            "fail to convert a Lead that has been already been converted" in new WithLeads {
                val Left(result) = database.syncTransaction(implicit c => leadService.convert(id = 1003L, agentId = 1001L))
                result.message must beEqualTo("The lead has already been converted.")
            }
        }

        "convertPRLead" >> {
            tag("convertPRLead")
            "convert a PR lead" in new WithLeads {
                val result = database.syncTransaction { implicit t =>
                    for {
                        opportunity <- opportunityService.read[JResult](999L)
                        prLead <- leadService.convertPRLead(opportunity)
                    } yield prLead
                }

                result must beRight[Option[(Lead, Lead)]].like { case Some((oldPrLead, convertedPrLead)) =>
                    oldPrLead.id must beSome(1004L)
                    oldPrLead.status must beEqualTo(LeadStatus.Active)
                    convertedPrLead.id must beSome(1004L)
                    convertedPrLead.status must beEqualTo(LeadStatus.Converted)
                }
            }

            tag("convertPRLead")
            "convert a PR lead whose status is 'Not Interested'" in new WithLeads {
                val result = database.syncTransaction { implicit t =>
                    for {
                        opportunity <- opportunityService.read[JResult](997L)
                        prLead <- leadService.convertPRLead(opportunity)
                    } yield prLead
                }

                result must beRight[Option[(Lead, Lead)]].like { case Some((oldPrLead, convertedPrLead)) =>
                    oldPrLead.id must beSome(1003L)
                    oldPrLead.status must beEqualTo(LeadStatus.Converted)
                    convertedPrLead.id must beSome(1003L)
                    convertedPrLead.status must beEqualTo(LeadStatus.Converted)
                }
            }

            tag("convertPRLead")
            "do nothing if there is no PR lead to convert" in new WithLeads {
                val result = database.syncTransaction { implicit t =>
                    for {
                        opportunity <- opportunityService.read[JResult](998L)
                        prLead <- leadService.convertPRLead(opportunity)
                    } yield prLead
                }

                result must beRight[Option[(Lead, Lead)]](None)
            }
        }

        "addPhones" >> {
            tag("addPhones")
            "add new phone entry relationships to the lead" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.addPhones(List(testLead.contact.phones(0)), 1002)
                } must beRight
                retrievePhoneEntryIds(1002L) must equalTo(List(1000L, 1004L))
            }

            tag("addPhones")
            "fail to add new phone entry relationships to a lead that does not exist" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.addPhones(List(testLead.contact.phones(0)), 10000)
                } must beLeft
            }
        }

        "deletePhone" >> {
            tag("deletePhone")
            "deletes a lead-phone relationship between a lead and phone entry" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.deletePhone[JResult](1000, 1000)
                } must beRight(true)
            }

            tag("deletePhone")
            "fail to delete a lead-phone relationship when the relationship does not exist" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.deletePhone[JResult](1001, 1002)
                } must beLeft
            }
        }

        "addEmails" >> {
            tag("addEmails")
            "add new phone entry relationships to the lead" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.addEmails(List(testLead.contact.emails(0)), 1002)
                } must beRight
                retrieveEmailEntryIds(1002L) must equalTo(List(1003L, 1004L))
            }

            tag("addEmails")
            "fail to add new phone entry relationships to a lead that does not exist" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.addEmails(List(testLead.contact.emails(0)), 10000)
                } must beLeft
            }
        }

        "deleteEmail" >> {
            tag("deleteEmail")
            "deletes a lead-email relationship between a lead and email entry" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.deleteEmail[JResult](1000, 1000)
                } must beRight(true)
            }

            tag("deleteEmail")
            "fail to delete a lead-email relationship when the relationship does not exist" in new WithLeads {
                database.syncTransaction { implicit c =>
                    leadService.deleteEmail[JResult](1001, 1002)
                } must beLeft
            }
        }

        "isDuplicate" >> {
            def defaultPhones = NonEmptyList.of(PhoneNumber.unsafe("2035551234"))
            def defaultEmails = NonEmptyList.of(EmailAddress.unsafe("<EMAIL>"))

            def phoneEntries(phones: List[PhoneNumber]) = phones.map(p => PhoneEntry.empty.copy(phoneNumber = p, orgId = 1L))
            def emailEntries(emails: List[EmailAddress]) = emails.map(e =>
                EmailEntry(id = None, email = e, doNotContact = false, orgId = 1L)
            )

            def phoneEntryService(list: List[PhoneEntry]): PhoneEntryService = {
                val svc = mock[PhoneEntryService]
                svc.list(any[NonEmptyList[PhoneNumber]], any[Long])(any[Connection]) returns Right(list)
                svc
            }

            def emailEntryService(list: List[EmailEntry]): EmailEntryService = {
                val svc = mock[EmailEntryService]
                svc.list(any[NonEmptyList[EmailAddress]], any[Long])(any[Connection]) returns Right(list)
                svc
            }

            def leadService(phoneEntryService: PhoneEntryService, emailEntryService: EmailEntryService) = new LeadService(
                campaignService = mock[CampaignService],
                cohortService = mock[CohortService],
                contactService = mock[ContactService],
                phoneEntryService = phoneEntryService,
                emailEntryService = emailEntryService,
                leadDetailsService = mock[LeadDetailsService],
                leadPhoneSynchronizer = mock[LeadPhoneSynchronizer],
                leadEmailSynchronizer = mock[LeadEmailSynchronizer],
                mortgageDetailsService = mock[MortgageDetailsService],
                opportunityService = mock[OpportunityService],
                opportunityDetailsService = mock[OpportunityDetailsService],
                partnerService = mock[PartnerService],
                relationshipService = mock[RelationshipService],
                taskService = mock[TaskService],
                clientConfig = clientConfig
            )

            def fakeLead(phones: List[PhoneEntry], emails: List[EmailEntry]) = Lead.empty.copy(
                contact = LeadContact.empty.copy(
                    phones = phones,
                    emails = emails
                )
            )

            tag("isDuplicate")
            "return true if a lead has phones and emails and the phone numbers and email addresses exist" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = phoneEntryService(phones),
                    emailEntryService = emailEntryService(emails)
                ).isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "return true if a lead has phones and emails and the phone numbers exist" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = phoneEntryService(phones),
                    emailEntryService = emailEntryService(List.empty[EmailEntry])
                ).isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "return true if a lead has phones and emails and the email addresses exist" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = phoneEntryService(List.empty[PhoneEntry]),
                    emailEntryService = emailEntryService(emails)
                ).isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "return false if a lead has phones and emails and none exist" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = phoneEntryService(List.empty[PhoneEntry]),
                    emailEntryService = emailEntryService(List.empty[EmailEntry])
                ).isDuplicate(lead) must beRight(false)
            }

            tag("isDuplicate")
            "return true if a lead has phones but no emails and the phone numbers exist" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = List.empty[EmailEntry]

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = phoneEntryService(phones), 
                    emailEntryService = mock[EmailEntryService] // shouldn't even be called
                ).isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "return false if a lead has phones but no emails and the phone numbers do not exist" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = List.empty[EmailEntry]

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = phoneEntryService(List.empty[PhoneEntry]),
                    emailEntryService = mock[EmailEntryService] // shouldn't even be called
                ).isDuplicate(lead) must beRight(false)
            }

            tag("isDuplicate")
            "return true if a lead has emails but no phones and the email addresses exist" in {
                implicit val connection = mock[Connection]
                val phones = List.empty[PhoneEntry]
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = mock[PhoneEntryService], // shouldn't even be called
                    emailEntryService = emailEntryService(emails)
                ).isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "return false if a lead has emails but no phones and the email addresses do not exist" in {
                implicit val connection = mock[Connection]
                val phones = List.empty[PhoneEntry]
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = mock[PhoneEntryService], // shouldn't even be called
                    emailEntryService = emailEntryService(List.empty[EmailEntry])
                ).isDuplicate(lead) must beRight(false)
            }

            tag("isDuplicate")
            "return false if a lead has no emails and no phones" in {
                implicit val connection = mock[Connection]
                val phones = List.empty[PhoneEntry]
                val emails = List.empty[EmailEntry]

                val lead = fakeLead(phones, emails)

                leadService(
                    phoneEntryService = mock[PhoneEntryService], // shouldn't even be called
                    emailEntryService = mock[EmailEntryService] // shouldn't even be called
                ).isDuplicate(lead) must beRight(false)
            }

            def campaignWithAlwaysDedupe = {
                val campaign = mock[Campaign]
                campaign.alwaysDedupe returns true
                campaign
            }

            def campaignWithNoDedupeConfig = {
                val campaign = mock[Campaign]
                campaign.alwaysDedupe returns false
                campaign.dedupeDays returns None
                campaign
            }

            def campaignWithZeroDedupeDays = {
                val campaign = mock[Campaign] 
                campaign.alwaysDedupe returns false
                campaign.dedupeDays returns Some(0)
                campaign
            }

            def campaignWithDedupeDays(days: Int) = {
                val campaign = mock[Campaign]
                campaign.alwaysDedupe returns false
                campaign.dedupeDays returns Some(days)
                campaign
            }

            def mockCampaignService(campaign: Campaign) = {
                val svc = mock[CampaignService]
                svc.read[JResult](any[Long])(any[Connection], any) returns Right(Some(campaign))
                svc
            }

            tag("isDuplicate")
            "Case A: alwaysDedupe = true - reject any lead with matching phone/email regardless of age" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails).copy(campaignId = Some(999L))

                val mockCampaignSvc = mock[CampaignService]
                val mockCampaign = mock[Campaign]
                mockCampaign.alwaysDedupe returns true
                mockCampaignSvc.read[JResult](any[Long])(any[Connection], any) returns Right(Some(mockCampaign))
                
                val customService = new LeadService(
                    campaignService = mockCampaignSvc,
                    cohortService = mock[CohortService],
                    contactService = mock[ContactService],
                    phoneEntryService = phoneEntryService(phones),
                    emailEntryService = emailEntryService(emails),
                    leadDetailsService = mock[LeadDetailsService],
                    leadPhoneSynchronizer = mock[LeadPhoneSynchronizer],
                    leadEmailSynchronizer = mock[LeadEmailSynchronizer],
                    mortgageDetailsService = mock[MortgageDetailsService],
                    opportunityService = mock[OpportunityService],
                    opportunityDetailsService = mock[OpportunityDetailsService],
                    partnerService = mock[PartnerService],
                    relationshipService = mock[RelationshipService],
                    taskService = mock[TaskService],
                    clientConfig = clientConfig
                )

                customService.isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "Case B: alwaysDedupe = false, dedupeDays = NULL - fallback to strict deduplication" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails).copy(campaignId = Some(999L))

                val mockCampaignSvc = mock[CampaignService]
                val mockCampaign = mock[Campaign]
                mockCampaign.alwaysDedupe returns false
                mockCampaign.dedupeDays returns None
                mockCampaignSvc.read[JResult](any[Long])(any[Connection], any) returns Right(Some(mockCampaign))
                
                val customService = new LeadService(
                    campaignService = mockCampaignSvc,
                    cohortService = mock[CohortService],
                    contactService = mock[ContactService],
                    phoneEntryService = phoneEntryService(phones),
                    emailEntryService = emailEntryService(emails),
                    leadDetailsService = mock[LeadDetailsService],
                    leadPhoneSynchronizer = mock[LeadPhoneSynchronizer],
                    leadEmailSynchronizer = mock[LeadEmailSynchronizer],
                    mortgageDetailsService = mock[MortgageDetailsService],
                    opportunityService = mock[OpportunityService],
                    opportunityDetailsService = mock[OpportunityDetailsService],
                    partnerService = mock[PartnerService],
                    relationshipService = mock[RelationshipService],
                    taskService = mock[TaskService],
                    clientConfig = clientConfig
                )

                customService.isDuplicate(lead) must beRight(true)
            }

            tag("isDuplicate")
            "Case C: alwaysDedupe = false, dedupeDays = 0 - do not reject any lead" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails).copy(campaignId = Some(999L))

                val mockCampaignSvc = mock[CampaignService]
                val mockCampaign = mock[Campaign]
                mockCampaign.alwaysDedupe returns false
                mockCampaign.dedupeDays returns Some(0)
                mockCampaignSvc.read[JResult](any[Long])(any[Connection], any) returns Right(Some(mockCampaign))
                
                val customService = new LeadService(
                    campaignService = mockCampaignSvc,
                    cohortService = mock[CohortService],
                    contactService = mock[ContactService],
                    phoneEntryService = phoneEntryService(phones),
                    emailEntryService = emailEntryService(emails),
                    leadDetailsService = mock[LeadDetailsService],
                    leadPhoneSynchronizer = mock[LeadPhoneSynchronizer],
                    leadEmailSynchronizer = mock[LeadEmailSynchronizer],
                    mortgageDetailsService = mock[MortgageDetailsService],
                    opportunityService = mock[OpportunityService],
                    opportunityDetailsService = mock[OpportunityDetailsService],
                    partnerService = mock[PartnerService],
                    relationshipService = mock[RelationshipService],
                    taskService = mock[TaskService],
                    clientConfig = clientConfig
                )

                customService.isDuplicate(lead) must beRight(false)
            }

            tag("isDuplicate")
            "Case D: alwaysDedupe = false, dedupeDays = 30 - reject if match exists within time window" in {
                implicit val connection = mock[Connection]
                val phones = phoneEntries(defaultPhones.toList)
                val emails = emailEntries(defaultEmails.toList)

                val lead = fakeLead(phones, emails).copy(campaignId = Some(999L))

                val mockPhoneSvc = mock[PhoneEntryService]
                mockPhoneSvc.list(any[NonEmptyList[PhoneNumber]], any[Long])(any[Connection]) returns Right(phones)
                
                val mockEmailSvc = mock[EmailEntryService]
                mockEmailSvc.list(any[NonEmptyList[EmailAddress]], any[Long])(any[Connection]) returns Right(emails)

                val mockCampaign = mock[Campaign]
                mockCampaign.alwaysDedupe returns false
                mockCampaign.dedupeDays returns Some(30)

                val mockCampaignSvc = mock[CampaignService]
                mockCampaignSvc.read[JResult](any[Long])(any[Connection], any) returns Right(Some(mockCampaign))
                
                // Helper method to build LeadService with specific mocks for time-based checks
                def buildLeadServiceWithMocks(
                    hasPhoneMatch: Boolean,
                    hasEmailMatch: Boolean
                ): LeadService = {
                    val leadServiceInstance = new LeadService(
                        campaignService = mockCampaignSvc,
                        cohortService = mock[CohortService],
                        contactService = mock[ContactService],
                        phoneEntryService = mockPhoneSvc,
                        emailEntryService = mockEmailSvc,
                        leadDetailsService = mock[LeadDetailsService],
                        leadPhoneSynchronizer = mock[LeadPhoneSynchronizer],
                        leadEmailSynchronizer = mock[LeadEmailSynchronizer],
                        mortgageDetailsService = mock[MortgageDetailsService],
                        opportunityService = mock[OpportunityService],
                        opportunityDetailsService = mock[OpportunityDetailsService],
                        partnerService = mock[PartnerService],
                        relationshipService = mock[RelationshipService],
                        taskService = mock[TaskService],
                        clientConfig = clientConfig
                    )
                    val spyInstance = spy(leadServiceInstance)
                    doReturn(Right(hasPhoneMatch)).when(spyInstance).hasLeadPhoneSince(any[NonEmptyList[PhoneNumber]], any[Long], any[OffsetDateTime])(any[Connection])
                    doReturn(Right(hasEmailMatch)).when(spyInstance).hasLeadEmailSince(any[NonEmptyList[EmailAddress]], any[Long], any[OffsetDateTime])(any[Connection])
                    spyInstance
                }
                
                "returns true when a lead with a matching phone exists within dedupe window" in {
                    val service = buildLeadServiceWithMocks(hasPhoneMatch = true, hasEmailMatch = false)
                    service.isDuplicate(lead) must beRight(true)
                }

                "returns true when a lead with a matching email exists within dedupe window" in {
                    val service = buildLeadServiceWithMocks(hasPhoneMatch = false, hasEmailMatch = true)
                    service.isDuplicate(lead) must beRight(true)
                }
                
                "returns false when no leads with matching phone or email exist within dedupe window" in {
                    val service = buildLeadServiceWithMocks(hasPhoneMatch = false, hasEmailMatch = false)
                    service.isDuplicate(lead) must beRight(false)
                }
            }
        }

        tag("getPartner")
        "retrieve a partner for the lead" in new WithLeads {
            val now = OffsetDateTime.now
            database.withConnection { implicit t =>
                leadService.getPartner[Try](Lead.empty.copy(Some(1000L), campaignId = Some(10000L), orgId = 1L))
            }.map(_.map(_.copy(created = now))) must beASuccessfulTry(
                Some(Partner(
                    id = 1000L,
                    name = "Prudential",
                    created = now,
                    orgId = 1L,
                    allowPr = true,
                    b2b2c = false,
                    agencyId = Some(1L)
                ))
            )
        }

        tag("listDialingLeads")
        "list all leads that have a dialing list and the status 'Ready to Dial'" in new WithLeads {
            database.withConnection { implicit c =>
                leadService.listDialingLeads[Try](1L)
            } must beASuccessfulTry.like {
                case leads => leads.flatMap(_.id) must contain(exactly(1002L))
            }
        }

        "paginatedList" >> {
            tag("paginatedList")
            "retrieve a list of leads by default, ordered by most recently created by default" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L, 1010L, 1009L, 1008L, 1007L, 1006L, 1005L, 1004L, 1000L, 1001L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(11L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the first name" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("George"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the agent's name" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("Jake Sampson"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L, 1004L, 1000L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(3L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the last name" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("Sloane"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1002L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the full name" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("tina sloane"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1002L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the street address" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("state street"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1004L, 1000L, 1001L, 1002L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(4L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the zip" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("06037"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1001L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the city" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("hartford"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1004L, 1000L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(2L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the state name" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("New York"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the state abbreviation" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("NY"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by the full address" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("23 state street, hartford, ct"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1004L, 1000L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(2L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by a full phone number" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("************"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1001L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by a partial phone number" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("555"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1000L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by an email" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some("arthur@jaroop"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1001L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtered by a lead status" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(includeStatuses = List(LeadStatus.Active))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1007L, 1005L, 1004L, 1000L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(4L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads filtering out PR leads" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(includePolicyReview = false)
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L, 1010L, 1009L, 1008L, 1007L, 1006L, 1005L, 1000L, 1001L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(10L)
                }
            }

            tag("paginatedList")
            "retrieve a list of leads not filtered if the search text is empty" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(searchText = Some(""))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1003L, 1010L, 1009L, 1008L, 1007L, 1006L, 1005L, 1004L, 1000L, 1001L).inOrder)

                    leadService.count[JResult](options, orgId = 1L) must beRight(11L)
                }
            }

            tag("paginatedList")
            "limit the number of leads returned, not the number of rows" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(pageSize = Some(1), searchText = Some("Jay"))
                    val leads = leadService.paginatedList[JResult](options, orgId = 1L)
                    leads.map(_.flatMap(_.id)) must beRight(exactly(1000L))
                    leads.map(_.flatMap(_.contact.phones.flatMap(_.id))) must beRight(exactly(1000L, 1002L))

                    leadService.count[JResult](options, orgId = 1L) must beRight(1L)
                }
            }

            tag("paginatedList")
            "retrieve leads by exact phone number match" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(phoneNumber = Option(PhoneNumber.unsafe("2234567890")))
                    val Right(leads) = leadService.paginatedList[JResult](options, orgId = 1L)

                    leads.map(_.id.toList).flatten must beEqualTo(List(1000L, 1001L))
                }
            }

            tag("paginatedList")
            "retrieve leads by exact last name match (case-insensitive)" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(lastName = Option("cOsTaNzA"))
                    val Right(leads) = leadService.paginatedList[JResult](options, orgId = 1L)

                    leads.map(_.id.toList).flatten must beEqualTo(List(1003L))
                }
            }

            tag("paginatedList")
            "retrieve leads by exact date of birth match" in new WithLeads {
                database.withConnection { implicit c =>
                    val options = LeadOptions.default.copy(dateOfBirth = Option(LocalDate.of(1991, 12, 17)))
                    val Right(leads) = leadService.paginatedList[JResult](options, orgId = 1L)

                    leads.map(_.id.toList).flatten must beEqualTo(List(1002L))
                }
            }
        }

        "check that the user" >> {
            val testLead = Lead(
                id = Some(1000L),
                campaignId = Some(1000L),
                status = LeadStatus.Active,
                product = Some(ProductType.Term),
                contact = LeadContact(id = Some(1000L)),
                leadDetails = LeadDetails(leadId = Some(1000L)),
                orgId = 1L
            )
            val account = Account.empty.copy(id = Some(1000L), orgId = 1L)

            "can edit a lead in the user's organization" in new WithApplication {
                val result = leadService.canViewEdit[Try](testLead, account)
                result must beASuccessfulTry[PermissionLevel](PermissionLevel.Edit)
            }

            "but cannot access a lead not in the user's organization" in new WithApplication {
                val outsideLead = testLead.copy(orgId = 1000L)
                val result = leadService.canViewEdit[Try](outsideLead, account)
                result must beASuccessfulTry[PermissionLevel](PermissionLevel.NoView)
            }
        }

        "count the number of leads" >> {
            tag("countAAAPrLeadsForStatus")
            "from a list of rapport ids that are in a Ready To Dial status" in new WithLeads {
                database.withConnection { implicit c =>
                    val rapportIds = NonEmptyList.of("test_rapport_id") ++ (1 to 100000)
                        .foldLeft(List.empty[String])((sum, ele) => sum :+ s"test_rap_${ele}")
                    val Right(count) = leadService.countAAAPrLeadsForStatus[JResult](rapportIds, LeadStatus.ReadyToDial)
                    count must beEqualTo(1)
                }
            }
        }

        "fetch rapport id" >> {
            tag("fetchRapportId")
            "from an existing opportunity with an existing lead id" in new WithLeads {
                val result = database.withConnection(implicit c => leadService.fetchLeadIdForRapportId("test_rapport_id_2"))
                result must beEqualTo(List(1005L))
            }

            tag("fetchRapportId")
            "from a non existing rapport id in an opportunity" in new WithLeads {
                val exception = database.withConnection { implicit c =>
                    leadService.fetchLeadIdForRapportId("test_rapport_id_5")
                } must throwA[Exception]("No leads found for rapportId test_rapport_id_5")
            }
        }
    }

    "The LeadContactService" should {
        tag("read")
        "read a LeadContact" in new WithLeads {
            val result = database.withConnection(implicit c => leadContactService.read(1001L))
            result must beRight(testLead.contact)
        }
    }

    "hasLeadPhoneSince" >> {
        "return true when a lead exists with the given phone number created after the cutoff date" in new WithLeads {
            val cutOffDate = OffsetDateTime.of(2018, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)
            val phoneNumbers = NonEmptyList.of(PhoneNumber.unsafe("2234567890")) // Corresponds to Lead 1000, created 2018-02-01
            
            database.withConnection { implicit c =>
                leadService.hasLeadPhoneSince(phoneNumbers, 1L, cutOffDate)
            } must beRight(true)
        }
        
        "return false when no lead exists with the given phone number created after the cutoff date" in new WithLeads {
            val cutOffDate = OffsetDateTime.of(2020, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC) // Future date, Lead 1000 is older
            val phoneNumbers = NonEmptyList.of(PhoneNumber.unsafe("2234567890"))
            
            database.withConnection { implicit c =>
                leadService.hasLeadPhoneSince(phoneNumbers, 1L, cutOffDate)
            } must beRight(false)
        }
    }

    "hasLeadEmailSince" >> {
        "return true when a lead exists with the given email address created after the cutoff date" in new WithLeads {
            val cutOffDate = OffsetDateTime.of(2018, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC)
            val emails = NonEmptyList.of(EmailAddress.unsafe("<EMAIL>")) // Corresponds to Lead 1001, created 2018-02-01
            
            database.withConnection { implicit c =>
                leadService.hasLeadEmailSince(emails, 1L, cutOffDate)
            } must beRight(true)
        }
        
        "return false when no lead exists with the given email address created after the cutoff date" in new WithLeads {
            val cutOffDate = OffsetDateTime.of(2020, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC) // Future date, Lead 1001 is older
            val emails = NonEmptyList.of(EmailAddress.unsafe("<EMAIL>"))
            
            database.withConnection { implicit c =>
                leadService.hasLeadEmailSince(emails, 1L, cutOffDate)
            } must beRight(false)
        }
    }

}
// scalastyle:on file.size.limit
