package com.elagy.models

import cats.data.NonEmptyList
import cats.implicits._
import com.jaroop.blocks.authentication.models.Account
import com.jaroop.core.auth.basic.ApiUser
import com.jaroop.core.libs.sql.DatabaseOps._
import com.jaroop.play.test._
import java.time.OffsetDateTime
import org.specs2.mutable.Specification
import play.api.Application
import play.api.test.WithApplication
import scala.util.Try

class CampaignSpec extends Specification {

    class WithCampaigns extends WithSQL(
        "test/resources/models/TestCampaigns.sql"
    )

    def partnerService(implicit app: Application): PartnerService = component[PartnerService]
    def campaignService(implicit app: Application): CampaignService = component[CampaignService]

    "The CampaignService" should {
        tag("read", "id")
        "read a Campaign by ID" in new WithCampaigns {
            val id = 11000L
            database.withConnection(implicit c => campaignService.read[Try](id)) must beSuccessfulTry.like {
                case Some(campaign) => {
                    campaign.id must beEqualTo(id)
                    campaign.name must beEqualTo("Insuracy Default")
                    campaign.partnerId must beEqualTo(10000L)
                    campaign.default must beTrue
                    campaign.requiresTcpa must beTrue
                    campaign.jornayaProfileId must beSome(1234L)
                    campaign.providerEntityCode must beSome("insuracy")
                    campaign.rejectOnTcpaFailure must beFalse
                }
            }
        }

        tag("read", "id")
        "return None when a Campaign is not found by ID" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.read[Try](9999L)) must beSuccessfulTry[Option[Campaign]](None)
        }

        tag("read", "name")
        "read a Campaign by name" in new WithCampaigns {
            val name = "Insuracy Alternate"
            database.withConnection(implicit c => campaignService.read[Try](name, 1L)) must beSuccessfulTry.like {
                case Some(campaign) => {
                    campaign.id must beEqualTo(11002L)
                    campaign.name must beEqualTo(name)
                    campaign.partnerId must beEqualTo(10000L)
                    campaign.default must beFalse
                    campaign.requiresTcpa must beTrue
                }
            }
        }

        tag("read", "name")
        "return None when a Campaign is not found by name" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.read[Try]("not found", 1L)) must beSuccessfulTry[Option[Campaign]](None)
        }

        tag("list")
        "return all campaigns in alphabetical order" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.list[Try](1L)) must beSuccessfulTry.like {
                case list => {
                    list.map(_.id) must contain(exactly(11002L, 11000L, 11001L, 11003L))
                }
            }
        }

        tag("list", "partners")
        "return all campaigns for a list of partners" in new WithCampaigns {
            database.withConnection { implicit c =>
                for {
                    insuracy <- partnerService.read[Try]("Insuracy", 1L).map(_.get)
                    moo <- partnerService.read[Try]("Mutual of Omaha", 1L).map(_.get)
                    campaigns <- campaignService.list[Try](NonEmptyList.of(insuracy, moo), 1L)
                } yield campaigns
            } must beSuccessfulTry.like {
                case list => {
                    list.map(_.id) must contain(exactly(11002L, 11000L, 11001L, 11003L))
                }
            }
        }

        tag("list", "partners")
        "return an empty list for a partner with no campaigns" in new WithCampaigns {
            database.withConnection { implicit c =>
                for {
                    google <- partnerService.read[Try]("Google Ads", 1L).map(_.get)
                    campaigns <- campaignService.list[Try](NonEmptyList.one(google), 1L)
                } yield campaigns
            } must beSuccessfulTry[List[Campaign]](Nil)
        }

        tag("list", "ids")
        "return a list of campaigns for certain ids" in new WithCampaigns {
            database.withConnection { implicit c =>
                campaignService.list[Try](NonEmptyList.of(11002L), 1L)
            } must beSuccessfulTry.like {
                case list => list.map(_.name) must contain(exactly("Insuracy Alternate"))
            }
        }

        tag("list", "names")
        "return a list of campaigns for certain names" in new WithCampaigns {
            database.withConnection { implicit c =>
                campaignService.listByName[Try](NonEmptyList.of("Insuracy Alternate"), 1L)
            } must beSuccessfulTry.like {
                case list => list.map(_.id) must contain(exactly(11002L))
            }
        }

        tag("getDefault", "partner")
        "return the default campaign for a partner" in new WithCampaigns {
            database.withConnection { implicit c =>
                for {
                    insuracy <- partnerService.read[Try]("Insuracy", 1L).map(_.get)
                    default <- campaignService.getDefault[Try](insuracy, 1L)
                } yield default
            } must beSuccessfulTry.like {
                case Some(campaign) => {
                    campaign.id must beEqualTo(11000L)
                }
            }
        }

        tag("getDefault", "partner")
        "return None for a partner with no default campaign" in new WithCampaigns {
            database.withConnection { implicit c =>
                for {
                    google <- partnerService.read[Try]("Google Ads", 1L).map(_.get)
                    campaign <- campaignService.getDefault[Try](google, 1L)
                } yield campaign
            } must beSuccessfulTry[Option[Campaign]](None)
        }

        tag("getDefault", "ApiUser")
        "return the default campaign for an API user" in new WithCampaigns {
            database.withConnection { implicit c =>
                val user = ApiUser(id = Some(50000L), username = "insuracy", password = None, orgId = 1L)
                campaignService.getDefault[Try](user)
            } must beSuccessfulTry.like {
                case Some(campaign) => campaign.id must beEqualTo(11000L)
            }
        }

        tag("getDefault", "ApiUser")
        "return None when an API user has no default campaign" in new WithCampaigns {
            database.withConnection { implicit c =>
                val user = ApiUser(id = Some(50002L), username = "not_for_leads", password = None, orgId = 1L)
                campaignService.getDefault[Try](user)
            } must beSuccessfulTry[Option[Campaign]](None)
        }

        tag("create")
        "create a new campaign" in new WithCampaigns {
            val input = CampaignInput(
                name = "Inserted Campaign",
                partnerId = 10000L,
                requiresTcpa = true,
                default = false,
                jornayaProfileId = Some(5678L),
                providerEntityCode = Some("string"),
                rejectOnTcpaFailure = false,
                defaultTcpaStatus = Some(TCPAStatus.Yes),
                orgId = 1L,
                alwaysDedupe = true,
                dedupeDays = None
            )

            database.syncTransaction(implicit t => campaignService.create[Try](input, 1L)) must
                beSuccessfulTry.like { case campaign =>
                    campaign.id must beEqualTo(11004L)
                    campaign.name must beEqualTo("Inserted Campaign")
                    campaign.orgId must beEqualTo(1L)
                }
        }

        tag("updateLeadCampaign")
        "edit lead campaign" in new WithCampaigns {
            val input = EditLeadCampaignInput(
                id = 11003L,
                name = "Updated Campaign",
                partnerId = 10001L,
                requiresTcpa = true,
                default = false,
                orgId = 1L,
                jornayaProfileId = Some(5678L),
                providerEntityCode = Some("string"),
                rejectOnTcpaFailure = true,
                defaultTcpaStatus = Some(TCPAStatus.Yes),
                alwaysDedupe = true,
                dedupeDays = None
            )

            database.syncTransaction(implicit t => campaignService.updateLeadCampaign[Try](input, 1L)) must
              beSuccessfulTry.like { case campaign =>
                  campaign.id must beEqualTo(11003L)
                  //  campaign.name must beEqualTo("Updated Campaign")
                  //  campaign.orgId must beEqualTo(1L)
              }
        }

        tag("updateParentCampaign")
        "update parent for Lead campaign" in new WithCampaigns {
            val parentCampaignName = "MOO Parent"
            val campaignName = "MOO MTM"
            val orgId = 1L
            val campaignId = 11001L
            val reconcileId = 11003L
            database.syncTransaction(implicit t => campaignService.updateParentCampaign[Try]("Parent Input2", "MOO MTM", 1L, 11001L,Some(11003L))) must
              beSuccessfulTry.like { case parent =>
                  parent.campaignName must beEqualTo("MOO MTM")
                  parent.campaignId must beEqualTo(11001L)
                  parent.parentCampaignName must beEqualTo("Parent Input2")
                  parent.orgId must beEqualTo(1L)
                  parent.reconciled_name_id must beEqualTo(Some(11003L))
              }
        }

        tag("updateReconcile")
        "update reconcile for Lead campaign" in new WithCampaigns {
            val parentCampaignName = "MOO Parent"
            val campaignName = "MOO MTM"
            val orgId = 1L
            val campaignId = 11001L
            val reconcileId = 11003L
            database.syncTransaction(implicit t => campaignService.updateReconcileName[Try]("Parent Input2",11003L)) must
              beSuccessfulTry.like { case parent =>
                parent.parentCampaignName must beEqualTo("Parent Input2")
                parent.reconciled_name_id must beEqualTo(Some(11003L))
              }
        }

        tag("createParentCampaign")
        "create a parent for lead campaign" in new WithCampaigns {
            val parentCampaignName = "Parent Input 1"
            val campaignName = "Insuracy Default"
            val orgId = 1L
            val campaignId = 11000L
            val reconcileId = Some(11003L)
            database.syncTransaction(implicit t => campaignService.createParentCampaign[Try]("Parent Input 1", "Insuracy Default", 1L, 11000L,Some(11003L))) must
                beSuccessfulTry.like { case parent =>
                    parent.campaignName must beEqualTo("Insuracy Default")
                    parent.campaignId must beEqualTo(11000L)
                    parent.parentCampaignName must beEqualTo("Parent Input 1")
                    parent.orgId must beEqualTo(1L)
                    parent.reconciled_name_id must beEqualTo(Some(11003L))
                }
        }

        tag("updateDesignation")
        "update values in campaign_out_to_paid_designation" in new WithCampaigns {
            val srOutToPaid = false
            val prOutToPaid = true
            val campaignId = 11000L
            database.syncTransaction(implicit t => campaignService.updateDesignation[Try](srOutToPaid, prOutToPaid, campaignId)) must
                beSuccessfulTry.like { case campaign =>
                    campaign.srOutToPaid must beEqualTo(false)
                    campaign.prOutToPaid must beEqualTo(true)
                }
        }

        "check that the account" >> {
            val account = Account.empty.withId(1L).copy(orgId = 1L)
            val campaign = Campaign(
                id = 1000L,
                name = "Leads in a Can",
                created = OffsetDateTime.now,
                partnerId = 1000L,
                requiresTcpa = false,
                default = true,
                rejectOnTcpaFailure = false,
                orgId = 1L
            )

            "can access the campaign" in new WithApplication {
                val result = campaignService.canViewEdit[Try](campaign, account)
                result must beASuccessfulTry[PermissionLevel](PermissionLevel.Edit)
            }

            "but not if the campaign not a part of same organization" in new WithApplication {
                val result = campaignService.canViewEdit[Try](campaign.copy(orgId = 2L), account)
                result must beASuccessfulTry[PermissionLevel](PermissionLevel.NoView)
            }
        }
        //
        tag("listFullLeadCampaign")
        "return campaigns associated with partner id 10000 in alphabetical order" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.listFullLeadCampaigns[Try](List(10000), 1L)) must beSuccessfulTry.like {
                case list => {
                    list.map(_.name) must contain(exactly("Insuracy Alternate", "Insuracy Default"))
                    list.map(_.parentCampaign) must contain(exactly[Option[String]](None, None:Option[String]))
                    list.map(_.requiresTcpa) must contain(exactly(true, true))
                    list.map(_.default) must contain(exactly(false, true))
                    list.map(_.jornayaProfileId) must contain(exactly[Option[Long]](None, Some(1234L)))
                    list.map(_.rejectOnTcpaFailure) must contain(exactly(false, false))
                    list.map(_.defaultTcpaStatus) must contain(exactly[Option[TCPAStatus]](None, None:Option[TCPAStatus]))
                    list.map(_.srOutToPaid) must contain(exactly(Some(true), Some(true):Option[Boolean]))
                    list.map(_.prOutToPaid) must contain(exactly(Some(false), Some(false):Option[Boolean]))
                }
            }
        }

        tag("listFullLeadCampaign")
        "return campaigns associated with partner id 10001 in alphabetical order" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.listFullLeadCampaigns[Try](List(10001), 1L)) must beSuccessfulTry.like {

                case list => {
                    list.map(_.name) must contain(exactly("MOO MTM", "MOO Parent"))
                    list.map(_.parentCampaign) must contain(exactly(Some("MOO Parent"), None:Option[String]))
                    list.map(_.requiresTcpa) must contain(exactly(true, false))
                    list.map(_.default) must contain(exactly(false, true))
                    list.map(_.jornayaProfileId) must contain(exactly[Option[Long]](None, Some(5678L)))
                    list.map(_.rejectOnTcpaFailure) must contain(exactly(true, false))
                    list.map(_.defaultTcpaStatus) must contain(exactly(Some(TCPAStatus.Yes), Some(TCPAStatus.No): Option[TCPAStatus]))
                    list.map(_.srOutToPaid) must contain(exactly(Some(true), Some(true):Option[Boolean]))
                    list.map(_.prOutToPaid) must contain(exactly(Some(false), Some(false):Option[Boolean]))
                }
            }
        }

        tag("listFullCallCampaign")
        "return campaigns associated with a partner id 10000 in alphabetical order" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.listFullCallCampaigns[Try](List(10000), 1L)) must beSuccessfulTry.like {
                case list => {
                    list.map(_.name) must contain(exactly("Dialer Campaign Alternate", "Dialer Campaign Default"))
                    list.map(_.campaignId) must contain(exactly(Some("10002"), Some("10001"):Option[String]))
                    list.map(_.campaignAllowableTime) must contain(exactly(100L, 120L))
                    list.map(_.twoMinuteThreshold) must contain(exactly(100L, 120L))
                    list.map(_.dedupeDay) must contain(exactly[Option[Boolean]](None, Some(false)))
                    list.map(_.dedupeWeek) must contain(exactly(Some(true), Some(false):Option[Boolean]))
                    list.map(_.autoAnswerConnect) must contain (exactly(Some(true), Some(false):Option[Boolean]))
                    list.map(_.trueTimeBillableDuration) must contain(exactly(20L, 30L))
                    list.map(_.agentBillableTime) must contain(exactly(10L, 15L))
                    list.map(_.domain) must contain(exactly(Some("Elagy MTM"), Some("Accuquote - Developer Program"):Option[String]))
                    list.map(_.srV2vEligible) must contain(exactly(Some(true), Some(true):Option[Boolean]))
                    list.map(_.prV2vEligible) must contain(exactly(Some(false), Some(false):Option[Boolean]))
                }
            }
        }

        tag("createFive9")
        "create a new campaign in Five9" in new WithCampaigns {
            val input = FullCallCampaignInput(
                name = "New Campaign",
                campaignId = "New Five9",
                campaignAllowableTime = "32",
                twoMinuteThreshold = "44",
                dedupeDay = Option(true),
                dedupeWeek = Option(false),
                autoAnswerConnect = Option(true),
                trueTimeBillableDuration = "32",
                agentBillableTime = "12",
                domain = "Elagy MTM",
                srV2vEligible = Option(true),
                prV2vEligible = Option(true)
            )
            val domainInput = 36L
            database.syncTransaction(implicit t => campaignService.createFive9[Try](input, 1L, 10000L, domainInput)) must
              beSuccessfulTry.like { case five9 =>
                  five9.campaignName must beEqualTo("New Campaign")
              }
        }

        tag("createCampaignV2V")
        "create a new campaign in CampaignV2V" in new WithCampaigns {
            val input = FullCallCampaignInput(
                name = "New Campaign",
                campaignId = "New Five9",
                campaignAllowableTime = "32",
                twoMinuteThreshold = "44",
                dedupeDay = Option(true),
                dedupeWeek = Option(false),
                autoAnswerConnect = Option(true),
                trueTimeBillableDuration = "32",
                agentBillableTime = "12",
                domain = "Elagy MTM",
                srV2vEligible = Option(true),
                prV2vEligible = Option(true)
            )
            val domainInput = "36"
            database.syncTransaction(implicit t => campaignService.createCampaignV2V[Try](input, 101L, domainInput)) must
              beSuccessfulTry.like { case campaignV2V =>
                  campaignV2V.campaignId must beEqualTo(Some("New Five9"))
              }
        }

        tag("readFive9")
        "select a campaign in Five9" in new WithCampaigns {
            database.syncTransaction(implicit t => campaignService.readFive9[Try]("Dialer Campaign Default", 36L)) must
              beSuccessfulTry.like { case Some(five9) =>
                  five9.campaignName must beEqualTo("Dialer Campaign Default")
              }
        }

        tag("readCampaignV2V")
        "select a campaign in CampaignV2V" in new WithCampaigns {
            database.syncTransaction(implicit t => campaignService.readCampaignV2V[Try]("10002", 36L)) must
              beSuccessfulTry.like { case Some(campaignV2V) =>
                  campaignV2V.campaignName must beEqualTo("Test2 Outbound Default")
              }
        }

        tag("updateFive9")
        "Update campaign in Five9" in new WithCampaigns {
            val input = EditCallCampaignInput(
                id = 101L,
                name = "New Campaign_v2",
                campaignId = "New Five9_v2",
                campaignAllowableTime = "33",
                twoMinuteThreshold = "55",
                dedupeDay = Option(true),
                dedupeWeek = Option(false),
                autoAnswerConnect = Option(true),
                trueTimeBillableDuration = "32",
                agentBillableTime = "12",
                domainName = "Elagy MTM_v2",
                domainId = 36L,
                srV2vEligible = Option(true),
                prV2vEligible = Option(true),
                partnerIdNewCall = 10000L
            )
            database.syncTransaction(implicit t => campaignService.updateFive9[Try](input, 1L)) must
              beSuccessfulTry.like { case five9 =>
                  five9.campaignName must beEqualTo("New Campaign_v2")
              }
        }

        tag("updateCampaignV2V")
        "update campaign in CampaignV2V" in new WithCampaigns {
            val input = EditCallCampaignInput(
                id = 101L,
                name = "New Campaign_v2",
                campaignId = "New Five9_v2",
                campaignAllowableTime = "33",
                twoMinuteThreshold = "55",
                dedupeDay = Option(true),
                dedupeWeek = Option(false),
                autoAnswerConnect = Option(true),
                trueTimeBillableDuration = "32",
                agentBillableTime = "12",
                domainName = "Elagy MTM_v2",
                domainId = 36L,
                srV2vEligible = Option(true),
                prV2vEligible = Option(true),
                partnerIdNewCall = 10000L
            )
            database.syncTransaction(implicit t => campaignService.updateCampaignV2V[Try](input)) must
              beSuccessfulTry.like { case campaignV2V =>
                  campaignV2V.campaignId must beEqualTo(Some("New Five9_v2"))
              }
        }

        tag("listCampaignV2V")
        "return five 9 campaign id and domain details" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.listCampaignV2V[Try]() must beSuccessfulTry(
                List(
                    CampaignV2V("Dialer Campaign Default", 101L, "10001", "Accuquote - Developer Program", "20001"),
                    CampaignV2V("Dialer Campaign Alternate", 102L, "10002", "Elagy MTM", "36")
                )))
         }

        tag("listJornayaProfiles")
        "return Jornaya Profile id and name" in new WithCampaigns {
            database.withConnection(implicit c => campaignService.listJornayaProfiles[Try]() must beSuccessfulTry(
                List(
                    JornayaProfile(1234L,"Test Profile"),
                    JornayaProfile(5678L,"Test Profile 2")
                )))
        }

        tag("listJornayaProfiles")
        "not return healthcare.com jornaya profile" in new WithCampaigns {
            database.withConnection { implicit c =>
                val profiles = campaignService.listJornayaProfiles[Try]()
                profiles.get.foreach { profile =>
                    profile.name must not be equalTo("healthcare.com")
                }
            }
        }
    }
}
