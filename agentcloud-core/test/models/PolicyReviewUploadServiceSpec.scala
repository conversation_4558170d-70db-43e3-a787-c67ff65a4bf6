package com.elagy.models

import cats.data.NonEmptyList
import com.elagy.events.models.LeadCreation
import com.jaroop.utils.DetailedLogger
import com.jaroop.blocks.authentication.models.Account
import com.jaroop.core.datatypes.State
import com.jaroop.core.errors._
import com.jaroop.core.libs.sql.DatabaseOps.Transaction
import com.jaroop.play.test.MockDatabase

import java.sql.Connection
import java.time.OffsetDateTime
import org.specs2.concurrent._
import org.specs2.mock._
import org.specs2.mutable._
import play.api.Configuration

import java.util.UUID
import scala.concurrent.duration._
import scala.concurrent.Future
import scala.reflect.ClassTag

class PolicyReviewUploadServiceSpec(implicit ee: ExecutionEnv) extends Specification with Mockito {
    val configuration = mock[Configuration]
    configuration.get[String]("eap.partnerNames.policyReview") returns "Policy Review"

    implicit val connection: Connection = mock[Connection]
    implicit val uuid: UUID = UUID.randomUUID()

    def getService(
        campaignService: CampaignService = mock[CampaignService],
        cohortService: CohortService = mock[CohortService],
        contactService: ContactService = mock[ContactService],
        leadService: LeadService = mock[LeadService],
        opportunityService: OpportunityService = mock[OpportunityService],
        partnerService: PartnerService = mock[PartnerService],
        detailedLogger: DetailedLogger =  mock[DetailedLogger]
    ): PolicyReviewUploadService = new PolicyReviewUploadService(
        campaignService = campaignService,
        cohortService = cohortService,
        configuration = configuration,
        contactService = contactService,
        db = MockDatabase(),
        leadService = leadService,
        opportunityService = opportunityService,
        partnerService = partnerService,
        detailedLogger = detailedLogger
    )

    val now = OffsetDateTime.now()

    val mockLead = Lead(
        id = Some(6000L),
        status = LeadStatus.ReadyToDial,
        campaignId = Some(2001L),
        contact = LeadContact(
            id = Some(5000L),
            created = now,
            firstName = Some("John"),
            middleName = Some("Quincy"),
            lastName = Some("Public"),
            street = Some("123 Main Street"),
            city = Some("Anywhere"),
            state = Some(State.Connecticut),
            zip = Some("00000")
        ),
        product = None,
        leadDetails = LeadDetails(
            leadId = None
        ),
        orgId = 1L
    )

    val mockCampaign = Right(Some(Campaign(2001L, "Campaign AAAA Blah", 1000L, now, false, false, 1L)))

    val mockpartner = Partner(
        id = 1000L,
        name = "Policy Review",
        created = OffsetDateTime.now,
        orgId = 1L,
        allowPr = true,
        b2b2c = false,
        agencyId = Some(1L)
    )

    "The PolicyReviewUploadService" should {
        "when creating a PR cohort" >> {
            tag("createPolicyReviewCohort")
            "create the new campaign and cohort" in {

                val cohort = Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)

                val campaignService = mock[CampaignService]
                campaignService.create(any[CampaignInput], any[Long])(any[Transaction], any[JMonad[JResult]]) returns
                    Right(Campaign(2000L, "Campaign Name", 3000L, now, false, false, 1L))
                campaignService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(None)

                val cohortService = mock[CohortService]
                cohortService.read(any[String])(any[Connection], any[JMonad[JResult]]) returns Right(None)
                cohortService.create(any[CohortInput])(any[Transaction], any[JMonad[JResult]]) returns
                    Right(cohort)

                val partnerService = mock[PartnerService]
                partnerService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))
                partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))


                val input = InitialCohortInput(
                    name = "Cohort Name",
                    campaignName = "Campaign Name",
                    description = None
                )

                val service = getService(
                    campaignService = campaignService,
                    cohortService = cohortService,
                    partnerService = partnerService
                )

                val result = service.createPolicyReviewCohort(input, Some(1000L), 1L)(mock[Transaction])
                result must beRight(cohort)

                val expectedCampaignInput = CampaignInput(
                    name = "Campaign Name",
                    partnerId = 3000L,
                    requiresTcpa = false,
                    default = false,
                    orgId = 1L,
                    alwaysDedupe = true,
                    dedupeDays = None
                )

                val expectedCohortInput = CohortInput(
                    uploader = Some(1000L),
                    name = "Cohort Name",
                    campaignId = 2000L,
                    description = None
                )

                there was one(campaignService).create(
                    ===(expectedCampaignInput), any[Long]
                )(any[Transaction], any[JMonad[JResult]])
                there was one(cohortService).create(===(expectedCohortInput))(any[Transaction], any[JMonad[JResult]])
            }

            tag("createPolicyReviewCohort")
            "but not if there's one with the same name" in {

                val cohort = Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)

                val campaignService = mock[CampaignService]
                campaignService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Campaign(2000L, "Campaign Name", 3000L, now, false, false, 1L)))

                val cohortService = mock[CohortService]
                cohortService.read(any[String])(any[Connection], any[JMonad[JResult]]) returns Right(Some(cohort))
                cohortService.create(any[CohortInput])(any[Transaction], any[JMonad[JResult]]) returns
                    Right(cohort)

                val partnerService = mock[PartnerService]
                partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))
                partnerService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))

                val input = InitialCohortInput(
                    name = "Cohort Name",
                    campaignName = "Campaign Name",
                    description = None
                )

                val service = getService(
                    campaignService = campaignService,
                    cohortService = cohortService,
                    partnerService = partnerService
                )

                val result = service.createPolicyReviewCohort(input, Some(1000L), 1L)(mock[Transaction])
                result must beLeft(InvalidError(s"A Cohort with the name ${input.name} already exists."))

                val expectedCampaignInput = CampaignInput(
                    name = "Campaign Name",
                    partnerId = 3000L,
                    requiresTcpa = false,
                    default = false,
                    orgId = 1L,
                    alwaysDedupe = true,
                    dedupeDays = None
                )

                val expectedCohortInput = CohortInput(
                    uploader = Some(1000L),
                    name = "Cohort Name",
                    campaignId = 2000L,
                    description = None
                )

                there was one(campaignService).read(any[String], any[Long])(any[Transaction], any[JMonad[JResult]])
                there was no(cohortService).create(===(expectedCohortInput))(any[Transaction], any[JMonad[JResult]])
            }

            tag("createPolicyReviewCohort")
            "create the new campaign and cohort with a different partner" in {

                val cohort = Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)

                val campaignService = mock[CampaignService]
                campaignService.create(any[CampaignInput], any[Long])(any[Transaction], any[JMonad[JResult]]) returns
                    Right(Campaign(2000L, "Campaign Name", 3000L, now, false, false, 1L))
                campaignService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(None)

                val cohortService = mock[CohortService]
                cohortService.read(any[String])(any[Connection], any[JMonad[JResult]]) returns Right(None)
                cohortService.create(any[CohortInput])(any[Transaction], any[JMonad[JResult]]) returns
                    Right(cohort)

                val partnerService = mock[PartnerService]
                partnerService.read(===("Some Other Partner"), any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3001L, "Some Other Partner", now, 1L, true, false, None, None, Some(1L))))

                val input = InitialCohortInput(
                    name = "Cohort Name",
                    campaignName = "Campaign Name",
                    description = None
                )

                val service = getService(
                    campaignService = campaignService,
                    cohortService = cohortService,
                    partnerService = partnerService
                )

                val result = service.createPolicyReviewCohort(
                    input,
                    Some(1000L),
                    1L,
                    "Some Other Partner"
                )(mock[Transaction])
                result must beRight(cohort)

                val expectedCampaignInput = CampaignInput(
                    name = "Campaign Name",
                    partnerId = 3001L,
                    requiresTcpa = false,
                    default = false,
                    orgId = 1L,
                    alwaysDedupe = true,
                    dedupeDays = None
                )

                val expectedCohortInput = CohortInput(
                    uploader = Some(1000L),
                    name = "Cohort Name",
                    campaignId = 2000L,
                    description = None
                )

                there was one(campaignService).create(
                    ===(expectedCampaignInput), any[Long]
                )(any[Transaction], any[JMonad[JResult]])
                there was one(cohortService).create(===(expectedCohortInput))(any[Transaction], any[JMonad[JResult]])
            }

            tag("createPolicyReviewCohort")
            "fail when the PR partner is missing" in {
                val campaignService = mock[CampaignService]

                val cohortService = mock[CohortService]

                val partnerService = mock[PartnerService]
                partnerService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(None)

                val input = InitialCohortInput(
                    name = "Cohort Name",
                    campaignName = "Campaign Name",
                    description = None
                )

                val service = getService(
                    campaignService = campaignService,
                    cohortService = cohortService,
                    partnerService = partnerService
                )

                val result = service.createPolicyReviewCohort(input, Some(1000L), 1L)(mock[Transaction])
                result must beLeft(MiscError("No Policy Review partner found."))

                there was no(campaignService).create(any[CampaignInput], any[Long])(any[Transaction], any[JMonad[JResult]])
                there was no(cohortService).create(any[CohortInput])(any[Transaction], any[JMonad[JResult]])
            }
        }

        "when uploading the PR leads" >> {
            tag("uploadForPolicyReview")
            "upload the leads successfully" in {

                val cohortService = mock[CohortService]
                cohortService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)))
                cohortService.createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]]) returns
                    Right(LeadCohort(5000L, 4000L, 6000L))
                cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(false)

                val contactService = mock[ContactService]
                contactService.read(any[Long])(any[Connection]) returns Right(Contact(
                    id = Some(7000L),
                    status = ContactStatus.Active,
                    agentId = 1000L,
                    homeAddress = ContactAddress(
                        street = Some("123 Main Street"),
                        street2 = None,
                        city = Some("Anywhere"),
                        state = Some(State.Connecticut),
                        zip = Some("00000")
                    ),
                    mailingAddress = ContactAddress(None, None, None, None, None),
                    firstName = Some("John"),
                    middleName = Some("Quincy"),
                    lastName = Some("Public"),
                    orgId = 1L
                ))

                val testLead = Lead(
                    id = Some(5000L),
                    status = LeadStatus.ReadyToDial,
                    contact = LeadContact(
                        id = Some(5000L),
                        created = now,
                        firstName = Some("John"),
                        middleName = Some("Quincy"),
                        lastName = Some("Public"),
                        street = Some("123 Main Street"),
                        city = Some("Anywhere"),
                        state = Some(State.Connecticut),
                        zip = Some("00000")
                    ),
                    product = None,
                    leadDetails = LeadDetails(
                        leadId = None
                    ),
                    orgId = 1L
                )
                val leadService = mock[LeadService]
                leadService.create(any[Lead], any)(any[Transaction]) returns Right(testLead)

                val opportunityService = mock[OpportunityService]
                opportunityService.list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(List(Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L), product = ProductType.Term,
                        leadId = Some(6000L))))

                val partnerService = mock[PartnerService]
                partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    partnerService = partnerService,
                    opportunityService = opportunityService
                )

                //format error records are suppressed during csv parsing and considered for pr uploads
                val fileContents =
                    """Opportunity ID,Column 2,Column 3
                      |1200AB,format error opportunity,suppressed
                      |6000,foo,bar""".stripMargin
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must beEqualTo(List(testLead)).awaitFor(10.seconds)

                val expectedLeadCohort = LeadCohort(
                    leadId = 5000L,
                    cohortId = 4000L,
                    opportunityId = 6000L
                )

                there was one(cohortService).read(===(4000L))(any[Connection], any[JMonad[JResult]])
                there was one(cohortService).createLeadCohort(===(expectedLeadCohort))(any[Transaction], any[JMonad[JResult]])
                there was one(contactService).read(===(7000L))(any[Connection])
                there was one(leadService).create(any[Lead], any)(any[Transaction])
                there was one(opportunityService).list(===(NonEmptyList.of(6000L)), any[Long])(any[Connection], any[JMonad[JResult]])
            }

            "upload the leads successfully for a valid opportunity product type" in {

                val cohortService = mock[CohortService]
                cohortService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                  Right(Some(Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)))
                cohortService.createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]]) returns
                  Right(LeadCohort(5000L, 4000L, 6000L))
                cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                  Right(false)

                val contactService = mock[ContactService]
                contactService.read(any[Long])(any[Connection]) returns Right(Contact(
                    id = Some(7000L),
                    status = ContactStatus.Active,
                    agentId = 1000L,
                    homeAddress = ContactAddress(
                        street = Some("123 Main Street"),
                        street2 = None,
                        city = Some("Anywhere"),
                        state = Some(State.Connecticut),
                        zip = Some("00000")
                    ),
                    mailingAddress = ContactAddress(None, None, None, None, None),
                    firstName = Some("John"),
                    middleName = Some("Quincy"),
                    lastName = Some("Public"),
                    orgId = 1L
                ))

                val testLead = Lead(
                    id = Some(5000L),
                    status = LeadStatus.ReadyToDial,
                    contact = LeadContact(
                        id = Some(5000L),
                        created = now,
                        firstName = Some("John"),
                        middleName = Some("Quincy"),
                        lastName = Some("Public"),
                        street = Some("123 Main Street"),
                        city = Some("Anywhere"),
                        state = Some(State.Connecticut),
                        zip = Some("00000")
                    ),
                    product = None,
                    leadDetails = LeadDetails(
                        leadId = None
                    ),
                    orgId = 1L
                )
                val leadService = mock[LeadService]
                leadService.create(any[Lead], any)(any[Transaction]) returns Right(testLead)
                leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)

                val opportunityService = mock[OpportunityService]
                opportunityService.list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                  Right(List(Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L), leadId = Some(6000L),
                        product = ProductType.Term),
                      Opportunity.empty.copy(id = Some(5000L), ownerId = Some(7000L), product = ProductType.FinalExpense)))

                val campaignService = mock[CampaignService]
                campaignService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    (mockCampaign)

                val partnerService = mock[PartnerService]
                partnerService.read(any[Long], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(mockpartner))
                partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    opportunityService = opportunityService,
                    campaignService = campaignService,
                    partnerService = partnerService
                )

                val fileContents =
                    """Opportunity ID,Column 2,Column 3
                      |6000,foo,bar
                      |5000,notTerm,excluded""".stripMargin
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must beEqualTo(List(testLead)).awaitFor(10.seconds)

                val expectedLeadCohort = LeadCohort(
                    leadId = 5000L,
                    cohortId = 4000L,
                    opportunityId = 6000L
                )

                val expectedLeadCohortForFinalExpenseOppId = LeadCohort(
                    leadId = 5000L,
                    cohortId = 4000L,
                    opportunityId = 5000L
                )

                there was one(cohortService).read(===(4000L))(any[Connection], any[JMonad[JResult]])
                there was one(cohortService).createLeadCohort(===(expectedLeadCohort))(any[Transaction], any[JMonad[JResult]])
                there was no(cohortService).createLeadCohort(===(expectedLeadCohortForFinalExpenseOppId))(any[Transaction], any[JMonad[JResult]])
                there was one(contactService).read(===(7000L))(any[Connection])
                there was one(leadService).create(any[Lead], any)(any[Transaction])
                there was one(opportunityService).list(===(NonEmptyList.of(6000L, 5000)), any[Long])(any[Connection], any[JMonad[JResult]])
            }

            tag("uploadForPolicyReview")
            "upload no leads if the passed-in opportunity already has an active lead" in {

                val cohortService = mock[CohortService]
                cohortService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)))
                cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(true)

                val contactService = mock[ContactService]

                val leadService = mock[LeadService]
                leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)

                val opportunityService = mock[OpportunityService]
                opportunityService.list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                    Right(List(Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L))))

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    opportunityService = opportunityService
                )

                val fileContents =
                    """Opportunity ID,Column 2,Column 3
                      |6000,foo,bar""".stripMargin
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must beEqualTo(Nil).awaitFor(10.seconds)

                there was one(cohortService).read(===(4000L))(any[Connection], any[JMonad[JResult]])
                there was no(cohortService).createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]])
                there was no(contactService).read(any[Long])(any[Connection])
                there was no(leadService).create(any[Lead], any)(any[Transaction])
                there was one(opportunityService).list(===(NonEmptyList.of(6000L)), any[Long])(any[Connection], any[JMonad[JResult]])
            }

            tag("uploadForPolicyReview")
            "upload no leads if the passed-in opportunity is not associated with Term product type" in {

                val cohortService = mock[CohortService]
                cohortService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                  Right(Some(Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)))
                cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                  Right(false)

                val contactService = mock[ContactService]

                val leadService = mock[LeadService]
                leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)

                val opportunityService = mock[OpportunityService]
                opportunityService.list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                  Right(List(Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L))))

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    opportunityService = opportunityService
                )

                val fileContents =
                    """Opportunity ID,Column 2,Column 3
                      |6000,foo,bar""".stripMargin
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must beEqualTo(Nil).awaitFor(10.seconds)

                there was one(cohortService).read(===(4000L))(any[Connection], any[JMonad[JResult]])
                there was no(cohortService).createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]])
                there was no(contactService).read(any[Long])(any[Connection])
                there was no(leadService).create(any[Lead], any)(any[Transaction])
                there was one(opportunityService).list(===(NonEmptyList.of(6000L)), any[Long])(any[Connection], any[JMonad[JResult]])
            }

            tag("uploadForPolicyReview")
            "upload no leads if the passed-in opportunity owned id is not present" in {

                val cohortService = mock[CohortService]
                cohortService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                  Right(Some(Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)))
                cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                  Right(false)

                val contactService = mock[ContactService]

                val leadService = mock[LeadService]
                leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)

                val opportunityService = mock[OpportunityService]
                opportunityService.list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                  Right(List(Opportunity.empty.copy(id = Some(6000L), product = ProductType.Term)))

                val partnerService = mock[PartnerService]
                partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                    Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    opportunityService = opportunityService,
                    partnerService = partnerService
                )

                val fileContents =
                    """Opportunity ID,Column 2,Column 3
                      |6000,foo,bar""".stripMargin
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must beEqualTo(Nil).awaitFor(10.seconds)

                there was one(cohortService).read(===(4000L))(any[Connection], any[JMonad[JResult]])
                there was no(cohortService).createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]])
                there was no(contactService).read(any[Long])(any[Connection])
                there was no(leadService).create(any[Lead], any)(any[Transaction])
                there was one(opportunityService).list(===(NonEmptyList.of(6000L)), any[Long])(any[Connection], any[JMonad[JResult]])
            }

            tag("uploadForPolicyReview")
            "upload no leads if the file is empty" in {
                val cohortService = mock[CohortService]
                val contactService = mock[ContactService]
                val leadService = mock[LeadService]
                leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)
                val opportunityService = mock[OpportunityService]

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    opportunityService = opportunityService
                )

                val fileContents = ""
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must throwA[InvalidException].awaitFor(10.seconds)

                there was no(cohortService).read(any[Long])(any[Connection], any[JMonad[JResult]])
                there was no(cohortService).createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]])
                there was no(contactService).read(any[Long])(any[Connection])
                there was no(leadService).create(any[Lead], any)(any[Transaction])
                there was no(opportunityService).list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]])
            }

            tag("uploadForPolicyReview")
            "upload no leads if the cohort doesn't exist" in {

                val cohortService = mock[CohortService]
                cohortService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(None)

                val contactService = mock[ContactService]
                val leadService = mock[LeadService]
                leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)
                val opportunityService = mock[OpportunityService]

                val service = getService(
                    cohortService = cohortService,
                    contactService = contactService,
                    leadService = leadService,
                    opportunityService = opportunityService
                )

                val fileContents =
                    """Opportunity ID,Column 2,Column 3
                      |6000,foo,bar""".stripMargin
                service.uploadForPolicyReview(
                    cohortId = 4000L,
                    fileContents = fileContents,
                    user = ApplicationUser(Account.empty.copy(id = Some(1000L)))
                ) must throwA[DoesNotExistException].awaitFor(10.seconds)

                there was one(cohortService).read(===(4000L))(any[Connection], any[JMonad[JResult]])
                there was no(cohortService).createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]])
                there was no(contactService).read(any[Long])(any[Connection])
                there was no(leadService).create(any[Lead], any)(any[Transaction])
                there was no(opportunityService).list(any[NonEmptyList[Long]], any[Long])(any[Connection], any[JMonad[JResult]])
            }
        }

        tag("uploadForPolicyReview")
        "convert an opportunity to a PR lead" in {
            val cohort = Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)
            val opportunity = Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L), leadId = Some(6000L))

            val cohortService = mock[CohortService]
            cohortService.createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]]) returns
                Right(LeadCohort(5000L, 4000L, 6000L))
            cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                Right(false)

            val partnerService = mock[PartnerService]
            partnerService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))
            partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                Right(Some(Partner(3000L, "Policy Review", now, 1L, true, false, None, None, Some(1L))))
            partnerService.read(any[Long], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                Right(Some(mockpartner))

            val contactService = mock[ContactService]
            contactService.read(any[Long])(any[Connection]) returns Right(Contact(
                id = Some(7000L),
                status = ContactStatus.Active,
                agentId = 1000L,
                homeAddress = ContactAddress(
                    street = Some("123 Main Street"),
                    street2 = None,
                    city = Some("Anywhere"),
                    state = Some(State.Connecticut),
                    zip = Some("00000")
                ),
                mailingAddress = ContactAddress(None, None, None, None, None),
                firstName = Some("John"),
                middleName = Some("Quincy"),
                lastName = Some("Public"),
                orgId = 1L
            ))

            val testLead = Lead(
                id = Some(5000L),
                status = LeadStatus.ReadyToDial,
                contact = LeadContact(
                    id = Some(5000L),
                    created = now,
                    firstName = Some("John"),
                    middleName = Some("Quincy"),
                    lastName = Some("Public"),
                    street = Some("123 Main Street"),
                    city = Some("Anywhere"),
                    state = Some(State.Connecticut),
                    zip = Some("00000")
                ),
                product = None,
                leadDetails = LeadDetails(
                    leadId = None
                ),
                orgId = 1L
            )
            val leadService = mock[LeadService]
            leadService.create(any[Lead], any)(any[Transaction]) returns Right(testLead)
            leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)

            val campaignService = mock[CampaignService]
            campaignService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                (mockCampaign)

            val service = getService(
                cohortService = cohortService,
                contactService = contactService,
                leadService = leadService,
                campaignService = campaignService,
                partnerService = partnerService
            )

            val Right(result) = service.convertToLead(opportunity, cohort, 1L)(mock[Transaction]).value
            result must beEqualTo(Option(testLead))
        }

        tag("uploadForPolicyReview")
        "but not if the partner is marked as no PR" in {
            val cohort = Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)
            val opportunity = Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L), leadId = Some(6000L))

            val cohortService = mock[CohortService]
            cohortService.createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]]) returns
                Right(LeadCohort(5000L, 4000L, 6000L))
            cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                Right(false)

            val badpartner = Partner(
                id = 1000L,
                name = "Policy Review",
                created = OffsetDateTime.now,
                orgId = 1L,
                allowPr = false,
                b2b2c = false,
                agencyId = Some(1L)
            )

            val partnerService = mock[PartnerService]
            partnerService.read(any[String], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                Right(Some(Partner(3000L, "Policy Review", now, 1L, false, false, None, None, Some(1L))))
            partnerService.read(any[Long], any[Long])(any[Connection], any[JMonad[JResult]]) returns
                Right(Some(badpartner))
            partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                Right(Some(Partner(3000L, "Policy Review", now, 1L, false, false, None, None, Some(1L))))

            val contactService = mock[ContactService]
            contactService.read(any[Long])(any[Connection]) returns Right(Contact(
                id = Some(7000L),
                status = ContactStatus.Active,
                agentId = 1000L,
                homeAddress = ContactAddress(
                    street = Some("123 Main Street"),
                    street2 = None,
                    city = Some("Anywhere"),
                    state = Some(State.Connecticut),
                    zip = Some("00000")
                ),
                mailingAddress = ContactAddress(None, None, None, None, None),
                firstName = Some("John"),
                middleName = Some("Quincy"),
                lastName = Some("Public"),
                orgId = 1L
            ))

            val testLead = Lead(
                id = Some(5000L),
                status = LeadStatus.ReadyToDial,
                contact = LeadContact(
                    id = Some(5000L),
                    created = now,
                    firstName = Some("John"),
                    middleName = Some("Quincy"),
                    lastName = Some("Public"),
                    street = Some("123 Main Street"),
                    city = Some("Anywhere"),
                    state = Some(State.Connecticut),
                    zip = Some("00000")
                ),
                product = None,
                leadDetails = LeadDetails(
                    leadId = None
                ),
                orgId = 1L
            )

            val leadService = mock[LeadService]
            leadService.create(any[Lead], any)(any[Transaction]) returns Right(testLead)
            leadService.read[JResult](any[Long])(any[Connection], any[JMonad[JResult]]) returns Right(mockLead)

            val campaignService = mock[CampaignService]
            campaignService.read(any[Long])(any[Connection], any[JMonad[JResult]]) returns
                (mockCampaign)

            val service = getService(
                cohortService = cohortService,
                contactService = contactService,
                leadService = leadService,
                campaignService = campaignService,
                partnerService = partnerService
            )

            val result = service.convertToLead(opportunity, cohort, 1L)(mock[Transaction]).value
            result must beEqualTo(Right(None))
        }

        tag("uploadForPolicyReview")
        "Opportunity with no partner should create PR Lead" in {
            val cohort = Cohort(4000L, now, Some(1000L), "Cohort Name", 2000L, None)
            val opportunity = Opportunity.empty.copy(id = Some(6000L), ownerId = Some(7000L))

            val cohortService = mock[CohortService]
            cohortService.createLeadCohort(any[LeadCohort])(any[Transaction], any[JMonad[JResult]]) returns
                Right(LeadCohort(5000L, 4000L, 6000L))
            cohortService.hasActiveLeadCohort(any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                Right(false)

            val partnerService = mock[PartnerService]
            partnerService.getPartnerForOpportunity[JResult](any[Opportunity])(any[Connection], any[JMonad[JResult]]) returns
                Right(None)

            val contactService = mock[ContactService]
            contactService.read(any[Long])(any[Connection]) returns Right(Contact(
                id = Some(7000L),
                status = ContactStatus.Active,
                agentId = 1000L,
                homeAddress = ContactAddress(
                    street = Some("123 Main Street"),
                    street2 = None,
                    city = Some("Anywhere"),
                    state = Some(State.Connecticut),
                    zip = Some("00000")
                ),
                mailingAddress = ContactAddress(None, None, None, None, None),
                firstName = Some("John"),
                middleName = Some("Quincy"),
                lastName = Some("Public"),
                orgId = 1L
            ))

            val testLead = Lead(
                id = Some(5000L),
                status = LeadStatus.ReadyToDial,
                contact = LeadContact(
                    id = Some(5000L),
                    created = now,
                    firstName = Some("John"),
                    middleName = Some("Quincy"),
                    lastName = Some("Public"),
                    street = Some("123 Main Street"),
                    city = Some("Anywhere"),
                    state = Some(State.Connecticut),
                    zip = Some("00000")
                ),
                product = None,
                leadDetails = LeadDetails(
                    leadId = None
                ),
                orgId = 1L
            )

            val leadService = mock[LeadService]
            leadService.create(any[Lead], any)(any[Transaction]) returns Right(testLead)

            val service = getService(
                cohortService = cohortService,
                contactService = contactService,
                leadService = leadService,
                partnerService = partnerService
            )

            val result = service.convertToLead(opportunity, cohort, 1L)(mock[Transaction]).value
            result must beEqualTo(Right(Some(testLead)))
        }
    }
}
