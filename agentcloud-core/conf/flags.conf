# Contains feature flags used by the application

eap {
    flags {
        # The frequency in milliseconds determining how often the active call should be polled.
        # NOTE: If 0, no polling will occur
        activeCallPollFrequency = 0

        # Allow a campaign code to be sent to the Lumico hand-off API
        allowHandoffRequestCampaignCode = false

        # Makes Paylocity company id field visible in AgentCloud
        allowPaylocityCompanyType = false

        # Makes Paylocity Sync button visible and able to sync the user profile with Paylocity
        allowPaylocitySync = false

        # Allows the use of the new PR leads columns instead of aaa.main_data
        allowNewPRLeadColumns = false

        # Allows the appointments tab
        allowAppointmentsTab = false

        # Allow Healthcare and AccuQuote U65 product type
        allowUnder65 = true

        # Allow Healthcare ReliaShield product type
        allowHCReliaShield = false

        # Allow Healthcare Hospital Indemnity product type
        allowHospitalIndemnity = false

        # Allow language selection in Modular Journeys
        allowLanguageSelection = false

        #Allow the product selection view to "paginate".
        allowU65Pagination = false

        # Show the new policy details page for the opportunities
        allowNewPolicyDetailsTab = true

        # Show the Journey Editor page
        allowJourneyEditorView = false

        # Admin assignment based on agency
        agencyAdminAssignment = true

        # Show the "Go To Summary" button for the Conversion product
        disableOldPolicyDetailsTab = true
    }
}
