# Configuration settings for external integrations

# Most integration settings should go in here and should roughly map to the package names.
# Try to keep these in alphabetical order at the top level so people can find what they're
# looking for.
com.elagy.integrations {
    agentcloud.api {
        homebridgeUser = "homebridge"
        insuracyUser = "insuracy"
        insuracyDefaultUrl = "https://www.insuracy.com/"
    }

    agentsync {
        authUrl = "https://auth.sandbox.agentsync.io/oauth2/token"
        domain = "https://api.sandbox.agentsync.io/v1"

        aq {
            key = ""
            secret = ""
        }

        hc {
            key = ""
            secret = ""
        }
    }

    callminer {
        securityDomain = "https://sapicm.callminer.net"
        dataDomain = "https://feapicm.callminer.net"
        ingestionDomain = "https://api.callminer.net/ingestion"
        idDomain = "https://idp.callminer.net"

        credentials {
            username = ""
            password = ""
            tenant = ""
        }

        ingestionCredentials {
            clientId = "changeme"
            clientSecret = "changeme"
            grantType = "client_credentials"
            scope = "https://callminer.net/auth/platform-ingestion"
        }

        sourceId = "AgentCloud_iDev"
        threadSleepTime = 60000 # In milliseconds
    }

    csg {
        tokenEndpoint = "https://csgapi.appspot.com/v1/auth.json"
        quoteEndpoint = "https://csgapi.appspot.com/v1/final_expense_life/quotes.json"
        cacheExpiration = 1 # In hours
        maxRetries = 1
        apiKey = "this-is-an-api-key"
        maxLogSize = 250000 # Don't log CSG responses greater than 250 kB
        enabled = true
    }

    fidelity {
        endpoint = "https://singlesignon.fidelitylife.com/Account/Login?ReturnUrl=https%3A%2F%2Fflex.fidelitylife.com%2F"
    }

    five9 {
        apiUrl = "https://app-scl.five9.com"
        soapUrl = "https://api.five9.com:443/wsadmin/v10_2/AdminWebService"
        tokenExpiration = 12 # Hours
        tcpaRecording = "QC4YouWillBeCalled"
        tcpaRecordingHomeBridge = "HomeBridge_TCPARec"
        homebridgeOutboundCampaign = "HomeBridge SI Term Insurance Mainline"
        campaignId = null # Ordinarily, you don't specify a campaign when making outbound calls.
        defaultListId = 1 # TODO Pick a real default dialer list

        # Credentials for the SOAP gateway
        apiCredentials {
            nonMtm {
                callLogFolder = "Shared Reports"
                callLogReport = "4/1/2019 non-MTM"
                domain = "NonMTM"
                username = "elagyapi"
                password = "changeme"
            }

            mtm {
                callLogFolder = "Shared Reports"
                callLogReport = "4/1/2019 MTM"
                domain = "MTM"
                username = "elagyapi"
                password = "changeme"
            }
        }

        autodialList {
            # Maximum number of records that can be modified per request.
            maxRecordsPerRequest = 100
            timeout = 60 # seconds - time for Five9 to process the data that was sent from the CSV or update task before checking results
            reportEmail = "<EMAIL>" # The e-mail address that will receive autodial list import notifications
            regularListName = "RegularOutbound" # The name of the list into which non-HB autodial calls will be inserted
            homebridgeListName = "HomeBridgeOutbound" # The name of the list into which HB autodial calls will be inserted
            # Number of milliseconds to wait between requests
            delay = 3000
            maxLeadOpps = 18000

            deleteDispositions = [ # The AQ disposition names for a call's lead to remove related phone numbers.
                "Bad Telemarketing",
                "Bogus Lead",
                "DNC",              # AQ uses the "DNC" disposition
                "Do Not Call",      # "Do Not Call" is a system disposition
                "Ineligible",
                "Invalid Number",
                "Not Interested",
                "PI - Contact Reached",
                "Contact in Future"
            ]

            deleteDispositionsHC = [ # The HC disposition names for a call's lead to remove related phone numbers.
                "49. Working with external Broker or Carrier",
                "50. Wrong Number",
                "60. No Disposition -ACW-",
                "41. Transfer - Customer Service",
                "40. Transfer - Broker Management",
                "43. Transfer - Overflow partners",
                "45. Unable to Pay",
                "46. Upset Customer",
                "00. Add to DNC",
                "02. Already Purchased",
                "05. Customer Busy",
                "06. Customer did not request quotes",
                "32. Refused to be Transferred",
                "14. Foreign Customer",
                "17. Invalid Number",
                "21. Language Issue",
                "24. Not eligigle for MedSup",
                "13. Not eligigle for MedSup",
                "25. Not Interested - Already Enrolled",
                "26. Not interested - Out of budget",
                "11. Failed Medical Questionnaire",
                "12. Failed Underwritting",
                "27. Not interested - with our products",
                "28. Not interested - Medicaid Elegible",
                "80. IVR.OVERFLOW",
                "90. IVR.BLACKLISTED",
                "Resource Unavailable",
                "Ringback",
                "Operator Intercept",
                "Dial Error",
                "Do Not Call",
                "S10. Error",
                "Fax",
                "S15. Forwarded",
                "Forwarded",
                "Hardware Timeout",
                "No Response From Caller",
                "3rd Party Transfer",
                "37. Spam",
                "Spam",
                "System Error",
                "Transferred To 3rd Party"
            ]

            # How long to wait before sending another notification about an opportunity, measured in hours
            notificationTimeout = 24

            # Notifications for list management failures
            notifications {
                sender = ${application.email.system}
                recipient = ${application.email.support}
                enabled = ${application.email.notifyOnError}
            }
        }

        callLogging {
            reportTimeout = 90 # The call log report timeout limit in seconds.
            task {
                timeout = 15 # Minutes
                interval = 60 # Minutes
                enabled = false

                notifications {
                    sender = ${application.email.system}
                    recipient = ${application.email.support}
                    enabled = ${application.email.notifyOnError}
                }

                organizations = [1, 2]
            }
        }

        dnc {

            fetch {
                interval = 60 # The fetch interval is in minutes.
                enabled = false
                reportTimeout = 90 # The fetch report timeout limit in seconds.

                folderName = "Do Not Call Reports"
                reportName = "Daily DNC List"

                taskTimeout = 30 # The amount of time in minutes to wait for a result from an DNC fetch request.

                # Controls how error notifications are handled for the fetch task
                notifications {
                    sender = ${application.email.system}
                    recipient = ${application.email.support}
                    enabled = false
                }

                organizations = [1, 2]
            }

            add {
                enabled = false
                taskTimeout = 30 # The amount of time in minutes to wait for a result from an DNC add request.
                maxRecordsPerRequest = 25000

                # Controls how error notifications are handled for the add task
                notifications {
                    sender = ${application.email.system}
                    recipient = ${application.email.support}
                    enabled = ${application.email.notifyOnError}
                }

                organizations = [1, 2]
            }
        }

        callstate {
            reportTimeout = 90 # Timeout limit in seconds to read report.
            folderName = "Shared Reports"
            reportName = "StateDetailReport"

            task {
                timeout = 35 # Minutes
                interval = 120 # Minutes
                enabled = true

                notifications {
                    sender = ${application.email.system}
                    recipient = ${application.email.support}
                    enabled = ${application.email.notifyOnError}
                }

                organizations = [1, 2]
            }
        }

        # The cache key for whether the application currently has paused lead uploads to the dialer lists.
        dialerUploads {
            pausedKey = "Five9-Uploads-Paused"
            pausedExpiration = 30 # The time to expire the paused lead uploads in minutes.
        }

        campaignManagement {
            cacheKey = "DialerCampaigns"
            notifications {
                sender = ${application.email.system}
                recipient = ${application.email.support}
                # Don't ever turn this off
                enabled = true
            }
        }
    }

    globe {
        endpoint = "https://www.globelifedirect.com/?company=ACCU001"
    }

    healthcare {
        middleware {
            authUrl = "https://dev-19796454.okta.com/oauth2/default/v1/token"
            domain = "https://api.qa.healthcare.com/plans-middleware"
            clientId = "0oa4cupatjOuGpQHH5d7"
            clientSecret = ""
            cacheExpiration = 55 # minutes
        }
    }

    ixn {
        appId = "changeme"
        appToken = "changeme"
        apiUrl = "https://api.ixn.tech/v1"
        enabled = true
    }

    journey {
        url = "http://journey-service/v1"
    }

    lumico {
        enabled = true
        # The strings are state abbreviation in eap.states.
        finalExpenseStates = []
    }

    newbenefits {
        endpoint = "https://acq001.secureenrollment.com"
    }

    psg {
        siteKey = "changeme",
        endpoint = "https://test.paperlessolutions.net/ffxml"
    }

    reliashield {
        endpoint = "https://agentlink.reliashield.com/account/login"
    }

    tcpa.jornaya {
        url = "https://api.leadid.com/SingleQuery"
        enabled = true
        entityCode = "changeme"
        test = true
    }

    google {
        # Credentials for accessing Jaroop's Google Drive. Comment out if you are using Accuquote's credentials instead.
        # Credential configuration is described here: https://github.com/jaroop/elagy-agency-platform/wiki/Google
        # developerKey: "AIzaSyA4ILgNn8t0WyjYwjhigcA8Z6GqL2OW0zU"
        # clientId: "************-28aplv516j59iik2rclj3lcib1i3pp73.apps.googleusercontent.com"
        # parentFolderId: "0AP6Tw_0TKXRlUk9PVA"

        # Credentials for accessing Accuquote's Google Drive. Comment out if you are using Jaroop's credentials instead.
        # Credential configuration is described here: https://github.com/jaroop/elagy-agency-platform/wiki/Google
        developerKey: "AIzaSyB1k168ZM4pQyAONaSVWv6xiJF0dhIVmfk"
        clientId: "************-52fge97vi6sg64ovuogmucknmev13svc.apps.googleusercontent.com"
        parentFolderId: "1uMEQGXZgmRzZhtiyrS0sLanVK2zqKndz"
    }

    apps {
        endpoint = "https://www.integratedtestingservices.com/appsacord/acordwebs.asmx"
        username = "AccuQuote"
        password = "TestQA324e"
        schedulerUrl = "https://www.integratedtestingservices.com/accuquote/Schedule.aspx"

        # This should be a base64-encoded 256 bit key
        schedulerSecret = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="
        # this should be a base64-encoded 128 bit initialization vector
        schedulerIv = "AAAAAAAAAAAAAAAAAAAAAA=="

        # flag to control whether the zip code should be overriden with the test zip
        isTest = true
    }

    neverbounce {
        # The url for the NeverBounce API
        url = "https://api.neverbounce.com/v4"
        # The key for the NeverBounce API
        apiKey = "changeme"
    }

    integriant {
        # The url for the Integriant API
        url = "https://integriant.leadspediatrack.com/post.do"
        # Determines if the lead POST is a test (should only be false for Production)
        isTest = true
        # The id of the campaign
        campaignId = "changeme"
        # The key for the campaign
        campaignKey = "changeme"

        # The AgentCloud lead campaigns to check for a POST
        leadCampaigns = ["AQLiveLifeWebLeads", "FE AQLiveLifeWebLeads"]

        # The state abbreviations for states to check for a POST.
        states = [
            "AZ",
            "FL",
            "GA",
            "IL",
            "IN",
            "MD",
            "MI",
            "NC",
            "NJ",
            "OH",
            "PA",
            "SC",
            "TN",
            "TX",
            "UT",
            "VA"
        ]
    }

    mediaAlpha {
        pingUrl = "https://insurance-test.mediaalpha.com/ping.json"
        postUrl = "https://insurance-test.mediaalpha.com/post.json"

        apiToken = "i-am-an-api-token"
        placementId = "i-am-a-placement-id"
        apiVersion = 18
    }

    paylocity {
        authUrl = "https://apisandbox.paylocity.com/IdentityServer/connect/token"
        baseUrl = "https://api.paylocity.com/api/v2"
        w2CompanyId = "i-am-a-w2-company-id"
        contractorCompanyId = "i-am-a-w2-company-id"
        clientId = "i-am-a-client-id"
        clientSecret = "i-am-a-client-secret"
    }

    blaze {
        baseUrl = "https://qa-sunfire.sunfirematrix.com/api/prospect"
    }

    barrington {
        sftp {
            hostname = "changeme"
            port = 2222
            username = "changeme"
            password = "changeme"
            enableCSVLogging = false
            # Attaches a postfix for the report file. Should be null unless we want to differenitate files from
            # multiple environments to one CrushFTP instance, ex QA and Dev to CrushFTP staging.
            filePostfix = null
            partnerName = "Barrington Media Group"
        }
    }

    smarts {
        url = "https://smarts.dev.aq.elagy.com/DecisionServer/decision-services"
        appId = "changeme"
        key = "changeme"
        username = "changeme"
        password = "changeme"
        cacheExpiration = 1380 # minutes, 23 hours
        workspace = "Top/Local"
        deploymentId = "changeme"
        decisionId = "changeme"
        enableSensitiveLogging = false
    }
}

com.elagy.lumico {
    # These are config variables for the provided `siteKey` in Lumico's system.
    # Each is associated with book of business / agency and informs Lumico about available products and commissions.
    # The siteKey also tells Lumico to which endpoint status updates should be sent.
    siteKey = "changeme"
    siteKeyHomebridge = "changeme"
    apiKey = "changeme"

    # These are config variables for the provided `agentId` in Lumico's system. Both refer to an agent id on Lumico's side.
    agentId = "changeme"
    agentIdHomebridge = "changeme"

    quote {
        endpoint = "https://api-uat-ext.iptiqamericas.com/ws/rest/v1/products/quotes"
    }
    handoff {
        endpoint = "https://api-uat-ext.iptiqamericas.com/ws/rest/v1/products/applications"
    }

    # Parameters for the OpenID implementation which is needed in order to send any Lumico data.
    openid {
        authorization {
            endpoint = "https://api-testauth-ext.iptiqamericas.com/auth/realms/8d11f5af-8cd0-4fcf-adb8-e96fa1a03a82/protocol/openid-connect/auth"
            clientId = "8d11f5af-8cd0-4fcf-adb8-e96fa1a03a82:OPENID"
        }
        access {
            endpoint = "https://api-testauth-ext.iptiqamericas.com/auth/realms/8d11f5af-8cd0-4fcf-adb8-e96fa1a03a82/protocol/openid-connect/token"
        }
    }

    # The list of campaign names that should submit a Campaign Code in the Lumico handoff.
    campaignNames = [
        "FE Lumico Blended",
        "FE Lumico Blended - Overflow",
        "FE Lumico Blended - Spillback"
    ]
}

com.elagy.tcpa {
    url = "https://api.leadid.com/SingleQuery"
    test = false
}
