package com.elagy.agentcloud.api

import cats.data.NonEmptyList
import com.jaroop.core.libs._
import com.jaroop.core.datatypes._
import com.jaroop.core.datatypes.JsonImplicits._
import com.jaroop.core.libs.JsImplicits._
import java.time._
import play.api.libs.functional.syntax._
import play.api.libs.json.Reads._
import play.api.libs.json._
import squants.market.{ Money, USD }
import squants.market.MoneyConversions._
import squants.mass.{ Mass, Pounds }
import squants.space.{ Inches, Length }

/** Represents a Lead JSON object sent by an user of the Lead API.
 *  @param clientRef A field to be used by the client for referencing the information that the lead came from.
 *  @param contact The information of the contact associated with the lead.
 *  @param policyDetails The details of the policy for the lead.
 *  @param mortgageDetails The details of the mortgage that are associated with the lead, if applicable.
 *  @param clientIp The ip address where the lead came from.
 *  @param referringUrl The url where the lead came from.
 *  @param tcpaToken A tcpa token that is associated with the lead.
 *  @param campaignName A string designating the campaign this lead belongs to.
 *  @param companion A lead companion that is directly associated with the lead.
 *  @param id The `leadId` that was originally returned from the create request to the AgentCloud API.
 *  @param product The product type of the lead in the AgentCloud system.
 *  @param sessionId The API session through which the lead was created
 *  @param trustedFormCert This is for HC users to send us leads. Has its values as URLs
 */
case class Lead(
    clientRef: String,
    contact: LeadContact,
    policyDetails: Option[LeadDetails],
    mortgageDetails: Option[MortgageDetails],
    clientIp: Option[IpAddress],
    referringUrl: Option[String],
    tcpaToken: Option[String],
    campaignName: Option[String],
    companion: Option[LeadCompanion],
    id: Option[Long],
    leadMedicareDetails: Option[LeadMedicareDetails],
    product: Option[Product],
    sessionId: Option[String],
    trustedFormCert: Option[String]
)

object Lead {
    implicit val reads: Reads[Lead] = (
        (__ \ "clientRef").read[String]
            .filter(JsonValidationError("clientRef should not be empty"))(_.trim.nonEmpty) and
        (__ \ "contact").read[LeadContact] and
        (__ \ "policyDetails").readNullable[LeadDetails] and
        (__ \ "mortgageDetails").readNullable[MortgageDetails] and
        (__ \ "clientIp").readNullable[IpAddress]
            .orElse(Reads(_ => JsError(JsonValidationError("clientIp should be a valid IPv4 or IPv6 address")))) and
        (__ \ "referringUrl").readNullable[String] and
        (__ \ "tcpaToken").readNullable[String]
            .filter(JsonValidationError("tcpaToken should not be empty"))(_.forall(_.trim.nonEmpty)) and
        (__ \ "campaignName").readNullable[String] and
        (__ \ "companion").readNullable[LeadCompanion] and
        (__ \ "id").readNullable[Long] and
        (__ \ "medicareDetails").readNullable[LeadMedicareDetails] and
        (__ \ "product").readNullable[Product] and
        (__ \ "sessionId").readNullable[String](maxLength[String](124)) and
        (__ \ "trustedFormCert").readNullable[String]
    )(Lead.apply _)

    implicit val writes: Writes[Lead] = (
        (__ \ "clientRef").write[String] and
        (__ \ "contact").write[LeadContact] and
        (__ \ "policyDetails").writeNullable[LeadDetails] and
        (__ \ "mortgageDetails").writeNullable[MortgageDetails] and
        (__ \ "clientIp").writeNullable[IpAddress] and
        (__ \ "referringUrl").writeNullable[String] and
        (__ \ "tcpaToken").writeNullable[String] and
        (__ \ "campaignName").writeNullable[String] and
        (__ \ "companion").writeNullable[LeadCompanion] and
        (__ \ "id").writeNullable[Long] and
        (__ \ "medicareDetails").writeNullable[LeadMedicareDetails] and
        (__ \ "product").writeNullable[Product] and
        (__ \ "sessionId").writeNullable[String] and
        (__ \ "trustedFormCert").writeNullable[String]
    )(unlift(Lead.unapply))
}

/** Represents personal information about the specific contact the lead is about.
 *  @param firstName The first name of the primary contact for the lead.
 *  @param middleName The middle name of the primary contact for the lead.
 *  @param lastName The last name of the primary contact for the lead.
 *  @param dateOfBirth The birth date of the primary contact.
 *  @param street The primary street address information of the contact.
 *  @param street2 The secondary street address information of the contact.
 *  @param city The city where the contact is from.
 *  @param state The state where the contact is from. Optional as not all lead vendors collect state.
 *  @param zip The zip code of the city of the contact.
 *  @param phones The phone numbers that are associated with the contact.
 *  @param email The primary email address that the contact uses.
 *  @param gender The gender of the contact.
 */
case class LeadContact(
    firstName: String,
    middleName: Option[String],
    lastName: String,
    dateOfBirth: Option[LocalDate],
    street: Option[String],
    street2: Option[String],
    city: Option[String],
    state: Option[State],
    zip: Option[String],
    phones: NonEmptyList[Phone],
    email: Option[EmailAddress],
    gender: Option[Gender]
)

object LeadContact {
    implicit val reads: Reads[LeadContact] =  (
        (__ \ "firstName").read[String]
            .filter(JsonValidationError("firstName should not be empty"))(_.trim.nonEmpty)
            .filter(JsonValidationError("firstName should be less than 125 characters"))(_.length < 125) and
        (__ \ "middleName").readNullable[String]
            .filter(JsonValidationError("middleName should not be empty"))(_.forall(_.trim.nonEmpty))
            .filter(JsonValidationError("middleName should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "lastName").read[String]
            .filter(JsonValidationError("lastName should not be empty"))(_.trim.nonEmpty)
            .filter(JsonValidationError("firstName should be less than 125 characters"))(_.length < 125) and
        (__ \ "dateOfBirth").readNullable[LocalDate]
            .orElse(Reads(_ => JsError(JsonValidationError("dateOfBirth should be a valid ISO formatted date")))) and
        (__ \ "street").readNullable[String]
            .filter(JsonValidationError("street should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "street2").readNullable[String]
            .filter(JsonValidationError("street2 should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "city").readNullable[String]
            .filter(JsonValidationError("city should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "state").readNullable[String]
            .filter(JsonValidationError("state should be a US state abbreviation"))(_.forall(State.withoutTerritoriesMap.contains(_)))
            .map(_.map(State.fromAbbreviationStatesAndDc(_).get)) and
        (__ \ "zip").readNullable[String]
            .filter(JsonValidationError("zip code should be limited to 5-10 characters"))(
                _.forall(zip => zip.length <= 10 && zip.length >= 5)
            ) and
        (__ \ "phones").read[NonEmptyList[Phone]] and
        (__ \ "email").readNullable[EmailAddress]
            .orElse(Reads(_ => JsError(JsonValidationError("email should be a valid email address")))) and
        (__ \ "gender").readNullable[Gender]
            .orElse(Reads(_ => JsError(JsonValidationError("gender should be either be 'Male' or 'Female'"))))
    )(LeadContact.apply _)

    // Serialize the LeadContact model as JSON where `state` is serialized as a string per the API.
    implicit val writes: Writes[LeadContact] = Json.writes[LeadContact]
        .withValue("state", _.state.map(_.abbreviation))
}

/** Represents a phone number that is associated with a contact
 *  @param phoneNumber The phone number itself.
 *  @param phoneType The type of phone that the contact has associated with the number.
 */
case class Phone(
    phoneNumber: PhoneNumber,
    phoneType: PhoneType
)

object Phone {
    implicit val reads: Reads[Phone] = (
        (__ \ "phoneNumber").read[PhoneNumber]
            .orElse(Reads(_ => JsError(JsonValidationError("phoneNumber should be valid phone number")))) and
        (__ \ "phoneType").read[PhoneType]
            .orElse(Reads(_ => JsError(JsonValidationError("phoneType should be 'Cell', 'Home', 'Work', 'Other'"))))
    )(Phone.apply _)

    implicit val writes: Writes[Phone] = Json.writes[Phone]
}

/** The miscellaneous details about the lead and the contact.
 *  @param termLength The term length of the policy the lead has in years.
 *  @param tobacco Whether or not the contact of the policy uses tobacco.
 *  @param riskClass The risk class that the policy is categorized under.
 *  @param optInDateTime The date and time the lead was collected.
 *  @param faceAmount The face amount that the lead is looking to buy in US Dollars.
 *  @param height The height of the lead in inches.
 *  @param weight The weight of the lead in pounds.
 *  @param usCitizen Whether or not the lead is a US citizen.
 *  @param familyStatus The family status of the lead.
 *  @param incomeMonthly The monthly income of the lead in US Dollars.
 */
case class LeadDetails(
    termLength: Option[Int],
    tobacco: Option[Boolean],
    riskClass: Option[RiskClass],
    optInDateTime: Option[OffsetDateTime],
    faceAmount: Option[Money],
    height: Option[Length],
    weight: Option[Mass],
    usCitizen: Option[Boolean],
    familyStatus: Option[FamilyStatus],
    incomeMonthly: Option[Money]
)

object LeadDetails {
    implicit val reads: Reads[LeadDetails] = (
        (__ \ "termLength").readNullable[Int]
            .filter(JsonValidationError("termLength should not be a negative number"))(_.forall(_ > 0)) and
        (__ \ "tobacco").readNullable[Boolean] and
        (__ \ "riskClass").readNullable[RiskClass]
            .orElse(Reads(_ => JsError(JsonValidationError("riskClass should be 'Standard', 'Preferred', 'Preferred Plus'")))) and
        (__ \ "optInDateTime").readNullable[OffsetDateTime]
            .orElse(Reads(_ => JsError(JsonValidationError("optInDateTime should be a valid ISO 8601 formatted date time")))) and
        (__ \ "faceAmount").readNullable[Money](SquantsHelpers.reads(USD))
            .filter(JsonValidationError("faceAmount should not be a negative number"))(_.forall(_ > 0.USD)) and
        (__ \ "height").readNullable[Length](SquantsHelpers.reads(Inches))
            .filter(JsonValidationError("height should be between 12 and 96 inches"))(
                _.forall(height => height.toInches >= 12 && height.toInches < 96)
            ) and
        (__ \ "weight").readNullable[Mass](SquantsHelpers.reads(Pounds))
            .filter(JsonValidationError("weight should be between 50 and 1000 pounds"))(
                _.forall(weight => weight.toPounds >= 50 && weight.toPounds < 1000)
            ) and
        (__ \ "usCitizen").readNullable[Boolean] and
        (__ \ "familyStatus").readNullable[FamilyStatus]
            .orElse(Reads(_ => JsError(JsonValidationError(
                "familyStatus should be 'Single', 'Couple', 'Single w/kids', 'Couple w/kids'"
            )))) and
        (__ \ "incomeMonthly").readNullable[Money](SquantsHelpers.reads(USD))
            .filter(JsonValidationError("incomeMonthly should not be a negative number"))(_.forall(_ > 0.USD))
    )(LeadDetails.apply _)

    implicit val writes: Writes[LeadDetails] = (
        (__ \ "termLength").writeNullable[Int] and
        (__ \ "tobacco").writeNullable[Boolean] and
        (__ \ "riskClass").writeNullable[RiskClass] and
        (__ \ "optInDateTime").writeNullable[OffsetDateTime] and
        (__ \ "faceAmount").writeNullable[Money](SquantsHelpers.writes(USD)) and
        (__ \ "height").writeNullable[Length](SquantsHelpers.writes(Inches)) and
        (__ \ "weight").writeNullable[Mass](SquantsHelpers.writes(Pounds)) and
        (__ \ "usCitizen").writeNullable[Boolean] and
        (__ \ "familyStatus").writeNullable[FamilyStatus] and
        (__ \ "incomeMonthly").writeNullable[Money](SquantsHelpers.writes(USD))
    )(unlift(unapply))
}

/** Contains details about the mortgage information associated with the lead.
 *  @param borrowerId The ID of the borrower for the mortgage.
 *  @param borrowerStatus The latest borrower status for the lead.
 *  @param mortgageId The ID of the mortgage for the lead.
 *  @param mortgageCloseDate The closing date for mortgage.
 *  @param insurancePurpose The purpose for the lead's insurance request.
 *  @param faceAmount The face value of the lead's face.
 *  @param termLength The term length in years of the lead's mortgage.
 *  @param loanOfficerFirstName The first name of the loan officer of the lead.
 *  @param loanOfficerLastName The last name of the loan officer of the lead.
 *  @param loanOfficerPhone The phone number of the loan officer.
 *  @param loanOfficerEmail The email address of the loan officer.
 *  @param processorFirstName The first name of the loan processor of the lead.
 *  @param processorLastName The last name of the loan processor of the lead.
 *  @param processorPhone The phone number of the loan processor.
 *  @param processorEmail The email address of the loan processor.
 */
case class MortgageDetails(
    borrowerId: String,
    borrowerStatus: BorrowerStatus,
    mortgageId: String,
    mortgageCloseDate: LocalDate,
    insurancePurpose: String,
    faceAmount: Money,
    termLength: Int,
    loanOfficerFirstName: String,
    loanOfficerLastName: String,
    loanOfficerPhone: PhoneNumber,
    loanOfficerEmail: EmailAddress,
    processorFirstName: String,
    processorLastName: String,
    processorPhone: PhoneNumber,
    processorEmail: EmailAddress
)

object MortgageDetails {
    implicit val reads: Reads[MortgageDetails] = (
        (__ \ "borrowerId").read[String]
            .filter(JsonValidationError("borrowerId should not be empty"))(_.trim.nonEmpty) and

        (__ \ "borrowerStatus").read[BorrowerStatus]
            .orElse(Reads(_ => JsError(JsonValidationError(
                "borrowerStatus should be 'Open', 'AutoDial', or 'Inactive'"
            )))) and

        (__ \ "mortgageId").read[String]
            .filter(JsonValidationError("mortgageId should not be empty"))(_.trim.nonEmpty) and

        (__ \ "mortgageCloseDate").read[LocalDate]
            .orElse(Reads(_ => JsError(JsonValidationError("mortgageCloseDate should be a valid ISO formatted date time")))) and

        (__ \ "insurancePurpose").read[String]
            .filter(JsonValidationError("insurancePurpose should not be empty"))(_.trim.nonEmpty) and

        (__ \ "faceAmount").read[Money](SquantsHelpers.reads(USD))
            .filter(JsonValidationError("faceAmount should not be a negative number"))(_ > 0.USD) and

        (__ \ "termLength").read[Int]
            .filter(JsonValidationError("termLength should not be a negative number"))(_ > 0) and

        (__ \ "loanOfficerFirstName").read[String]
            .filter(JsonValidationError("loanOfficerFirstName should be less than 125 characters"))(_.length < 125)
            .filter(JsonValidationError("loanOfficerFirstName should not be empty"))(_.trim.nonEmpty) and

        (__ \ "loanOfficerLastName").read[String]
            .filter(JsonValidationError("loanOfficerLastName should be less than 125 characters"))(_.length < 125)
            .filter(JsonValidationError("loanOfficerLastName should not be empty"))(_.trim.nonEmpty) and

        (__ \ "loanOfficerPhone").read[PhoneNumber]
            .orElse(Reads(_ => JsError(JsonValidationError("loanOfficerPhone should be a valid phone number")))) and

        (__ \ "loanOfficerEmail").read[EmailAddress]
            .orElse(Reads(_ => JsError(JsonValidationError("loanOfficerEmail should be a valid email address")))) and

        (__ \ "processorFirstName").read[String]
            .filter(JsonValidationError("processorFirstName should be less than 125 characters"))(_.length < 125)
            .filter(JsonValidationError("processorFirstName should not be empty"))(_.trim.nonEmpty) and

        (__ \ "processorLastName").read[String]
            .filter(JsonValidationError("processorLastName should be less than 125 characters"))(_.length < 125)
            .filter(JsonValidationError("processorLastName should not be empty"))(_.trim.nonEmpty) and

        (__ \ "processorPhone").read[PhoneNumber]
            .orElse(Reads(_ => JsError(JsonValidationError("processorPhone should be a valid phone number")))) and

        (__ \ "processorEmail").read[EmailAddress]
            .orElse(Reads(_ => JsError(JsonValidationError("processorEmail should be a valid email address"))))
    )(MortgageDetails.apply _)

    implicit val writes: Writes[MortgageDetails] = (
        (__ \ "borrowerId").write[String] and
        (__ \ "borrowerStatus").write[BorrowerStatus] and
        (__ \ "mortgageId").write[String] and
        (__ \ "mortgageCloseDate").write[LocalDate] and
        (__ \ "insurancePurpose").write[String] and
        (__ \ "faceAmount").write[Money](SquantsHelpers.writes(USD)) and
        (__ \ "termLength").write[Int] and
        (__ \ "loanOfficerFirstName").write[String] and
        (__ \ "loanOfficerLastName").write[String] and
        (__ \ "loanOfficerPhone").write[PhoneNumber] and
        (__ \ "loanOfficerEmail").write[EmailAddress] and
        (__ \ "processorFirstName").write[String] and
        (__ \ "processorLastName").write[String] and
        (__ \ "processorPhone").write[PhoneNumber] and
        (__ \ "processorEmail").write[EmailAddress]
    )(unlift(unapply))
}

/** Represents a companion lead that is associated with the main lead itself.
 *
 *  @param borrowerId The ID of the borrower that the companion's mortgage is made through.
 *  @param firstName The first name of the companion.
 *  @param lastName The last name of the companion.
 *  @param gender The gender of the companion.
 *  @param state The state where the companion is from. Optional as not all lead vendors collect state.
 *  @param termLength The term length of the policy the companion has in years.
 *  @param dateOfBirth The birth date of the companion.
 *  @param email The primary email address that the companion uses.
 *  @param faceAmount The face amount that the companion is looking to buy in US Dollars.
 *  @param street The primary street address information of the companion.
 *  @param street2 The secondary street address information of the companion.
 *  @param city The city where the companion is from.
 *  @param zip The zip code of the city of the companion.
 *  @param incomeMonthly The monthly income of the companion in US Dollars.
 *  @param phones The phone numbers that are associated with the companion.
 */
case class LeadCompanion(
    borrowerId: String,
    firstName: String,
    lastName: String,
    gender: Option[Gender],
    state: Option[State],
    termLength: Option[Int],
    dateOfBirth: Option[LocalDate],
    email: Option[EmailAddress],
    faceAmount: Option[Money],
    street: Option[String],
    street2: Option[String],
    city: Option[String],
    zip: Option[String],
    incomeMonthly: Option[Money],
    phones: NonEmptyList[Phone]
)

object LeadCompanion {
    implicit val reads: Reads[LeadCompanion] = (
        (__ \ "borrowerId").read[String]
            .filter(JsonValidationError("companion.borrowerId should not be empty"))(_.trim.nonEmpty) and
        (__ \ "firstName").read[String]
            .filter(JsonValidationError("companion.firstName should not be empty"))(_.trim.nonEmpty)
            .filter(JsonValidationError("companion.firstName should be less than 125 characters"))(_.length < 125) and
        (__ \ "lastName").read[String]
            .filter(JsonValidationError("companion.lastName should not be empty"))(_.trim.nonEmpty)
            .filter(JsonValidationError("companion.lastName should be less than 125 characters"))(_.length < 125) and
        (__ \ "gender").readNullable[Gender]
            .orElse(Reads(_ => JsError(JsonValidationError("companion.gender should be either be 'Male' or 'Female'")))) and
        (__ \ "state").readNullable[String]
            .filter(JsonValidationError("state should be a US state abbreviation"))(_.forall(State.withoutTerritoriesMap.contains(_)))
            .map(_.map(State.fromAbbreviationStatesAndDc(_).get)) and
        (__ \ "termLength").readNullable[Int]
            .filter(JsonValidationError("companion.termLength should not be a negative number"))(_.forall(_ > 0)) and
        (__ \ "dateOfBirth").readNullable[LocalDate]
            .orElse(Reads(_ => JsError(JsonValidationError("companion.dateOfBirth should be a valid ISO formatted date")))) and
        (__ \ "email").readNullable[EmailAddress]
            .orElse(Reads(_ => JsError(JsonValidationError("companion.email should be a valid email address")))) and
        (__ \ "faceAmount").readNullable[Money](SquantsHelpers.reads(USD))
            .filter(JsonValidationError("companion.faceAmount should not be a negative number"))(_.forall(_ > 0.USD)) and
        (__ \ "street").readNullable[String]
            .filter(JsonValidationError("companion.street should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "street2").readNullable[String]
            .filter(JsonValidationError("companion.street2 should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "city").readNullable[String]
            .filter(JsonValidationError("companion.city should be less than 125 characters"))(_.forall(_.length < 125)) and
        (__ \ "zip").readNullable[String]
            .filter(JsonValidationError("zip code should be limited to 5-10 characters"))(
                _.forall(zip => zip.length <= 10 && zip.length >= 5)
            ) and
        (__ \ "incomeMonthly").readNullable[Money](SquantsHelpers.reads(USD))
            .filter(JsonValidationError("companion.incomeMonthly should not be a negative number"))(_.forall(_ > 0.USD)) and
        (__ \ "phones").read[NonEmptyList[Phone]]
    )(LeadCompanion.apply _)

    implicit val writes: Writes[LeadCompanion] = (
        (__ \ "borrowerId").write[String] and
        (__ \ "firstName").write[String] and
        (__ \ "lastName").write[String] and
        (__ \ "gender").writeNullable[Gender] and
        (__ \ "state").writeNullable[State] and
        (__ \ "termLength").writeNullable[Int] and
        (__ \ "dateOfBirth").writeNullable[LocalDate] and
        (__ \ "email").writeNullable[EmailAddress] and
        (__ \ "faceAmount").writeNullable[Money](SquantsHelpers.writes(USD)) and
        (__ \ "street").writeNullable[String] and
        (__ \ "street2").writeNullable[String] and
        (__ \ "city").writeNullable[String] and
        (__ \ "zip").writeNullable[String] and
        (__ \ "incomeMonthly").writeNullable[Money](SquantsHelpers.writes(USD)) and
        (__ \ "phones").write[NonEmptyList[Phone]]
    )(unlift(unapply))
        .withValue("state", _.state.map(_.abbreviation))
}
