package com.elagy.agentcloud.api

import cats.data.NonEmptyList
import cats.syntax.option._
import com.elagy.agentcloud.api._
import com.jaroop.core.datatypes._
import com.jaroop.core.libs.JsImplicits._
import java.time.{ LocalDate, OffsetDateTime, ZoneOffset }
import org.specs2.mutable._
import play.api.libs.json.Json
import squants.market.MoneyConversions._
import squants.space.LengthConversions._
import squants.mass.MassConversions._

class LeadSpec extends Specification {

    "The API Lead" should {
        val testPayloadFull = Json.parse("""
            {
                "clientRef": "607",
                "contact": {
                    "zip": "06501",
                    "firstName": "Mark",
                    "lastName": "Trofdir",
                    "gender": "Male",
                    "city": "New Haven",
                    "street": "11 State Street",
                    "phones": [
                        {
                            "phoneNumber": "(*************",
                            "phoneType": "Cell"
                        },
                        {
                            "phoneNumber": "(*************",
                            "phoneType": "Home"
                        },
                        {
                            "phoneNumber": "(*************",
                            "phoneType": "Work"
                        },
                        {
                            "phoneNumber": "(*************",
                            "phoneType": "Other"
                        }
                    ],
                    "middleName": "Allen",
                    "dateOfBirth": "1985-08-30",
                    "street2": "Suite 5",
                    "state": "CT",
                    "email": "<EMAIL>"
                },
                "policyDetails": {
                    "termLength": 30,
                    "tobacco": true,
                    "riskClass": "Standard",
                    "optInDateTime": "2019-07-01T12:00:00Z",
                    "faceAmount": 100000,
                    "height": 72,
                    "weight": 190,
                    "usCitizen": true,
                    "familyStatus": "Couple",
                    "incomeMonthly": 80000
                },
                "mortgageDetails": {
                    "borrowerId": "51",
                    "borrowerStatus": "Inactive",
                    "mortgageId": "example_51",
                    "mortgageCloseDate": "2040-04-01",
                    "insurancePurpose": "Mortgage protection",
                    "faceAmount": 200000,
                    "termLength": 30,
                    "loanOfficerFirstName": "Kyle",
                    "loanOfficerLastName": "Marsden",
                    "loanOfficerPhone": "(*************",
                    "loanOfficerEmail": "<EMAIL>",
                    "processorFirstName": "Lauren",
                    "processorLastName": "Gambino",
                    "processorPhone": "(*************",
                    "processorEmail": "<EMAIL>"
                },
                "clientIp": "***********",
                "referringUrl": "https://www.someurl.com",
                "tcpaToken": "yes",
                "campaignName": "HomeBridge SI Term Referral",
                "companion": {
                    "zip": "06501",
                    "incomeMonthly": 60000,
                    "borrowerId": "51",
                    "lastName": "Trofdir",
                    "gender": "Female",
                    "city": "New Haven",
                    "phones": [
                        {
                            "phoneNumber": "(*************",
                            "phoneType": "Cell"
                        }
                    ],
                    "dateOfBirth": "1986-01-10",
                    "faceAmount": 100000,
                    "firstName": "Jasmine",
                    "termLength": 15,
                    "street": "11 State Street",
                    "state": "CT",
                    "street2": "Suite 5",
                    "email": "<EMAIL>"
                },
                "medicareDetails": {
                    "medicareType": "Medicare Supplement",
                    "medicareCoverage": "AB"
                },
                "product": "FE",
                "sessionId": "12345",
                "trustedFormCert": "http://example.com"
            }
        """)

        val leadObject = Lead(
            clientRef = "607",
            contact = LeadContact(
                firstName = "Mark",
                middleName = Some("Allen"),
                lastName = "Trofdir",
                dateOfBirth = Some(LocalDate.of(1985, 8, 30)),
                street = Some("11 State Street"),
                street2 = Some("Suite 5"),
                city = Some("New Haven"),
                state = Some(State.Connecticut),
                zip = Some("06501"),
                phones = NonEmptyList.of(
                    Phone(
                        phoneNumber = PhoneNumber.unsafe("8605551234"),
                        phoneType = PhoneType.Cell
                    ),
                    Phone(
                        phoneNumber = PhoneNumber.unsafe("8605551235"),
                        phoneType = PhoneType.Home
                    ),
                    Phone(
                        phoneNumber = PhoneNumber.unsafe("8605551236"),
                        phoneType = PhoneType.Work
                    ),
                    Phone(
                        phoneNumber = PhoneNumber.unsafe("8605551237"),
                        phoneType = PhoneType.Other
                    )
                ),
                email = Some(EmailAddress.unsafe("<EMAIL>")),
                gender = Some(Gender.Male)
            ),
            policyDetails = Some(LeadDetails(
                termLength = Some(30),
                tobacco = Some(true),
                riskClass = Some(RiskClass.Standard),
                optInDateTime = Some(OffsetDateTime.of(2019, 7, 1, 12, 0, 0, 0, ZoneOffset.UTC)),
                faceAmount = Some(100000.USD),
                height = Some(72.inches),
                weight = Some(190.pounds),
                usCitizen = Some(true),
                familyStatus = Some(FamilyStatus.Couple),
                incomeMonthly = Some(80000.USD)
            )),
            mortgageDetails = Some(MortgageDetails(
                borrowerId = "51",
                borrowerStatus = BorrowerStatus.Inactive,
                mortgageId = "example_51",
                mortgageCloseDate = LocalDate.of(2040, 4, 1),
                insurancePurpose = "Mortgage protection",
                faceAmount = 200000.USD,
                termLength = 30,
                loanOfficerFirstName = "Kyle",
                loanOfficerLastName = "Marsden",
                loanOfficerPhone = PhoneNumber.unsafe("2035558888"),
                loanOfficerEmail = EmailAddress.unsafe("<EMAIL>"),
                processorFirstName = "Lauren",
                processorLastName = "Gambino",
                processorPhone = PhoneNumber.unsafe("2035558888"),
                processorEmail = EmailAddress.unsafe("<EMAIL>")
            )),
            clientIp = Some(IpAddress.unsafe("***********")),
            referringUrl = Some("https://www.someurl.com"),
            tcpaToken = Some("yes"),
            campaignName = Some("HomeBridge SI Term Referral"),
            companion = Some(LeadCompanion(
                borrowerId = "51",
                firstName = "Jasmine",
                lastName = "Trofdir",
                gender = Some(Gender.Female),
                state = Some(State.Connecticut),
                termLength = Some(15),
                dateOfBirth = Some(LocalDate.of(1986, 1, 10)),
                email = Some(EmailAddress.unsafe("<EMAIL>")),
                faceAmount = Some(100000.USD),
                street = Some("11 State Street"),
                street2 = Some("Suite 5"),
                city = Some("New Haven"),
                zip = Some("06501"),
                incomeMonthly = Some(60000.USD),
                phones = NonEmptyList.of(
                    Phone(
                        phoneNumber = PhoneNumber.unsafe("8605551234"),
                        phoneType = PhoneType.Cell
                    )
                )
            )),
            id = None,
            leadMedicareDetails = Some(LeadMedicareDetails(Some(MedicareType.Supplement), Some(MedicareCoverage.PartAB))),
            product = Some(Product.FE),
            sessionId = Some("12345"),
            trustedFormCert = "http://example.com".some
        )

        "read a JSON payload as an ApiLead" >> {
            "as a full ApiLead" in {
                testPayloadFull.validate[Lead].toEither must beRight(leadObject)
            }

            "rejecting leads from US territories" in {
                State.territories.map { territory =>
                    val badLead = Json.toJson(leadObject.copy(contact = leadObject.contact.copy(state = Some(territory))))
                    badLead.validate[Lead].toEither must beLeft
                }
            }
        }

        "serialize the Lead" >> {
            "as JSON" in {
                Json.toJson(leadObject) must beEqualTo(testPayloadFull)
            }
        }
    }
}
