# v5.94.1 (Thu May 22 2025)

#### 🐛 Bug Fix

- [AC-4735](https://salessolutions.atlassian.net/browse/AC-4735): Provide Ability to Accept/Reject Duplicate Leads Based on Timemeframe [#8192](https://github.com/jaroop/agentcloud/pull/8192) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.94.0 (Wed May 21 2025)

#### 🚀 Enhancement

- [AC-4686](https://salessolutions.atlassian.net/browse/AC-4686): Change contact callback task due date for U65 [#8191](https://github.com/jaroop/agentcloud/pull/8191) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.93.7 (Thu May 15 2025)

#### 🐛 Bug Fix

- [AC-4713](https://salessolutions.atlassian.net/browse/AC-4713): Retroactively Fix Universal Journey to Update Prefill JSON [#8186](https://github.com/jaroop/agentcloud/pull/8186) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.93.6 (Tue May 13 2025)

#### 🐛 Bug Fix

- [AC-4686](https://salessolutions.atlassian.net/browse/AC-4686): Under65 journey prefills [#8185](https://github.com/jaroop/agentcloud/pull/8185) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.93.5 (Fri May 09 2025)

#### 🐛 Bug Fix

- [AC-4315](https://salessolutions.atlassian.net/browse/AC-4315): Fix for View dialer list permission mismatch [#8181](https://github.com/jaroop/agentcloud/pull/8181) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.93.4 (Fri May 09 2025)

#### 🐛 Bug Fix

- [AC-4731](https://salessolutions.atlassian.net/browse/AC-4731): Add new Abacus fields [#8180](https://github.com/jaroop/agentcloud/pull/8180) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.93.3 (Fri May 09 2025)

#### 🐛 Bug Fix

- [AC-4727](https://salessolutions.atlassian.net/browse/AC-4727): Add Abacus dialer list [#8179](https://github.com/jaroop/agentcloud/pull/8179) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.93.2 (Fri May 09 2025)

#### 🐛 Bug Fix

- [AC-4315](https://salessolutions.atlassian.net/browse/AC-4315): Add Ability to Create Dialer Lists in the Admin panel [#8173](https://github.com/jaroop/agentcloud/pull/8173) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.93.1 (Fri May 09 2025)

#### 🐛 Bug Fix

- [AC-4722](https://salessolutions.atlassian.net/browse/AC-4722): Add Abacus email branding [#8178](https://github.com/jaroop/agentcloud/pull/8178) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.93.0 (Tue May 06 2025)

#### 🚀 Enhancement

- [AC-4723](https://salessolutions.atlassian.net/browse/AC-4723): Add business unit for Abacus [#8174](https://github.com/jaroop/agentcloud/pull/8174) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.92.9 (Fri May 02 2025)

#### 🐛 Bug Fix

- Allow U65 flag on in QA2 for demo [#8171](https://github.com/jaroop/agentcloud/pull/8171) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.92.8 (Fri May 02 2025)

#### 🐛 Bug Fix

- [AC-2067](https://salessolutions.atlassian.net/browse/AC-2067): Eliminate GraphQL errors in the logs [#8170](https://github.com/jaroop/agentcloud/pull/8170) ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))

#### Authors: 1

- Roslin Leon Matias ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))

---

# v5.92.7 (Thu May 01 2025)

#### 🐛 Bug Fix

- [AC-4692](https://salessolutions.atlassian.net/browse/AC-4692): Add scripting to the contact selection page for product type U65 [#8165](https://github.com/jaroop/agentcloud/pull/8165) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.92.6 (Wed Apr 30 2025)

#### 🐛 Bug Fix

- [AC-4682](https://salessolutions.atlassian.net/browse/AC-4682): Set Kotlin Logger [#8164](https://github.com/jaroop/agentcloud/pull/8164) ([@abelramosgelover](https://github.com/abelramosgelover))

#### Authors: 1

- [@abelramosgelover](https://github.com/abelramosgelover)

---

# v5.92.5 (Tue Apr 29 2025)

#### 🐛 Bug Fix

- Turn off u65 feature flag in qa2 for testing [#8166](https://github.com/jaroop/agentcloud/pull/8166) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.92.4 (Tue Apr 29 2025)

#### 🐛 Bug Fix

- [AC-4693](https://salessolutions.atlassian.net/browse/AC-4693): Upon converting a U65 lead, look up collation and start journey [#8161](https://github.com/jaroop/agentcloud/pull/8161) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.92.3 (Mon Apr 28 2025)

#### 🐛 Bug Fix

- [AC-4706](https://salessolutions.atlassian.net/browse/AC-4706): Create Dialing List - LifeQuotes [#8162](https://github.com/jaroop/agentcloud/pull/8162) ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))
- Workaround CircleCI path-filtering bug [#8163](https://github.com/jaroop/agentcloud/pull/8163) (jplorier [@jphealthcare](https://github.com/jphealthcare))
- [AC-4702](https://salessolutions.atlassian.net/browse/AC-4702): Trigger build [#8157](https://github.com/jaroop/agentcloud/pull/8157) ([@leaveller](https://github.com/leaveller))
- [AC-4702](https://salessolutions.atlassian.net/browse/AC-4702): Create Dialing List - LifeQuotes with tag release [#8156](https://github.com/jaroop/agentcloud/pull/8156) ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))
- [AC-4702](https://salessolutions.atlassian.net/browse/AC-4702): Create Dialing List - LifeQuotes [#8155](https://github.com/jaroop/agentcloud/pull/8155) ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))

#### Authors: 4

- [@jphealthcare](https://github.com/jphealthcare)
- [@leaveller](https://github.com/leaveller)
- Juan Pablo Lorier (jplorier)
- Roslin Leon Matias ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))

---

# v5.92.2 (Thu Apr 24 2025)

#### 🐛 Bug Fix

- [AC-4697](https://salessolutions.atlassian.net/browse/AC-4697): Create Dialer List [#8146](https://github.com/jaroop/agentcloud/pull/8146) ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))
- [AC-4698](https://salessolutions.atlassian.net/browse/AC-4698): Add Mercata Business Unit [#8151](https://github.com/jaroop/agentcloud/pull/8151) ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))

#### Authors: 1

- Roslin Leon Matias ([@DevFSLeonMatias](https://github.com/DevFSLeonMatias))

---

# v5.92.1 (Tue Apr 22 2025)

#### 🐛 Bug Fix

- [AC-4695](https://salessolutions.atlassian.net/browse/AC-4695): Create U65 Business Unit [#8143](https://github.com/jaroop/agentcloud/pull/8143) ([@abelramosgelover](https://github.com/abelramosgelover))
- [AC-3733](https://salessolutions.atlassian.net/browse/AC-3733): Add U65 to Product Enum on Lead API [#8144](https://github.com/jaroop/agentcloud/pull/8144) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- [@abelramosgelover](https://github.com/abelramosgelover)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.92.0 (Tue Apr 22 2025)

#### 🚀 Enhancement

- [AC-4687](https://salessolutions.atlassian.net/browse/AC-4687): U65 Dialer Lists [#8142](https://github.com/jaroop/agentcloud/pull/8142) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.91.4 (Fri Mar 28 2025)

#### 🐛 Bug Fix

- [AC-4591](https://salessolutions.atlassian.net/browse/AC-4591): AgentCloud-Web dependencies UUID [#8129](https://github.com/jaroop/agentcloud/pull/8129) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.91.3 (Thu Mar 27 2025)

#### 🐛 Bug Fix

- [AC-4589](https://salessolutions.atlassian.net/browse/AC-4589): AgentCloud-Web dependencies - upgrade react-select to 5.x.x & remove @types/react-select [#8127](https://github.com/jaroop/agentcloud/pull/8127) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.91.2 (Thu Mar 27 2025)

#### 🐛 Bug Fix

- [AC-4596](https://salessolutions.atlassian.net/browse/AC-4596): AgentCloud-Web dependencies - remove or upgrade @mdx-js/react to 3.x.x [#8126](https://github.com/jaroop/agentcloud/pull/8126) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.91.1 (Tue Mar 25 2025)

#### 🐛 Bug Fix

- [AC-4596](https://salessolutions.atlassian.net/browse/AC-4596): AgentCloud-Web devDependencies - Remove or Upgrade @yarnpkg/pnpify to 4.x.x [#8125](https://github.com/jaroop/agentcloud/pull/8125) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.91.0 (Tue Mar 25 2025)

#### 🚀 Enhancement

- [AC-4593](https://salessolutions.atlassian.net/browse/AC-4593): AgentCloud-Web dependencies Upgrade qrcode.react [#8124](https://github.com/jaroop/agentcloud/pull/8124) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.90.4 (Tue Mar 18 2025)

#### 🐛 Bug Fix

- [AC-4144](https://salessolutions.atlassian.net/browse/AC-4144): Update AgentCloud Quote Page Language [#8122](https://github.com/jaroop/agentcloud/pull/8122) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.90.3 (Tue Mar 18 2025)

#### 🐛 Bug Fix

- [AC-4655](https://salessolutions.atlassian.net/browse/AC-4655): Add Missing Indexes [#8121](https://github.com/jaroop/agentcloud/pull/8121) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.90.2 (Thu Mar 13 2025)

#### 🐛 Bug Fix

- [AC-4004](https://salessolutions.atlassian.net/browse/AC-4004): Prevent sales agents from launching a new application from an existing inforce policy record [#8113](https://github.com/jaroop/agentcloud/pull/8113) ([@somnathcts](https://github.com/somnathcts) [@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@somnathcts](https://github.com/somnathcts)

---

# v5.90.1 (Thu Mar 13 2025)

#### 🐛 Bug Fix

- [AC-4530](https://salessolutions.atlassian.net/browse/AC-4530): Issue fixed in Add Reassign Agent button to Opportunity Records [#8115](https://github.com/jaroop/agentcloud/pull/8115) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.90.0 (Wed Mar 12 2025)

#### 🚀 Enhancement

- [AC-4144](https://salessolutions.atlassian.net/browse/AC-4144): Update AgentCloud Quote Page Language [#8111](https://github.com/jaroop/agentcloud/pull/8111) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### 🐛 Bug Fix

- [AC-4530](https://salessolutions.atlassian.net/browse/AC-4530): Add Reassign Agent button to Opportunity Records [#8110](https://github.com/jaroop/agentcloud/pull/8110) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.89.6 (Thu Mar 06 2025)

#### 🐛 Bug Fix

- [AC-4219](https://salessolutions.atlassian.net/browse/AC-4219): Add Ability to View "Read Only" Dialer Lists in the Admin panel [#8105](https://github.com/jaroop/agentcloud/pull/8105) ([@somnathcts](https://github.com/somnathcts))
- [AC-3421](https://salessolutions.atlassian.net/browse/AC-3421): Improve UI Queue Logging [#8104](https://github.com/jaroop/agentcloud/pull/8104) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 3

- [@somnathcts](https://github.com/somnathcts)
- sunil (<EMAIL>)
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))

---

# v5.89.5 (Thu Mar 06 2025)

#### 🐛 Bug Fix

- [AC-4219](https://salessolutions.atlassian.net/browse/AC-4219): Add Ability to View "Read Only" Dialer Lists in the Admin panel [#8103](https://github.com/jaroop/agentcloud/pull/8103) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.89.4 (Tue Mar 04 2025)

#### 🐛 Bug Fix

- [AC-4598](https://salessolutions.atlassian.net/browse/AC-4598): Rider premium validation issue fixed [#8101](https://github.com/jaroop/agentcloud/pull/8101) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.89.3 (Mon Mar 03 2025)

#### 🐛 Bug Fix

- [AC-4598](https://salessolutions.atlassian.net/browse/AC-4598): Fixed Rider deletion issue [#8092](https://github.com/jaroop/agentcloud/pull/8092) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.89.2 (Thu Feb 27 2025)

#### 🐛 Bug Fix

- [AC-4617](https://salessolutions.atlassian.net/browse/AC-4617): AgentCloud-Core replace any CDN asset URLs per-environment address [#8096](https://github.com/jaroop/agentcloud/pull/8096) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.89.1 (Wed Feb 26 2025)

#### 🐛 Bug Fix

- [AC-4637](https://salessolutions.atlassian.net/browse/AC-4637): AgentCloud assets use one CDN per environment [#8091](https://github.com/jaroop/agentcloud/pull/8091) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.89.0 (Wed Feb 26 2025)

#### 🚀 Enhancement

- [AC-4346](https://salessolutions.atlassian.net/browse/AC-4346): Log Failure Reason to Bad Rows - Rapport SDUR output file [#8089](https://github.com/jaroop/agentcloud/pull/8089) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.88.11 (Tue Feb 25 2025)

#### 🐛 Bug Fix

- [AC-4644](https://salessolutions.atlassian.net/browse/AC-4644): Populate closed dates from backend call [#8090](https://github.com/jaroop/agentcloud/pull/8090) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.88.10 (Fri Feb 21 2025)

#### 🐛 Bug Fix

- [AC-4643](https://salessolutions.atlassian.net/browse/AC-4643): New Policy Details tab not showing the Policy Information [#8086](https://github.com/jaroop/agentcloud/pull/8086) ([@umeshaq](https://github.com/umeshaq))
- [AC-4493](https://salessolutions.atlassian.net/browse/AC-4493): removed in force date from issued policy check [#8085](https://github.com/jaroop/agentcloud/pull/8085) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.88.9 (Wed Feb 19 2025)

#### 🐛 Bug Fix

- [AC-4493](https://salessolutions.atlassian.net/browse/AC-4493): Status Dropdown Points to Policy Details New To Determine if Sections are Complete [#8082](https://github.com/jaroop/agentcloud/pull/8082) ([@jaroopshared](https://github.com/jaroopshared) [@somnathcts](https://github.com/somnathcts))

#### 🔩 Dependency Updates

- TOPS - Update circleci dependencies (new pipeline) [#8062](https://github.com/jaroop/agentcloud/pull/8062) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@jaroopshared](https://github.com/jaroopshared)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@somnathcts](https://github.com/somnathcts)

---

# v5.88.8 (Wed Feb 19 2025)

#### 🐛 Bug Fix

- [AC-4383](https://salessolutions.atlassian.net/browse/AC-4383): Fixed Discard policy state issue [#8079](https://github.com/jaroop/agentcloud/pull/8079) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.88.7 (Tue Feb 18 2025)

#### 🐛 Bug Fix

- fix for [AC-4383](https://salessolutions.atlassian.net/browse/AC-4383) [#8077](https://github.com/jaroop/agentcloud/pull/8077) ([@swoosh1337](https://github.com/swoosh1337))
- [AC-4595](https://salessolutions.atlassian.net/browse/AC-4595): Script for updating rapport ID on calls [#8076](https://github.com/jaroop/agentcloud/pull/8076) ([@leaveller](https://github.com/leaveller))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.88.6 (Mon Feb 17 2025)

#### 🐛 Bug Fix

- [AC-4383](https://salessolutions.atlassian.net/browse/AC-4383): Create Discard Button for Policy Details New Tab [#8074](https://github.com/jaroop/agentcloud/pull/8074) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.88.5 (Mon Feb 17 2025)

#### 🐛 Bug Fix

- [AC-4491](https://salessolutions.atlassian.net/browse/AC-4491): Fixing Validation to Reasons for Non-Approved Decision [#8075](https://github.com/jaroop/agentcloud/pull/8075) ([@debmalya-pal](https://github.com/debmalya-pal) [@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.88.4 (Mon Feb 17 2025)

#### 🐛 Bug Fix

- [AC-4491](https://salessolutions.atlassian.net/browse/AC-4491): Make Read Only View of Current Status Details Section of Policy Details New Tab [#8068](https://github.com/jaroop/agentcloud/pull/8068) ([@swoosh1337](https://github.com/swoosh1337))

#### 🔩 Dependency Updates

- TOPS - Update pre-commit dependencies [#8061](https://github.com/jaroop/agentcloud/pull/8061) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.88.3 (Fri Feb 14 2025)

#### 🐛 Bug Fix

- [AC-4620](https://salessolutions.atlassian.net/browse/AC-4620): Update Policy Detail Tabs on Opportunity Record [#8069](https://github.com/jaroop/agentcloud/pull/8069) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.88.2 (Wed Feb 12 2025)

#### 🐛 Bug Fix

- [AC-4600](https://salessolutions.atlassian.net/browse/AC-4600): Upgrade the datadog agent on lambdas - agentcloud [#8067](https://github.com/jaroop/agentcloud/pull/8067) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- sunil (<EMAIL>)
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))

---

# v5.88.1 (Tue Feb 11 2025)

#### 🐛 Bug Fix

- [AC-4439,CVE-2024](https://salessolutions.atlassian.net/browse/AC-4439/CVE-2024): CVE-2024-49767 - werkzeug - sftp lambda [#8065](https://github.com/jaroop/agentcloud/pull/8065) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.88.0 (Tue Feb 11 2025)

#### 🚀 Enhancement

- [AC-4624](https://salessolutions.atlassian.net/browse/AC-4624): Fix task permissions [#8066](https://github.com/jaroop/agentcloud/pull/8066) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.87.12 (Thu Feb 06 2025)

#### 🐛 Bug Fix

- [AC-4613](https://salessolutions.atlassian.net/browse/AC-4613): Flaky Test: JourneySchemaSpec.scala [#8059](https://github.com/jaroop/agentcloud/pull/8059) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.87.11 (Thu Feb 06 2025)

#### 🐛 Bug Fix

- [AC-4492](https://salessolutions.atlassian.net/browse/AC-4492): QA Test Bugfix: Make Read Only View of Issued Status Details … [#8060](https://github.com/jaroop/agentcloud/pull/8060) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- sunil (<EMAIL>)
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))

---

# v5.87.10 (Wed Feb 05 2025)

#### 🐛 Bug Fix

- [AC-4466](https://salessolutions.atlassian.net/browse/AC-4466): Adding Validation to Reasons for Approved Decision [#8058](https://github.com/jaroop/agentcloud/pull/8058) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.87.9 (Wed Feb 05 2025)

#### 🐛 Bug Fix

- [AC-4466](https://salessolutions.atlassian.net/browse/AC-4466): Add Validation and Save Reasons Details [#8056](https://github.com/jaroop/agentcloud/pull/8056) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.87.8 (Wed Feb 05 2025)

#### 🐛 Bug Fix

- [AC-4546](https://salessolutions.atlassian.net/browse/AC-4546): Create an endpoint to create and send a list of emails [#8053](https://github.com/jaroop/agentcloud/pull/8053) ([@swoosh1337](https://github.com/swoosh1337) [@umeshaq](https://github.com/umeshaq))
- [AC-4548](https://salessolutions.atlassian.net/browse/AC-4548): Add documentation for DOMPurify [#8057](https://github.com/jaroop/agentcloud/pull/8057) ([@leaveller](https://github.com/leaveller))

#### Authors: 3

- [@leaveller](https://github.com/leaveller)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.87.7 (Tue Feb 04 2025)

#### 🐛 Bug Fix

- Make Read Only View of Issued Status Details Section of Policy Details New Tab [#8051](https://github.com/jaroop/agentcloud/pull/8051) ([@swoosh1337](https://github.com/swoosh1337) <EMAIL>)

#### Authors: 2

- sunil (<EMAIL>)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.87.6 (Tue Feb 04 2025)

#### 🐛 Bug Fix

- [AC-4547](https://salessolutions.atlassian.net/browse/AC-4547): Permission Based Access Controls for Tasks [#8054](https://github.com/jaroop/agentcloud/pull/8054) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.87.5 (Tue Feb 04 2025)

#### 🐛 Bug Fix

- [AC-4496](https://salessolutions.atlassian.net/browse/AC-4496): Insert a Row Into the Audit Table When Policy Details Table I… [#8055](https://github.com/jaroop/agentcloud/pull/8055) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.87.4 (Mon Feb 03 2025)

#### 🐛 Bug Fix

- [AC-4575](https://salessolutions.atlassian.net/browse/AC-4575): Permission Based Access Controls for Opportunities [#8050](https://github.com/jaroop/agentcloud/pull/8050) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.87.3 (Wed Jan 29 2025)

#### 🐛 Bug Fix

- [AC-4548](https://salessolutions.atlassian.net/browse/AC-4548): Sanitize HTML inputs with DOMPurify [#8044](https://github.com/jaroop/agentcloud/pull/8044) ([@leaveller](https://github.com/leaveller))

#### 📝 Documentation

- Pipeline - Update PR template [#7981](https://github.com/jaroop/agentcloud/pull/7981) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.87.2 (Wed Jan 29 2025)

#### 🐛 Bug Fix

- [AC-4465](https://salessolutions.atlassian.net/browse/AC-4465): Update Reason on its Category and SubCategory update [#8047](https://github.com/jaroop/agentcloud/pull/8047) ([@debmalya-pal](https://github.com/debmalya-pal))
- [AC-4382](https://salessolutions.atlassian.net/browse/AC-4382): Add read-only section for Applied details [#8046](https://github.com/jaroop/agentcloud/pull/8046) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.87.1 (Tue Jan 28 2025)

#### 🐛 Bug Fix

- [AC-4584](https://salessolutions.atlassian.net/browse/AC-4584): Fix spelling of the word “Addtional” on the Policy Details New tab [#8045](https://github.com/jaroop/agentcloud/pull/8045) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.87.0 (Tue Jan 28 2025)

#### 🚀 Enhancement

- [AC-4465](https://salessolutions.atlassian.net/browse/AC-4465): Add Dropdown Values to Reasons of Current Status Section of Policy Details New Tab [#8041](https://github.com/jaroop/agentcloud/pull/8041) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.86.16 (Tue Jan 28 2025)

#### 🐛 Bug Fix

- [AC-4570](https://salessolutions.atlassian.net/browse/AC-4570): Removing the ETL-Data-Importer [#8042](https://github.com/jaroop/agentcloud/pull/8042) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.86.15 (Fri Jan 24 2025)

#### 🐛 Bug Fix

- [AC-4219](https://salessolutions.atlassian.net/browse/AC-4219): Add Ability to View "Read Only" Dialer Lists in the Admin panel [#7897](https://github.com/jaroop/agentcloud/pull/7897) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.86.14 (Wed Jan 22 2025)

#### 🐛 Bug Fix

- [AC-4495](https://salessolutions.atlassian.net/browse/AC-4495): Insert a Row Into the Audit Table When Policy Details Table Is Updated From UI [#8034](https://github.com/jaroop/agentcloud/pull/8034) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.86.13 (Wed Jan 22 2025)

#### 🐛 Bug Fix

- [AC-4370](https://salessolutions.atlassian.net/browse/AC-4370): Upgrade @hookform/resolvers - Version 3.x.x [#8033](https://github.com/jaroop/agentcloud/pull/8033) ([@umeshaq](https://github.com/umeshaq) [@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.86.12 (Mon Jan 20 2025)

#### 🐛 Bug Fix

- [AC-4434](https://salessolutions.atlassian.net/browse/AC-4434): Display Postponed Date Picker When Postponed is Selected [#8032](https://github.com/jaroop/agentcloud/pull/8032) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.86.11 (Fri Jan 17 2025)

#### 🐛 Bug Fix

- [AC-4475](https://salessolutions.atlassian.net/browse/AC-4475): Create a Button to Access an Opportunity Lead [#8030](https://github.com/jaroop/agentcloud/pull/8030) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.86.10 (Fri Jan 17 2025)

#### 🐛 Bug Fix

- [AC-4553](https://salessolutions.atlassian.net/browse/AC-4553): Adding 'CO' to table "fidelity.quote_rates_non_rop" [#8031](https://github.com/jaroop/agentcloud/pull/8031) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.86.9 (Thu Jan 16 2025)

#### 🐛 Bug Fix

- [AC-4462](https://salessolutions.atlassian.net/browse/AC-4462): Add Ability to Update API User Passwords [#8027](https://github.com/jaroop/agentcloud/pull/8027) ([@debmalya-pal](https://github.com/debmalya-pal))

#### 🔩 Dependency Updates

- [TOPS-1144](https://salessolutions.atlassian.net/browse/TOPS-1144): Upgrade aws-cli orb in circleci [#8029](https://github.com/jaroop/agentcloud/pull/8029) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.86.8 (Wed Jan 15 2025)

#### 🐛 Bug Fix

- [AC-4432](https://salessolutions.atlassian.net/browse/AC-4432): Add Additional Fields for Issued Section [#8028](https://github.com/jaroop/agentcloud/pull/8028) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.86.7 (Wed Jan 15 2025)

#### 🐛 Bug Fix

- [AC-4511](https://salessolutions.atlassian.net/browse/AC-4511): Removed graphiql from production like environments [#8026](https://github.com/jaroop/agentcloud/pull/8026) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.86.6 (Tue Jan 14 2025)

#### 🐛 Bug Fix

- [AC-4467](https://salessolutions.atlassian.net/browse/AC-4467): Add converted policy number field to new policy form [#8025](https://github.com/jaroop/agentcloud/pull/8025) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.86.5 (Tue Jan 14 2025)

#### 🐛 Bug Fix

- [AC-4432](https://salessolutions.atlassian.net/browse/AC-4432): Add Additional Fields for Issued Section [#8022](https://github.com/jaroop/agentcloud/pull/8022) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-4509](https://salessolutions.atlassian.net/browse/AC-4509): Add Attribution Clause to ReadMe in all 3rd party libraries we use in the AgentCloud, Jaroop Core, and Jaroop Core UI code [#8020](https://github.com/jaroop/agentcloud/pull/8020) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 3

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.86.4 (Fri Jan 10 2025)

#### 🐛 Bug Fix

- [AC-4464](https://salessolutions.atlassian.net/browse/AC-4464): Fix reason field labels and placeholders [#8021](https://github.com/jaroop/agentcloud/pull/8021) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.86.3 (Fri Jan 10 2025)

#### 🐛 Bug Fix

- [AC-4464](https://salessolutions.atlassian.net/browse/AC-4464): Add reasons for the approved policy [#8019](https://github.com/jaroop/agentcloud/pull/8019) ([@COtlowski](https://github.com/COtlowski))
- [TOPS-822](https://salessolutions.atlassian.net/browse/TOPS-822): Upgrade alpine containers to 3.19 [#8018](https://github.com/jaroop/agentcloud/pull/8018) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.86.2 (Wed Jan 08 2025)

#### 🐛 Bug Fix

- [AC-4433](https://salessolutions.atlassian.net/browse/AC-4433): Fix save button for approved policy [#8017](https://github.com/jaroop/agentcloud/pull/8017) ([@COtlowski](https://github.com/COtlowski))
- [AC-4534](https://salessolutions.atlassian.net/browse/AC-4534): Remove Centrian Life Insurance Conversion Products [#8013](https://github.com/jaroop/agentcloud/pull/8013) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.86.1 (Wed Jan 08 2025)

#### 🐛 Bug Fix

- [AC-4433](https://salessolutions.atlassian.net/browse/AC-4433): Add status dropdown for approved policy [#8015](https://github.com/jaroop/agentcloud/pull/8015) ([@COtlowski](https://github.com/COtlowski))
- [AC-4374,Group-15](https://salessolutions.atlassian.net/browse/AC-4374/Group-15): Upgrade (Group-15) dotenv and dotenv-expand [#8016](https://github.com/jaroop/agentcloud/pull/8016) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.86.0 (Tue Jan 07 2025)

#### 🚀 Enhancement

- [AC-4169](https://salessolutions.atlassian.net/browse/AC-4169): Remove the Lumico Lambda SFTP - samconfig.toml change [#8010](https://github.com/jaroop/agentcloud/pull/8010) ([@debmalya-pal](https://github.com/debmalya-pal))

#### 🐛 Bug Fix

- [TOPS-1599](https://salessolutions.atlassian.net/browse/TOPS-1599): Lock poetry version to 1.8.5 [#8014](https://github.com/jaroop/agentcloud/pull/8014) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.85.10 (Sat Jan 04 2025)

#### 🐛 Bug Fix

- [AC-4431](https://salessolutions.atlassian.net/browse/AC-4431) [#8009](https://github.com/jaroop/agentcloud/pull/8009) (<EMAIL> [@leaveller](https://github.com/leaveller))
- [AC-4431](https://salessolutions.atlassian.net/browse/AC-4431): Copy Applied Section to Approved and Issued. [#8008](https://github.com/jaroop/agentcloud/pull/8008) (<EMAIL>)

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- Sunil Kumar C S (<EMAIL>)

---

# v5.85.9 (Fri Jan 03 2025)

#### 🐛 Bug Fix

- [AC-4429](https://salessolutions.atlassian.net/browse/AC-4429): Fix the deletion issue with the riders [#8007](https://github.com/jaroop/agentcloud/pull/8007) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.85.8 (Fri Jan 03 2025)

#### 🐛 Bug Fix

- [AC-4429](https://salessolutions.atlassian.net/browse/AC-4429): Fix issue with the rider deletion [#8006](https://github.com/jaroop/agentcloud/pull/8006) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.85.7 (Thu Jan 02 2025)

#### 🐛 Bug Fix

- [AC-4429](https://salessolutions.atlassian.net/browse/AC-4429): Add saving to the policy detail riders [#8001](https://github.com/jaroop/agentcloud/pull/8001) ([@COtlowski](https://github.com/COtlowski))
- [AC-4169](https://salessolutions.atlassian.net/browse/AC-4169): Remove the Lumico Lambda SFTP [#8004](https://github.com/jaroop/agentcloud/pull/8004) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.85.6 (Thu Jan 02 2025)

#### 🐛 Bug Fix

- [AC-4461](https://salessolutions.atlassian.net/browse/AC-4461): | Testing Feedback | Create API Users with Partners and Permissions [#8005](https://github.com/jaroop/agentcloud/pull/8005) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.85.5 (Mon Dec 30 2024)

#### 🐛 Bug Fix

- [AC-4430](https://salessolutions.atlassian.net/browse/AC-4430): Riders validation [#8003](https://github.com/jaroop/agentcloud/pull/8003) ([@swoosh1337](https://github.com/swoosh1337) [@leaveller](https://github.com/leaveller))
- [AC-4430](https://salessolutions.atlassian.net/browse/AC-4430): Add Validation to Riders [#8002](https://github.com/jaroop/agentcloud/pull/8002) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.85.4 (Mon Dec 30 2024)

#### 🐛 Bug Fix

- [AC-4461](https://salessolutions.atlassian.net/browse/AC-4461): Create API Users with Partners and Permissions [#7998](https://github.com/jaroop/agentcloud/pull/7998) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.85.3 (Fri Dec 27 2024)

#### 🐛 Bug Fix

- [AC-4460](https://salessolutions.atlassian.net/browse/AC-4460): Create script to search TCPA expired calls [#7999](https://github.com/jaroop/agentcloud/pull/7999) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.85.2 (Thu Dec 26 2024)

#### 🐛 Bug Fix

- [AC-4318](https://salessolutions.atlassian.net/browse/AC-4318): Add button to access Partner Manager from the Admin Panel [#8000](https://github.com/jaroop/agentcloud/pull/8000) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.85.1 (Fri Dec 20 2024)

#### 🐛 Bug Fix

- [AC-4494](https://salessolutions.atlassian.net/browse/AC-4494): Create Audit Table for Policy Details New Tab [#7993](https://github.com/jaroop/agentcloud/pull/7993) ([@swoosh1337](https://github.com/swoosh1337))

#### 🏎 Performance

- Pipeline - Out of memory issues [#7992](https://github.com/jaroop/agentcloud/pull/7992) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.85.0 (Tue Dec 17 2024)

#### 🚀 Enhancement

- [AC-4507](https://salessolutions.atlassian.net/browse/AC-4507): Fix preferred payment date validation logic [#7991](https://github.com/jaroop/agentcloud/pull/7991) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.84.16 (Fri Dec 13 2024)

#### 🐛 Bug Fix

- [AC-4390](https://salessolutions.atlassian.net/browse/AC-4390): Revert changes [#7989](https://github.com/jaroop/agentcloud/pull/7989) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.84.15 (Fri Dec 13 2024)

#### 🐛 Bug Fix

- [AC-4365](https://salessolutions.atlassian.net/browse/AC-4365): Fix jcore-ui resolution version [#7988](https://github.com/jaroop/agentcloud/pull/7988) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.84.14 (Fri Dec 13 2024)

#### 🐛 Bug Fix

- [AC-4390](https://salessolutions.atlassian.net/browse/AC-4390): Upgrade (group 1) React [#7987](https://github.com/jaroop/agentcloud/pull/7987) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.84.13 (Fri Dec 13 2024)

#### 🐛 Bug Fix

- [AC-4380](https://salessolutions.atlassian.net/browse/AC-4380): Empty Commit to re-trigger pipeline [#7985](https://github.com/jaroop/agentcloud/pull/7985) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.84.12 (Thu Dec 12 2024)

#### 🐛 Bug Fix

- [AC-4380](https://salessolutions.atlassian.net/browse/AC-4380): Fixed issue with null inputs [#7984](https://github.com/jaroop/agentcloud/pull/7984) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Pipeline - Revert #7980 and #7982 [#7983](https://github.com/jaroop/agentcloud/pull/7983) ([@nnovaeshc](https://github.com/nnovaeshc))
- Pipeline - Fix issue introduced by #7980 by setting parameter as a string [#7982](https://github.com/jaroop/agentcloud/pull/7982) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-4380](https://salessolutions.atlassian.net/browse/AC-4380): Save Button on Applied Policy Details [#7962](https://github.com/jaroop/agentcloud/pull/7962) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🏎 Performance

- Pipeline - Out of memory issues [#7980](https://github.com/jaroop/agentcloud/pull/7980) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.84.11 (Wed Dec 11 2024)

#### 🐛 Bug Fix

- [AC-4364](https://salessolutions.atlassian.net/browse/AC-4364): Update css-loader to version 7 [#7975](https://github.com/jaroop/agentcloud/pull/7975) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.84.10 (Wed Dec 11 2024)

#### 🐛 Bug Fix

- [AC-4365](https://salessolutions.atlassian.net/browse/AC-4365): Upgrade (group 5) eslint [#7979](https://github.com/jaroop/agentcloud/pull/7979) ([@umeshaq](https://github.com/umeshaq) [@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.84.9 (Wed Dec 11 2024)

#### 🐛 Bug Fix

- [AC-4363](https://salessolutions.atlassian.net/browse/AC-4363): Upgrade (group 2) Yup, @types/yup [#7977](https://github.com/jaroop/agentcloud/pull/7977) ([@somnathcts](https://github.com/somnathcts))
- [AC-4390](https://salessolutions.atlassian.net/browse/AC-4390): Upgrade (group 1) React [#7971](https://github.com/jaroop/agentcloud/pull/7971) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@somnathcts](https://github.com/somnathcts)

---

# v5.84.8 (Tue Dec 10 2024)

#### 🐛 Bug Fix

- [AC-4371](https://salessolutions.atlassian.net/browse/AC-4371): Upgrade  stylelint to latest version [#7976](https://github.com/jaroop/agentcloud/pull/7976) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.84.7 (Mon Dec 09 2024)

#### 🐛 Bug Fix

- [AC-4471](https://salessolutions.atlassian.net/browse/AC-4471): 2024 Pen Test Findings - High: SQL Injection in rapportDataSearch GraphQL query [#7973](https://github.com/jaroop/agentcloud/pull/7973) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.84.6 (Mon Dec 09 2024)

#### 🐛 Bug Fix

- [AC-4404](https://salessolutions.atlassian.net/browse/AC-4404): Add Ability to Create API Users [#7972](https://github.com/jaroop/agentcloud/pull/7972) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.84.5 (Fri Dec 06 2024)

#### 🐛 Bug Fix

- Pipeline - Upgrade jaroop-core to 6.0.4 due to https://github.com/jar… [#7970](https://github.com/jaroop/agentcloud/pull/7970) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.84.4 (Thu Dec 05 2024)

#### 🐛 Bug Fix

- [AC-4391](https://salessolutions.atlassian.net/browse/AC-4391): Upgrade TypeScript libraries [#7969](https://github.com/jaroop/agentcloud/pull/7969) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.84.3 (Thu Dec 05 2024)

#### 🐛 Bug Fix

- Pipeline - Add S3 resolver plugin and configuration [#7937](https://github.com/jaroop/agentcloud/pull/7937) ([@nnovaeshc](https://github.com/nnovaeshc))
- [TOPS-1529](https://salessolutions.atlassian.net/browse/TOPS-1529): Increase memory for sonar scanner [#7968](https://github.com/jaroop/agentcloud/pull/7968) ([@nnovaeshc](https://github.com/nnovaeshc))
- [TOPS-1529](https://salessolutions.atlassian.net/browse/TOPS-1529): SonarCloud pipeline integration [#7965](https://github.com/jaroop/agentcloud/pull/7965) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.84.2 (Wed Dec 04 2024)

#### 🐛 Bug Fix

- [AC-4368](https://salessolutions.atlassian.net/browse/AC-4368): Upgrade (group 8) nouislider [#7964](https://github.com/jaroop/agentcloud/pull/7964) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.84.1 (Wed Dec 04 2024)

#### 🐛 Bug Fix

- [AC-4366](https://salessolutions.atlassian.net/browse/AC-4366): Upgrade PostCSS Preset Env to 10.1.1 [#7963](https://github.com/jaroop/agentcloud/pull/7963) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.84.0 (Tue Dec 03 2024)

#### 🚀 Enhancement

- [AC-4428](https://salessolutions.atlassian.net/browse/AC-4428): Create Editable View of Riders [#7961](https://github.com/jaroop/agentcloud/pull/7961) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.83.8 (Wed Nov 27 2024)

#### 🐛 Bug Fix

- [AC-4168](https://salessolutions.atlassian.net/browse/AC-4168): Updated exception message for Lumico Handoff request [#7958](https://github.com/jaroop/agentcloud/pull/7958) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.83.7 (Tue Nov 26 2024)

#### 🐛 Bug Fix

- [AC-4379](https://salessolutions.atlassian.net/browse/AC-4379): Fixed Payment Type Dropdown values [#7957](https://github.com/jaroop/agentcloud/pull/7957) ([@umeshaq](https://github.com/umeshaq))
- [AC-4413](https://salessolutions.atlassian.net/browse/AC-4413): Fix : For NewBenefits Product, "Go to App" button redirects to http instead of https, causing 404 [#7956](https://github.com/jaroop/agentcloud/pull/7956) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.83.6 (Mon Nov 25 2024)

#### 🐛 Bug Fix

- [AC-4381](https://salessolutions.atlassian.net/browse/AC-4381): Applied Details Data Validation [#7955](https://github.com/jaroop/agentcloud/pull/7955) ([@umeshaq](https://github.com/umeshaq) [@somnathcts](https://github.com/somnathcts))

#### Authors: 2

- [@somnathcts](https://github.com/somnathcts)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.83.5 (Mon Nov 25 2024)

#### 🐛 Bug Fix

- [AC-4379](https://salessolutions.atlassian.net/browse/AC-4379): Add Dropdown Values to Applied Details Section [#7954](https://github.com/jaroop/agentcloud/pull/7954) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.83.4 (Fri Nov 22 2024)

#### 🐛 Bug Fix

- [AC-4235](https://salessolutions.atlassian.net/browse/AC-4235): Add a tertiary row to the Journey header [#7951](https://github.com/jaroop/agentcloud/pull/7951) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.83.3 (Fri Nov 22 2024)

#### 🐛 Bug Fix

- [AC-4402](https://salessolutions.atlassian.net/browse/AC-4402): Upgrade AgentCloud to Node 22 [#7943](https://github.com/jaroop/agentcloud/pull/7943) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.83.2 (Thu Nov 21 2024)

#### 🐛 Bug Fix

- [AC-3991](https://salessolutions.atlassian.net/browse/AC-3991): Update AgentCloud Redis to setup SSL [#7949](https://github.com/jaroop/agentcloud/pull/7949) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.83.1 (Wed Nov 20 2024)

#### 🐛 Bug Fix

- [AC-4454](https://salessolutions.atlassian.net/browse/AC-4454): Update task detail view [#7948](https://github.com/jaroop/agentcloud/pull/7948) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.83.0 (Mon Nov 18 2024)

#### 🚀 Enhancement

- [AC-4448](https://salessolutions.atlassian.net/browse/AC-4448): Updating configuration to use new email addresses [#7942](https://github.com/jaroop/agentcloud/pull/7942) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.82.10 (Thu Nov 14 2024)

#### 🐛 Bug Fix

- [AC-4377](https://salessolutions.atlassian.net/browse/AC-4377): Stop New Policy Details Tab From Duplicating [#7938](https://github.com/jaroop/agentcloud/pull/7938) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.82.9 (Tue Nov 12 2024)

#### 🐛 Bug Fix

- [AC-4377](https://salessolutions.atlassian.net/browse/AC-4377): Backend Cleanup for Redesigned Policy Details Tab [#7936](https://github.com/jaroop/agentcloud/pull/7936) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.82.8 (Tue Nov 12 2024)

#### 🐛 Bug Fix

- [AC-4020](https://salessolutions.atlassian.net/browse/AC-4020): Update Partner Details CSV [#7934](https://github.com/jaroop/agentcloud/pull/7934) ([@somnathcts](https://github.com/somnathcts))
- [AC-4378](https://salessolutions.atlassian.net/browse/AC-4378): Make Editable (But Not With Dropdowns Options) View of Applie… [#7935](https://github.com/jaroop/agentcloud/pull/7935) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-4418](https://salessolutions.atlassian.net/browse/AC-4418): Performance enhancements for TCPAExpirationTaskRunner [#7933](https://github.com/jaroop/agentcloud/pull/7933) ([@leaveller](https://github.com/leaveller))

#### Authors: 4

- [@leaveller](https://github.com/leaveller)
- [@somnathcts](https://github.com/somnathcts)
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.82.7 (Mon Nov 11 2024)

#### 🐛 Bug Fix

- [AC-4377](https://salessolutions.atlassian.net/browse/AC-4377): Create New Policy Details Tab in React and Delete Policy Details Redesign Tab Schema [#7924](https://github.com/jaroop/agentcloud/pull/7924) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.82.6 (Mon Nov 11 2024)

#### 🐛 Bug Fix

- [AC-4020](https://salessolutions.atlassian.net/browse/AC-4020): Update Partner Details [#7930](https://github.com/jaroop/agentcloud/pull/7930) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-4319](https://salessolutions.atlassian.net/browse/AC-4319): Add button to view and access Campaigns from Partner Management Page [#7932](https://github.com/jaroop/agentcloud/pull/7932) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.82.5 (Thu Nov 07 2024)

#### 🐛 Bug Fix

- [AC-4019](https://salessolutions.atlassian.net/browse/AC-4019): Change logo URL [#7931](https://github.com/jaroop/agentcloud/pull/7931) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.82.4 (Thu Nov 07 2024)

#### 🐛 Bug Fix

- [AC-4353](https://salessolutions.atlassian.net/browse/AC-4353): Update Carrier Name for SBLI [#7927](https://github.com/jaroop/agentcloud/pull/7927) ([@mallikarjun0102](https://github.com/mallikarjun0102))
- [AC-4406](https://salessolutions.atlassian.net/browse/AC-4406): Add missing rate class to MOO/UOO [#7928](https://github.com/jaroop/agentcloud/pull/7928) ([@leaveller](https://github.com/leaveller))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.82.3 (Wed Nov 06 2024)

#### 🐛 Bug Fix

- [AC-4406](https://salessolutions.atlassian.net/browse/AC-4406): Script update [#7926](https://github.com/jaroop/agentcloud/pull/7926) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.82.2 (Wed Nov 06 2024)

#### 🐛 Bug Fix

- [AC-4019](https://salessolutions.atlassian.net/browse/AC-4019): Create SBLI Branding [#7921](https://github.com/jaroop/agentcloud/pull/7921) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.82.1 (Tue Nov 05 2024)

#### 🐛 Bug Fix

- [AC-4406](https://salessolutions.atlassian.net/browse/AC-4406): Add new CSV for importing additional carriers/products [#7920](https://github.com/jaroop/agentcloud/pull/7920) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.82.0 (Mon Nov 04 2024)

#### 🚀 Enhancement

- [AC-4168](https://salessolutions.atlassian.net/browse/AC-4168): Remove Lumico getQuote and handOff logic [#7894](https://github.com/jaroop/agentcloud/pull/7894) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.81.10 (Fri Nov 01 2024)

#### 🐛 Bug Fix

- [AC-4259](https://salessolutions.atlassian.net/browse/AC-4259): Fix modal titles to match mockup [#7916](https://github.com/jaroop/agentcloud/pull/7916) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.81.9 (Fri Nov 01 2024)

#### 🐛 Bug Fix

- [AC-4259](https://salessolutions.atlassian.net/browse/AC-4259): Fix prefill issues with modals [#7915](https://github.com/jaroop/agentcloud/pull/7915) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.81.8 (Fri Nov 01 2024)

#### 🐛 Bug Fix

- [AC-4216](https://salessolutions.atlassian.net/browse/AC-4216): Fix cache issues found on QA2 [#7914](https://github.com/jaroop/agentcloud/pull/7914) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.81.7 (Thu Oct 31 2024)

#### 🐛 Bug Fix

- [AC-4259](https://salessolutions.atlassian.net/browse/AC-4259): Fix the closed status modal pop [#7912](https://github.com/jaroop/agentcloud/pull/7912) ([@COtlowski](https://github.com/COtlowski) [@nnovaeshc](https://github.com/nnovaeshc))
- [AC-4259](https://salessolutions.atlassian.net/browse/AC-4259): Adjust node memory/cpu settings to match the circleci runner [#7913](https://github.com/jaroop/agentcloud/pull/7913) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.81.6 (Thu Oct 31 2024)

#### 🐛 Bug Fix

- [AC-4216](https://salessolutions.atlassian.net/browse/AC-4216): Remove existing implementation and client code in AgentCloudCore and replace with Lettuce [#7911](https://github.com/jaroop/agentcloud/pull/7911) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.81.5 (Wed Oct 30 2024)

#### 🐛 Bug Fix

- [TOPS-1347](https://salessolutions.atlassian.net/browse/TOPS-1347): Change nginx image to be based on alpine [#7910](https://github.com/jaroop/agentcloud/pull/7910) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.81.4 (Wed Oct 30 2024)

#### 🐛 Bug Fix

- [AC-4334](https://salessolutions.atlassian.net/browse/AC-4334): Agent Cloud Status Enhancement - CRS/NEW BUSINESS Roles *Only* [#7907](https://github.com/jaroop/agentcloud/pull/7907) ([@somnathcts](https://github.com/somnathcts))
- [AC-4395,AC-4207](https://salessolutions.atlassian.net/browse/AC-4395/AC-4207): Further updates to AC-4207 script [#7909](https://github.com/jaroop/agentcloud/pull/7909) ([@leaveller](https://github.com/leaveller))
- [AC-4340](https://salessolutions.atlassian.net/browse/AC-4340): Updated partner API script to upsert instead of insert [#7906](https://github.com/jaroop/agentcloud/pull/7906) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 3

- [@leaveller](https://github.com/leaveller)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@somnathcts](https://github.com/somnathcts)

---

# v5.81.3 (Mon Oct 28 2024)

#### 🐛 Bug Fix

- [AC-4260](https://salessolutions.atlassian.net/browse/AC-4260): Require Policy Number When Moving to In Force or Verified In Force statuses [#7899](https://github.com/jaroop/agentcloud/pull/7899) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.81.2 (Sat Oct 26 2024)

#### 🐛 Bug Fix

- [AC-3683](https://salessolutions.atlassian.net/browse/AC-3683): Upgrade Journey Service to PostGres 16.x [#7898](https://github.com/jaroop/agentcloud/pull/7898) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.81.1 (Fri Oct 25 2024)

#### 🐛 Bug Fix

- [AC-4259](https://salessolutions.atlassian.net/browse/AC-4259): Require Final Disposition Date When Opportunity is Cancelled or Terminated [#7905](https://github.com/jaroop/agentcloud/pull/7905) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.81.0 (Fri Oct 25 2024)

#### 🚀 Enhancement

- [AC-4263](https://salessolutions.atlassian.net/browse/AC-4263): Add 2FA Lockout changes [#7900](https://github.com/jaroop/agentcloud/pull/7900) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.80.10 (Tue Oct 22 2024)

#### 🐛 Bug Fix

- [AC-4255](https://salessolutions.atlassian.net/browse/AC-4255): Fix Breaking Evolution for AgentCloud Core PostGres 16.1 Upgrade [#7884](https://github.com/jaroop/agentcloud/pull/7884) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.80.9 (Fri Oct 18 2024)

#### 🐛 Bug Fix

- [AC-1645](https://salessolutions.atlassian.net/browse/AC-1645): Filter By Carrier filter fixes on Quotes Page and Tab for FE journey [#7892](https://github.com/jaroop/agentcloud/pull/7892) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.80.8 (Fri Oct 18 2024)

#### 🐛 Bug Fix

- [AC-4145](https://salessolutions.atlassian.net/browse/AC-4145): Fix Flaky Test for the SalesReviewListenerSpec [#7895](https://github.com/jaroop/agentcloud/pull/7895) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.80.7 (Thu Oct 17 2024)

#### 🐛 Bug Fix

- [AC-4339](https://salessolutions.atlassian.net/browse/AC-4339): Enhancements to AAA script [#7891](https://github.com/jaroop/agentcloud/pull/7891) ([@leaveller](https://github.com/leaveller))
- [AC-4133](https://salessolutions.atlassian.net/browse/AC-4133): Script to add/update values to eap.psg_config_partners_carriers [#7890](https://github.com/jaroop/agentcloud/pull/7890) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@leaveller](https://github.com/leaveller)

---

# v5.80.6 (Tue Oct 15 2024)

#### 🐛 Bug Fix

- [AC-4244](https://salessolutions.atlassian.net/browse/AC-4244): AAA Offboarding: Write a generic script to close the cases [#7883](https://github.com/jaroop/agentcloud/pull/7883) ([@umeshaq](https://github.com/umeshaq))
- [AC-4218](https://salessolutions.atlassian.net/browse/AC-4218): Create LifeQuotes and SBLI BRU Dialer Lists [#7881](https://github.com/jaroop/agentcloud/pull/7881) ([@swoosh1337](https://github.com/swoosh1337))
- [TOPS-1141](https://salessolutions.atlassian.net/browse/TOPS-1141): Fix lambda deployments via SAM [#7886](https://github.com/jaroop/agentcloud/pull/7886) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.80.5 (Fri Oct 11 2024)

#### 🐛 Bug Fix

- 5.79 Turn on allowConversionGoToSummary feature flag [#7879](https://github.com/jaroop/agentcloud/pull/7879) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.80.4 (Thu Oct 10 2024)

#### 🐛 Bug Fix

- [AC-4324](https://salessolutions.atlassian.net/browse/AC-4324): Add UOO/Banner conversion products [#7880](https://github.com/jaroop/agentcloud/pull/7880) ([@COtlowski](https://github.com/COtlowski))
- Pipeline - Change setup_remote_docker version to default [#7871](https://github.com/jaroop/agentcloud/pull/7871) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.80.3 (Thu Oct 10 2024)

#### 🐛 Bug Fix

- [AC-4299](https://salessolutions.atlassian.net/browse/AC-4299): Removing Email entries in the AAA removal script [#7873](https://github.com/jaroop/agentcloud/pull/7873) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-3926](https://salessolutions.atlassian.net/browse/AC-3926): Fix [#7878](https://github.com/jaroop/agentcloud/pull/7878) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 3

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.80.2 (Wed Oct 09 2024)

#### 🐛 Bug Fix

- [AC-4144](https://salessolutions.atlassian.net/browse/AC-4144): Update AgentCloud Quote Page Language [#7864](https://github.com/jaroop/agentcloud/pull/7864) ([@somnathcts](https://github.com/somnathcts))
- [AC-3926](https://salessolutions.atlassian.net/browse/AC-3926): Add LifeQuotes Carrier-specific PSG configuration to UAT2 Environment [#7877](https://github.com/jaroop/agentcloud/pull/7877) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- [@somnathcts](https://github.com/somnathcts)
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.80.1 (Wed Oct 09 2024)

#### 🐛 Bug Fix

- [AC-4309](https://salessolutions.atlassian.net/browse/AC-4309): Output CSV file for contents of deleted rows from automations.email_ledger table [#7874](https://github.com/jaroop/agentcloud/pull/7874) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.80.0 (Tue Oct 08 2024)

#### 🚀 Enhancement

- [AC-4133](https://salessolutions.atlassian.net/browse/AC-4133): Script to add/update values to eap.psg_config_partners_carriers [#7865](https://github.com/jaroop/agentcloud/pull/7865) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### 🐛 Bug Fix

- Pipeline - Update sbt legacy ci image from python 3.9.x python 3.12.x [#7870](https://github.com/jaroop/agentcloud/pull/7870) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.79.11 (Sat Oct 05 2024)

#### 🐛 Bug Fix

- [AC-4207](https://salessolutions.atlassian.net/browse/AC-4207): Remove AAA data [#7869](https://github.com/jaroop/agentcloud/pull/7869) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.79.10 (Sat Oct 05 2024)

#### 🐛 Bug Fix

- [AC-4207](https://salessolutions.atlassian.net/browse/AC-4207): Remove AAA data [#7868](https://github.com/jaroop/agentcloud/pull/7868) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.79.9 (Sat Oct 05 2024)

#### 🐛 Bug Fix

- [AC-4207](https://salessolutions.atlassian.net/browse/AC-4207): Remove AAA data [#7860](https://github.com/jaroop/agentcloud/pull/7860) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.79.8 (Thu Oct 03 2024)

#### 🐛 Bug Fix

- [AC-4215](https://salessolutions.atlassian.net/browse/AC-4215): Fix vulnerabilities found in AWS [#7863](https://github.com/jaroop/agentcloud/pull/7863) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.79.7 (Thu Oct 03 2024)

#### 🐛 Bug Fix

- [AC-4215](https://salessolutions.atlassian.net/browse/AC-4215): Use Custom AgentCloud image for SFTP [#7862](https://github.com/jaroop/agentcloud/pull/7862) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.79.6 (Tue Oct 01 2024)

#### 🐛 Bug Fix

- [AC-4245](https://salessolutions.atlassian.net/browse/AC-4245): Add Conversion Carriers, Products and Rate Classes [#7859](https://github.com/jaroop/agentcloud/pull/7859) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.79.5 (Mon Sep 30 2024)

#### 🐛 Bug Fix

- [AC-4127](https://salessolutions.atlassian.net/browse/AC-4127): updated AgencyAdminAssignment feature flag to true [#7854](https://github.com/jaroop/agentcloud/pull/7854) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.79.4 (Mon Sep 30 2024)

#### 🐛 Bug Fix

- [AC-4215](https://salessolutions.atlassian.net/browse/AC-4215): Fix SFTP Dockerfile [#7858](https://github.com/jaroop/agentcloud/pull/7858) ([@COtlowski](https://github.com/COtlowski))
- [AC-4214](https://salessolutions.atlassian.net/browse/AC-4214): Debugging python 3.12 changes [#7857](https://github.com/jaroop/agentcloud/pull/7857) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-716](https://salessolutions.atlassian.net/browse/AC-716): Update PSG Codes to reflect the changes for PSG signer [#7856](https://github.com/jaroop/agentcloud/pull/7856) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 3

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.79.3 (Mon Sep 30 2024)

#### 🐛 Bug Fix

- [AC-4215](https://salessolutions.atlassian.net/browse/AC-4215): Upgrade SFTP lambda to Python 3.12 [#7855](https://github.com/jaroop/agentcloud/pull/7855) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.79.2 (Fri Sep 27 2024)

#### 🐛 Bug Fix

- [AC-4214](https://salessolutions.atlassian.net/browse/AC-4214): In progress of updating batch data lambda [#7853](https://github.com/jaroop/agentcloud/pull/7853) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.79.1 (Tue Sep 24 2024)

#### 🐛 Bug Fix

- [AC-4210](https://salessolutions.atlassian.net/browse/AC-4210): Provide the Name of the Jornaya Profile [#7846](https://github.com/jaroop/agentcloud/pull/7846) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.79.0 (Mon Sep 23 2024)

#### 🚀 Enhancement

- [AC-4174](https://salessolutions.atlassian.net/browse/AC-4174): ALPHA: Set up table to store etl-data-importer configuration in ETL DB [#7833](https://github.com/jaroop/agentcloud/pull/7833) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.78.11 (Fri Sep 20 2024)

#### 🐛 Bug Fix

- [AC-4157](https://salessolutions.atlassian.net/browse/AC-4157): Correction to PATCH contact API logic [#7850](https://github.com/jaroop/agentcloud/pull/7850) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.78.10 (Fri Sep 20 2024)

#### 🐛 Bug Fix

- [AC-4172](https://salessolutions.atlassian.net/browse/AC-4172): Removed readiness and liveness probes [#7848](https://github.com/jaroop/agentcloud/pull/7848) ([@umeshaq](https://github.com/umeshaq))
- [AC-4127](https://salessolutions.atlassian.net/browse/AC-4127): Test branch for QA to test agencyAdminAssignment flag disable… [#7847](https://github.com/jaroop/agentcloud/pull/7847) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 3

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.78.9 (Thu Sep 19 2024)

#### 🐛 Bug Fix

- [AC-4172](https://salessolutions.atlassian.net/browse/AC-4172): Earth File & Helm Chart corrections [#7845](https://github.com/jaroop/agentcloud/pull/7845) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.78.8 (Thu Sep 19 2024)

#### 🐛 Bug Fix

- [AC-4127](https://salessolutions.atlassian.net/browse/AC-4127): BugFix for validation message [#7844](https://github.com/jaroop/agentcloud/pull/7844) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.78.7 (Thu Sep 19 2024)

#### 🐛 Bug Fix

- [AC-4127](https://salessolutions.atlassian.net/browse/AC-4127): Fix bug with initial carriers for ops [#7843](https://github.com/jaroop/agentcloud/pull/7843) ([@COtlowski](https://github.com/COtlowski))
- [AC-4172](https://salessolutions.atlassian.net/browse/AC-4172): Service account updated & package refactored [#7842](https://github.com/jaroop/agentcloud/pull/7842) ([@gaurav-sharma85](https://github.com/gaurav-sharma85) [@umeshaq](https://github.com/umeshaq))

#### Authors: 3

- [@gaurav-sharma85](https://github.com/gaurav-sharma85)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.78.6 (Thu Sep 19 2024)

#### 🐛 Bug Fix

- [AC-4127](https://salessolutions.atlassian.net/browse/AC-4127): Visual changes for agency specific admin reassignment [#7840](https://github.com/jaroop/agentcloud/pull/7840) (<EMAIL> [@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Sunil Kumar C S (<EMAIL>)

---

# v5.78.5 (Thu Sep 19 2024)

#### 🐛 Bug Fix

- [AC-4172](https://salessolutions.atlassian.net/browse/AC-4172): Create etl-data-importer container [#7841](https://github.com/jaroop/agentcloud/pull/7841) ([@umeshaq](https://github.com/umeshaq))
- [AC-4218](https://salessolutions.atlassian.net/browse/AC-4218): Create LifeQuotes BRU Dialer Lists [#7838](https://github.com/jaroop/agentcloud/pull/7838) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.78.4 (Wed Sep 18 2024)

#### 🐛 Bug Fix

- Revert "[AC-4172](https://salessolutions.atlassian.net/browse/AC-4172): Create etl-data-importer container" [#7839](https://github.com/jaroop/agentcloud/pull/7839) ([@umeshaq](https://github.com/umeshaq))
- [AC-4172](https://salessolutions.atlassian.net/browse/AC-4172): Create etl-data-importer container [#7836](https://github.com/jaroop/agentcloud/pull/7836) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.78.3 (Tue Sep 17 2024)

#### 🐛 Bug Fix

- [AC-4157](https://salessolutions.atlassian.net/browse/AC-4157): Apply Validation to DOB field of Contact record and Opportunity record [#7835](https://github.com/jaroop/agentcloud/pull/7835) ([@debmalya-pal](https://github.com/debmalya-pal))
- [AC-4185](https://salessolutions.atlassian.net/browse/AC-4185): list of all open AAA cases [#7829](https://github.com/jaroop/agentcloud/pull/7829) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.78.2 (Thu Sep 12 2024)

#### 🐛 Bug Fix

- [AC-4208](https://salessolutions.atlassian.net/browse/AC-4208): Script to upsert PSG product codes [#7830](https://github.com/jaroop/agentcloud/pull/7830) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.78.1 (Wed Sep 11 2024)

#### 🐛 Bug Fix

- [AC-4109](https://salessolutions.atlassian.net/browse/AC-4109): Remove Outdated PSG Code [#7825](https://github.com/jaroop/agentcloud/pull/7825) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.78.0 (Mon Sep 09 2024)

#### 🚀 Enhancement

- [AC-4132](https://salessolutions.atlassian.net/browse/AC-4132): Create SBLI Agency and Associate with SBLI partner [#7810](https://github.com/jaroop/agentcloud/pull/7810) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.77.15 (Fri Sep 06 2024)

#### 🐛 Bug Fix

- [AC-4128](https://salessolutions.atlassian.net/browse/AC-4128): BugFix after QA identified the child opportunity doesnt have … [#7824](https://github.com/jaroop/agentcloud/pull/7824) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.77.14 (Thu Sep 05 2024)

#### 🐛 Bug Fix

- [AC-4075](https://salessolutions.atlassian.net/browse/AC-4075): Create SBLI Conversion and New Business Products [#7822](https://github.com/jaroop/agentcloud/pull/7822) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.77.13 (Wed Sep 04 2024)

#### 🐛 Bug Fix

- [AC-4128](https://salessolutions.atlassian.net/browse/AC-4128): BugFix after QA Test, Ability to assign cases per carriers un… [#7821](https://github.com/jaroop/agentcloud/pull/7821) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.77.12 (Tue Sep 03 2024)

#### 🐛 Bug Fix

- [AC-4107](https://salessolutions.atlassian.net/browse/AC-4107): Default to Filter by Opportunity on the Assigned Task Management page [#7820](https://github.com/jaroop/agentcloud/pull/7820) ([@mallikarjun0102](https://github.com/mallikarjun0102))
- [AC-4128](https://salessolutions.atlassian.net/browse/AC-4128): Ability to assign cases per carriers under a specific agency [#7819](https://github.com/jaroop/agentcloud/pull/7819) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 3

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.77.11 (Tue Sep 03 2024)

#### 🐛 Bug Fix

- [AC-4153](https://salessolutions.atlassian.net/browse/AC-4153): Add Go To Summary button for Conversion [#7818](https://github.com/jaroop/agentcloud/pull/7818) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.77.10 (Tue Sep 03 2024)

#### 🐛 Bug Fix

- [AC-4128](https://salessolutions.atlassian.net/browse/AC-4128): Ability to assign cases per carriers under a specific agency … [#7817](https://github.com/jaroop/agentcloud/pull/7817) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.77.9 (Mon Sep 02 2024)

#### 🐛 Bug Fix

- [AC-4096](https://salessolutions.atlassian.net/browse/AC-4096): Fix Protective Carrier Feed [#7814](https://github.com/jaroop/agentcloud/pull/7814) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.77.8 (Mon Sep 02 2024)

#### 🐛 Bug Fix

- [AC-3598](https://salessolutions.atlassian.net/browse/AC-3598): Remove App In Progress Trigger When Past Reached Quoted [#7813](https://github.com/jaroop/agentcloud/pull/7813) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))
- [AC-4101](https://salessolutions.atlassian.net/browse/AC-4101): Add conversion policy number and connected phone number [#7816](https://github.com/jaroop/agentcloud/pull/7816) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.77.7 (Fri Aug 30 2024)

#### 🐛 Bug Fix

- [AC-4107](https://salessolutions.atlassian.net/browse/AC-4107): Default to Filter by Opportunity on the Assigned Task Management page [#7815](https://github.com/jaroop/agentcloud/pull/7815) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.77.6 (Fri Aug 30 2024)

#### 🐛 Bug Fix

- [AC-4142](https://salessolutions.atlassian.net/browse/AC-4142): Task creation API call is removed from CreateContactNode [#7812](https://github.com/jaroop/agentcloud/pull/7812) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.77.5 (Wed Aug 28 2024)

#### 🐛 Bug Fix

- [AC-4152](https://salessolutions.atlassian.net/browse/AC-4152): Add product type to PSG product codes [#7811](https://github.com/jaroop/agentcloud/pull/7811) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.77.4 (Wed Aug 28 2024)

#### 🐛 Bug Fix

- [AC-2827](https://salessolutions.atlassian.net/browse/AC-2827): Create the ability to edit Lead campaigns :- Defualt_tcpa_status not required [#7809](https://github.com/jaroop/agentcloud/pull/7809) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 1

- [@somnathcts](https://github.com/somnathcts)

---

# v5.77.3 (Mon Aug 26 2024)

#### 🐛 Bug Fix

- [AC-4158](https://salessolutions.atlassian.net/browse/AC-4158): Remove Extra "Westmont" on LifeQuotes Email Branding [#7805](https://github.com/jaroop/agentcloud/pull/7805) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.77.2 (Mon Aug 26 2024)

#### 🐛 Bug Fix

- [AC-4151](https://salessolutions.atlassian.net/browse/AC-4151): Fix broken tests after adding product type [#7804](https://github.com/jaroop/agentcloud/pull/7804) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.77.1 (Mon Aug 26 2024)

#### 🐛 Bug Fix

- [AC-4151](https://salessolutions.atlassian.net/browse/AC-4151): Add product type id to product codes [#7803](https://github.com/jaroop/agentcloud/pull/7803) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.77.0 (Mon Aug 26 2024)

#### 🚀 Enhancement

- [AC-4121](https://salessolutions.atlassian.net/browse/AC-4121): Pre-appointment States for AIG/Corebridge [#7800](https://github.com/jaroop/agentcloud/pull/7800) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### 🐛 Bug Fix

- [AC-2827](https://salessolutions.atlassian.net/browse/AC-2827): Create the ability to edit Lead campaigns -Bug Fix [#7788](https://github.com/jaroop/agentcloud/pull/7788) ([@somnathcts](https://github.com/somnathcts))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@somnathcts](https://github.com/somnathcts)

---

# v5.76.10 (Thu Aug 22 2024)

#### 🐛 Bug Fix

- [AC-4106](https://salessolutions.atlassian.net/browse/AC-4106): Fixed bug with pagination of new query [#7799](https://github.com/jaroop/agentcloud/pull/7799) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [TOPS-1263](https://salessolutions.atlassian.net/browse/TOPS-1263): Remove files that should not have been pushed to git [#7791](https://github.com/jaroop/agentcloud/pull/7791) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.76.9 (Tue Aug 20 2024)

#### 🐛 Bug Fix

- [AC-4099](https://salessolutions.atlassian.net/browse/AC-4099): Remove the Carrier Logo images on the Quotes page [#7798](https://github.com/jaroop/agentcloud/pull/7798) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.76.8 (Tue Aug 20 2024)

#### 🐛 Bug Fix

- [AC-3830](https://salessolutions.atlassian.net/browse/AC-3830): Fix test snapshots with old profile data [#7796](https://github.com/jaroop/agentcloud/pull/7796) ([@COtlowski](https://github.com/COtlowski))
- [AC-3830](https://salessolutions.atlassian.net/browse/AC-3830): Ability to assign cases per carriers under a specific agency … [#7793](https://github.com/jaroop/agentcloud/pull/7793) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 3

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.76.7 (Tue Aug 20 2024)

#### 🐛 Bug Fix

- [AC-4106](https://salessolutions.atlassian.net/browse/AC-4106): Create a New Opportunity Specific Query on the Task Management page [#7789](https://github.com/jaroop/agentcloud/pull/7789) ([@ishukhatriCTS](https://github.com/ishukhatriCTS) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-4099](https://salessolutions.atlassian.net/browse/AC-4099): Remove the Carrier Logo images on the Quotes page [#7795](https://github.com/jaroop/agentcloud/pull/7795) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 3

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.76.6 (Mon Aug 19 2024)

#### 🐛 Bug Fix

- [AC-3520](https://salessolutions.atlassian.net/browse/AC-3520): Publishers/Queues: Build a Shell for Queues [#7712](https://github.com/jaroop/agentcloud/pull/7712) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.76.5 (Mon Aug 19 2024)

#### 🐛 Bug Fix

- [AC-4149](https://salessolutions.atlassian.net/browse/AC-4149): Remove dynamic NOT IN clauses for performance [#7794](https://github.com/jaroop/agentcloud/pull/7794) ([@leaveller](https://github.com/leaveller))
- Pipeline - Add uat2 to CSP in nginx.conf [#7780](https://github.com/jaroop/agentcloud/pull/7780) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.76.4 (Mon Aug 19 2024)

#### 🐛 Bug Fix

- Pipeline - Replace auto CI image with auto-slim [#7748](https://github.com/jaroop/agentcloud/pull/7748) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.76.3 (Wed Aug 14 2024)

#### 🐛 Bug Fix

- [AC-4130](https://salessolutions.atlassian.net/browse/AC-4130): Make Search Parameter Fields Consistent [#7787](https://github.com/jaroop/agentcloud/pull/7787) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.76.2 (Wed Aug 14 2024)

#### 🐛 Bug Fix

- [AC-4140](https://salessolutions.atlassian.net/browse/AC-4140): Turn on Dual Contracting Feature Flag [#7783](https://github.com/jaroop/agentcloud/pull/7783) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.76.1 (Tue Aug 13 2024)

#### 🐛 Bug Fix

- [AC-4098](https://salessolutions.atlassian.net/browse/AC-4098): Change PSG Product Code Fetch [#7781](https://github.com/jaroop/agentcloud/pull/7781) ([@COtlowski](https://github.com/COtlowski))

#### 📝 Documentation

- [TOPS-1263](https://salessolutions.atlassian.net/browse/TOPS-1263): Database scripts for DR test [#7770](https://github.com/jaroop/agentcloud/pull/7770) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.76.0 (Mon Aug 12 2024)

#### 🚀 Enhancement

- [AC-2827](https://salessolutions.atlassian.net/browse/AC-2827): Edit Lead Campaign implementation [#7753](https://github.com/jaroop/agentcloud/pull/7753) ([@somnathcts](https://github.com/somnathcts))

#### 🐛 Bug Fix

- [AC-4115](https://salessolutions.atlassian.net/browse/AC-4115): Move LifeQuotes contracts to agency [#7778](https://github.com/jaroop/agentcloud/pull/7778) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@somnathcts](https://github.com/somnathcts)

---

# v5.75.16 (Fri Aug 09 2024)

#### 🐛 Bug Fix

- Pipeline - Add python black git hook [#7777](https://github.com/jaroop/agentcloud/pull/7777) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.75.15 (Thu Aug 08 2024)

#### 🐛 Bug Fix

- Pipeline - Enable additional pre-commit hooks [#7757](https://github.com/jaroop/agentcloud/pull/7757) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.75.14 (Wed Aug 07 2024)

#### 🐛 Bug Fix

- [AC-3850](https://salessolutions.atlassian.net/browse/AC-3850): Insert on call handler information as answers to the journey implementation [#7771](https://github.com/jaroop/agentcloud/pull/7771) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.75.13 (Tue Aug 06 2024)

#### 🐛 Bug Fix

- [AC-4049](https://salessolutions.atlassian.net/browse/AC-4049): Remove auth token from logging in cURL commands [#7773](https://github.com/jaroop/agentcloud/pull/7773) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.75.12 (Tue Aug 06 2024)

#### 🐛 Bug Fix

- [AC-4124](https://salessolutions.atlassian.net/browse/AC-4124): Turn on b2b2cScreenpop feature flag: training/prod [#7772](https://github.com/jaroop/agentcloud/pull/7772) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.75.11 (Tue Aug 06 2024)

#### 🐛 Bug Fix

- [AC-4046](https://salessolutions.atlassian.net/browse/AC-4046): Add Contract and Licensing Controls To Go To Summary Button [#7764](https://github.com/jaroop/agentcloud/pull/7764) ([@mallikarjun0102](https://github.com/mallikarjun0102))
- [AC-4113](https://salessolutions.atlassian.net/browse/AC-4113): Update Contract Requirements [#7768](https://github.com/jaroop/agentcloud/pull/7768) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.75.10 (Mon Aug 05 2024)

#### 🐛 Bug Fix

- [TOPS-1263](https://salessolutions.atlassian.net/browse/TOPS-1263): Add missing configs for Journey service helm chart [#7769](https://github.com/jaroop/agentcloud/pull/7769) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.75.9 (Mon Aug 05 2024)

#### 🐛 Bug Fix

- [AC-3833](https://salessolutions.atlassian.net/browse/AC-3833): Find all AAA related leads under Client Originated Policy Review [#7763](https://github.com/jaroop/agentcloud/pull/7763) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.75.8 (Mon Aug 05 2024)

#### 🐛 Bug Fix

- [AC-3102](https://salessolutions.atlassian.net/browse/AC-3102): CreateInstance method added for LeadAgentReassignment, LeaInteraction and LeadUpdate [#7755](https://github.com/jaroop/agentcloud/pull/7755) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.75.7 (Mon Aug 05 2024)

#### 🐛 Bug Fix

- [AC-4049](https://salessolutions.atlassian.net/browse/AC-4049): Remove auth token from logging in cURL commands [#7759](https://github.com/jaroop/agentcloud/pull/7759) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.75.6 (Fri Aug 02 2024)

#### 🐛 Bug Fix

- [TOPS-1263](https://salessolutions.atlassian.net/browse/TOPS-1263): Remove minDomain from helm charts [#7761](https://github.com/jaroop/agentcloud/pull/7761) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.75.5 (Fri Aug 02 2024)

#### 🐛 Bug Fix

- [AC-4097](https://salessolutions.atlassian.net/browse/AC-4097): Create PSG Product Codes Table [#7756](https://github.com/jaroop/agentcloud/pull/7756) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.75.4 (Thu Aug 01 2024)

#### 🐛 Bug Fix

- [TOPS-1263](https://salessolutions.atlassian.net/browse/TOPS-1263): Add AZ and fargate profile controls to helm chart [#7752](https://github.com/jaroop/agentcloud/pull/7752) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.75.3 (Thu Aug 01 2024)

#### 🐛 Bug Fix

- [AC-4049](https://salessolutions.atlassian.net/browse/AC-4049): Remove auth token from logging in cURL commands [#7751](https://github.com/jaroop/agentcloud/pull/7751) ([@debmalya-pal](https://github.com/debmalya-pal))
- Pipeline - Enable additional pre-commit hooks [#7754](https://github.com/jaroop/agentcloud/pull/7754) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.75.2 (Tue Jul 30 2024)

#### 🐛 Bug Fix

- Pipeline - Enable pre-commit hooks and fix existing issues [#7749](https://github.com/jaroop/agentcloud/pull/7749) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.75.1 (Tue Jul 30 2024)

#### 🐛 Bug Fix

- [AC-4088](https://salessolutions.atlassian.net/browse/AC-4088): Rapport data ingestion script changes for dobs and last name columns [#7750](https://github.com/jaroop/agentcloud/pull/7750) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.75.0 (Mon Jul 29 2024)

#### 🚀 Enhancement

- [AC-4082](https://salessolutions.atlassian.net/browse/AC-4082): Add Ability to Generify DOB and Last Name Search Terms - Query [#7746](https://github.com/jaroop/agentcloud/pull/7746) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.74.14 (Fri Jul 26 2024)

#### 🐛 Bug Fix

- [AC-3529,AC-3945](https://salessolutions.atlassian.net/browse/AC-3529/AC-3945): Update Ingestion Script to include new values in AC-3945 [#7745](https://github.com/jaroop/agentcloud/pull/7745) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- Revert "Pipeline - Replace auto CI image with auto-slim" [#7744](https://github.com/jaroop/agentcloud/pull/7744) ([@nnovaeshc](https://github.com/nnovaeshc))
- Pipeline - Replace auto CI image with auto-slim [#7718](https://github.com/jaroop/agentcloud/pull/7718) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@nnovaeshc](https://github.com/nnovaeshc)
- balanacqt (<EMAIL>)

---

# v5.74.13 (Wed Jul 24 2024)

#### 🐛 Bug Fix

- [AC-3968](https://salessolutions.atlassian.net/browse/AC-3968): Add button on opportunity header for Go To Summary. BugFix af… [#7743](https://github.com/jaroop/agentcloud/pull/7743) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.74.12 (Wed Jul 24 2024)

#### 🐛 Bug Fix

- [AC-3998](https://salessolutions.atlassian.net/browse/AC-3998): Query ETL Database Based on 2/3 match - Validation and journey [#7742](https://github.com/jaroop/agentcloud/pull/7742) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.74.11 (Wed Jul 24 2024)

#### 🐛 Bug Fix

- [AC-4067](https://salessolutions.atlassian.net/browse/AC-4067): Add Ability to Generify DOB and Last Name Search Terms - Schema [#7741](https://github.com/jaroop/agentcloud/pull/7741) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.74.10 (Wed Jul 24 2024)

#### 🐛 Bug Fix

- [AC-3947](https://salessolutions.atlassian.net/browse/AC-3947): Create a Script for open Opportunities of Legacy Partners [#7735](https://github.com/jaroop/agentcloud/pull/7735) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))
- Pipeline - Rollback the datadog extension to v60 [#7739](https://github.com/jaroop/agentcloud/pull/7739) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.74.9 (Tue Jul 23 2024)

#### 🐛 Bug Fix

- [AC-3998](https://salessolutions.atlassian.net/browse/AC-3998): Query ETL Database Based on 2/3 match - Validation and journey [#7740](https://github.com/jaroop/agentcloud/pull/7740) ([@umeshaq](https://github.com/umeshaq))
- [AC-4022](https://salessolutions.atlassian.net/browse/AC-4022): Create SBLI-B2B2C Business Unit [#7734](https://github.com/jaroop/agentcloud/pull/7734) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🤖 CI

- Revert "Pipeline - PR to temporarily bypass the broken SFTP lambda test" [#7738](https://github.com/jaroop/agentcloud/pull/7738) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 3

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.74.8 (Tue Jul 23 2024)

#### 🐛 Bug Fix

- [AC-3529,AC-3945](https://salessolutions.atlassian.net/browse/AC-3529/AC-3945): Update Ingestion Script to include new values in AC-3945 [#7732](https://github.com/jaroop/agentcloud/pull/7732) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-3968](https://salessolutions.atlassian.net/browse/AC-3968): Add button on opportunity header for Go To Summary. Fixed sel… [#7733](https://github.com/jaroop/agentcloud/pull/7733) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-3998](https://salessolutions.atlassian.net/browse/AC-3998): Query ETL Database Based on 2/3 match - Validation and journey [#7731](https://github.com/jaroop/agentcloud/pull/7731) ([@umeshaq](https://github.com/umeshaq))

#### 🤖 CI

- Pipeline - PR to temporarily bypass the broken SFTP lambda test [#7737](https://github.com/jaroop/agentcloud/pull/7737) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 6

- [@balanacqt](https://github.com/balanacqt)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- balanacqt (<EMAIL>)
- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.74.7 (Fri Jul 19 2024)

#### 🐛 Bug Fix

- [AC-4000](https://salessolutions.atlassian.net/browse/AC-4000): Decomission Healthcare Batch Data lambdas [#7728](https://github.com/jaroop/agentcloud/pull/7728) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.74.6 (Fri Jul 19 2024)

#### 🐛 Bug Fix

- [AC-3152](https://salessolutions.atlassian.net/browse/AC-3152): Reduce message byte length limit to 64KB [#7729](https://github.com/jaroop/agentcloud/pull/7729) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.74.5 (Thu Jul 18 2024)

#### 🐛 Bug Fix

- [AC-3916](https://salessolutions.atlassian.net/browse/AC-3916): Populate conversion policy number from policy number [#7725](https://github.com/jaroop/agentcloud/pull/7725) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.74.4 (Thu Jul 18 2024)

#### 🐛 Bug Fix

- [AC-3968](https://salessolutions.atlassian.net/browse/AC-3968): Add button on opportunity header for Go To Summary. [#7727](https://github.com/jaroop/agentcloud/pull/7727) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.74.3 (Thu Jul 18 2024)

#### 🐛 Bug Fix

- [AC-3152](https://salessolutions.atlassian.net/browse/AC-3152): Reduce size of max message size to 250KB [#7724](https://github.com/jaroop/agentcloud/pull/7724) ([@COtlowski](https://github.com/COtlowski))
- [AC-4038](https://salessolutions.atlassian.net/browse/AC-4038): Make AAA Partner PR Ineligible [#7726](https://github.com/jaroop/agentcloud/pull/7726) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.74.2 (Wed Jul 17 2024)

#### 🐛 Bug Fix

- [AC-3831](https://salessolutions.atlassian.net/browse/AC-3831): Create a Script for open Leads of Legacy Partners [#7722](https://github.com/jaroop/agentcloud/pull/7722) ([@debmalya-pal](https://github.com/debmalya-pal) [@swoosh1337](https://github.com/swoosh1337))
- [AC-3152,SNAPSHOT-18](https://salessolutions.atlassian.net/browse/AC-3152/SNAPSHOT-18): Update JCore to 6.0.0-SNAPSHOT-18 [#7723](https://github.com/jaroop/agentcloud/pull/7723) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 3

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.74.1 (Tue Jul 16 2024)

#### 🐛 Bug Fix

- Pipeline - Update CI image versions [#7716](https://github.com/jaroop/agentcloud/pull/7716) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.74.0 (Mon Jul 15 2024)

#### 🚀 Enhancement

- [AC-3976,CVE-2020,CVE-2022](https://salessolutions.atlassian.net/browse/AC-3976/CVE-2020/CVE-2022): Upgrade Mocha to remove CVE-2020-36632 & CVE-2022-3517 [#7711](https://github.com/jaroop/agentcloud/pull/7711) ([@ishukhatriCTS](https://github.com/ishukhatriCTS))

#### Authors: 1

- [@ishukhatriCTS](https://github.com/ishukhatriCTS)

---

# v5.73.14 (Fri Jul 12 2024)

#### 🐛 Bug Fix

- [AC-3934](https://salessolutions.atlassian.net/browse/AC-3934): Enforce quote controls using the selected opportunity's agency [#7714](https://github.com/jaroop/agentcloud/pull/7714) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### 🧪 Tests

- Pipeline - NGINX test [#7706](https://github.com/jaroop/agentcloud/pull/7706) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.73.13 (Thu Jul 11 2024)

#### 🐛 Bug Fix

- [AC-3711](https://salessolutions.atlassian.net/browse/AC-3711): Update DB URL in Secrets Manager - Journeys [#7705](https://github.com/jaroop/agentcloud/pull/7705) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.73.12 (Thu Jul 11 2024)

#### 🐛 Bug Fix

- [AC-3785](https://salessolutions.atlassian.net/browse/AC-3785): Replace CDN URL [#7707](https://github.com/jaroop/agentcloud/pull/7707) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- Sunil C S ([@sunilcs1](https://github.com/sunilcs1))
- Sunil Kumar C S (<EMAIL>)

---

# v5.73.11 (Wed Jul 10 2024)

#### 🐛 Bug Fix

- [AC-3785](https://salessolutions.atlassian.net/browse/AC-3785): Remove line break from nginx.conf [#7704](https://github.com/jaroop/agentcloud/pull/7704) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.73.10 (Wed Jul 10 2024)

#### 🐛 Bug Fix

- Revert "[AC-3711](https://salessolutions.atlassian.net/browse/AC-3711): Update DB URL in Secrets Manager - Journeys" [#7703](https://github.com/jaroop/agentcloud/pull/7703) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.73.9 (Wed Jul 10 2024)

#### 🐛 Bug Fix

- [AC-3785](https://salessolutions.atlassian.net/browse/AC-3785): Replace CDN address [#7691](https://github.com/jaroop/agentcloud/pull/7691) ([@Jake10012](https://github.com/Jake10012))
- [AC-3711](https://salessolutions.atlassian.net/browse/AC-3711): Update DB URL in Secrets Manager - Journeys [#7698](https://github.com/jaroop/agentcloud/pull/7698) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@Jake10012](https://github.com/Jake10012)
- balanacqt (<EMAIL>)

---

# v5.73.8 (Wed Jul 10 2024)

#### 🐛 Bug Fix

- [AC-3969](https://salessolutions.atlassian.net/browse/AC-3969): Query ETL Database Based on 2/3 match [#7701](https://github.com/jaroop/agentcloud/pull/7701) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.73.7 (Tue Jul 09 2024)

#### 🐛 Bug Fix

- [AC-3648](https://salessolutions.atlassian.net/browse/AC-3648): Update DB URL in Secrets Manager - AgentCloud Core [#7697](https://github.com/jaroop/agentcloud/pull/7697) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-3934](https://salessolutions.atlassian.net/browse/AC-3934): Enforce quote controls using the selected opportunity's agency [#7694](https://github.com/jaroop/agentcloud/pull/7694) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.73.6 (Tue Jul 09 2024)

#### 🐛 Bug Fix

- [AC-3969](https://salessolutions.atlassian.net/browse/AC-3969): Query ETL Database Based on 2/3 match [#7696](https://github.com/jaroop/agentcloud/pull/7696) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.73.5 (Mon Jul 08 2024)

#### 🐛 Bug Fix

- [AC-3955,samclisourcebucket-1](https://salessolutions.atlassian.net/browse/AC-3955/samclisourcebucket-1): Replace S3 bucket aws-sam-cli-managed-default-samclisourcebucket-1x77wzyby6hl4 [#7695](https://github.com/jaroop/agentcloud/pull/7695) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.73.4 (Mon Jul 08 2024)

#### 🐛 Bug Fix

- [AC-4016](https://salessolutions.atlassian.net/browse/AC-4016): Create Cronjob Endpoint for ETL Bridge Plan [#7693](https://github.com/jaroop/agentcloud/pull/7693) ([@debmalya-pal](https://github.com/debmalya-pal))
- post deploy of 5.71.0 [#7685](https://github.com/jaroop/agentcloud/pull/7685) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.73.3 (Thu Jul 04 2024)

#### 🐛 Bug Fix

- [AC-3933](https://salessolutions.atlassian.net/browse/AC-3933): Dynamically send the agency's contract code in the PSG request [#7688](https://github.com/jaroop/agentcloud/pull/7688) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.73.2 (Wed Jul 03 2024)

#### 🐛 Bug Fix

- [AC-3996,CVE-2023](https://salessolutions.atlassian.net/browse/AC-3996/CVE-2023): CVE-2023-50782 [#7687](https://github.com/jaroop/agentcloud/pull/7687) ([@debmalya-pal](https://github.com/debmalya-pal))
- Commit message linter - Gitlint approach [#7633](https://github.com/jaroop/agentcloud/pull/7633) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 2

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.73.1 (Mon Jul 01 2024)

#### 🐛 Bug Fix

- [AC-3994](https://salessolutions.atlassian.net/browse/AC-3994): Change the Endpoints of all Lumico APIs [#7678](https://github.com/jaroop/agentcloud/pull/7678) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.73.0 (Mon Jul 01 2024)

#### 🚀 Enhancement

- [AC-3804,CVE-2021](https://salessolutions.atlassian.net/browse/AC-3804/CVE-2021): agentcloud-web - nth-check CVE-2021-3803 [#7676](https://github.com/jaroop/agentcloud/pull/7676) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.72.16 (Fri Jun 28 2024)

#### 🐛 Bug Fix

- [AC-3951](https://salessolutions.atlassian.net/browse/AC-3951): B2B2C screenpop bugfix [#7684](https://github.com/jaroop/agentcloud/pull/7684) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.72.15 (Fri Jun 28 2024)

#### 🐛 Bug Fix

- [AC-3932](https://salessolutions.atlassian.net/browse/AC-3932): Add agency drop down to contract tab fix [#7683](https://github.com/jaroop/agentcloud/pull/7683) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.72.14 (Fri Jun 28 2024)

#### 🐛 Bug Fix

- [AC-3965](https://salessolutions.atlassian.net/browse/AC-3965): Fixed bug with bad Campaign Name [#7682](https://github.com/jaroop/agentcloud/pull/7682) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.72.13 (Fri Jun 28 2024)

#### 🐛 Bug Fix

- [AC-3965](https://salessolutions.atlassian.net/browse/AC-3965): Create shell of B2B2C Screenpop page - Page Set-up [#7681](https://github.com/jaroop/agentcloud/pull/7681) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.72.12 (Thu Jun 27 2024)

#### 🐛 Bug Fix

- [AC-3959](https://salessolutions.atlassian.net/browse/AC-3959): Add yarn production flag to old pipeline [#7680](https://github.com/jaroop/agentcloud/pull/7680) ([@COtlowski](https://github.com/COtlowski))
- [AC-3951](https://salessolutions.atlassian.net/browse/AC-3951): 3932 Turing on dualContractsAndAppointments and b2b2cScreenPop flags for idev and qa2 [#7679](https://github.com/jaroop/agentcloud/pull/7679) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.72.11 (Thu Jun 27 2024)

#### 🐛 Bug Fix

- [AC-3959](https://salessolutions.atlassian.net/browse/AC-3959): Remove devdependencies from agentcloud-web builds [#7669](https://github.com/jaroop/agentcloud/pull/7669) ([@umeshaq](https://github.com/umeshaq))
- [AC-3951](https://salessolutions.atlassian.net/browse/AC-3951): Turning off b2b2cScreenPop flags for QA testing [#7677](https://github.com/jaroop/agentcloud/pull/7677) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 3

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.72.10 (Wed Jun 26 2024)

#### 🐛 Bug Fix

- [AC-3831](https://salessolutions.atlassian.net/browse/AC-3831): Create a Script for Open leads of Legacy Partner [#7673](https://github.com/jaroop/agentcloud/pull/7673) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.72.9 (Tue Jun 25 2024)

#### 🐛 Bug Fix

- [AC-3932](https://salessolutions.atlassian.net/browse/AC-3932): Add agency drop down to contract tab [#7674](https://github.com/jaroop/agentcloud/pull/7674) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- Fix pipeline issue by updating the image version [#7675](https://github.com/jaroop/agentcloud/pull/7675) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@nnovaeshc](https://github.com/nnovaeshc)
- balanacqt (<EMAIL>)

---

# v5.72.8 (Mon Jun 24 2024)

#### 🐛 Bug Fix

- [AC-3945](https://salessolutions.atlassian.net/browse/AC-3945): Update B2B2C rapport table schema [#7672](https://github.com/jaroop/agentcloud/pull/7672) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.72.7 (Mon Jun 24 2024)

#### 🐛 Bug Fix

- [AC-4372](https://salessolutions.atlassian.net/browse/AC-4372): Update version of main [#7670](https://github.com/jaroop/agentcloud/pull/7670) ([@COtlowski](https://github.com/COtlowski))
- [AC-3472](https://salessolutions.atlassian.net/browse/AC-3472): Fix image S3 URLs that are unsigned [#7663](https://github.com/jaroop/agentcloud/pull/7663) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.72.6 (Mon Jun 24 2024)

#### 🐛 Bug Fix

- [AC-3831](https://salessolutions.atlassian.net/browse/AC-3831): Create a Script for Open Leads of Legacy Partner [#7660](https://github.com/jaroop/agentcloud/pull/7660) ([@debmalya-pal](https://github.com/debmalya-pal))
- [AC-3981](https://salessolutions.atlassian.net/browse/AC-3981): Data Setup for Agent Dual-contracting [#7661](https://github.com/jaroop/agentcloud/pull/7661) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.72.5 (Mon Jun 24 2024)

#### 🐛 Bug Fix

- [AC-3951](https://salessolutions.atlassian.net/browse/AC-3951): Create shell of B2B2C Screenpop page - Redirect Logic [#7662](https://github.com/jaroop/agentcloud/pull/7662) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.72.4 (Fri Jun 21 2024)

#### 🐛 Bug Fix

- 5.71 SAM config changesn for UAT [#7655](https://github.com/jaroop/agentcloud/pull/7655) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.72.3 (Fri Jun 21 2024)

#### 🐛 Bug Fix

- [AC-3667](https://salessolutions.atlassian.net/browse/AC-3667): agentcloud-web: remove react-scripts [#7659](https://github.com/jaroop/agentcloud/pull/7659) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.72.2 (Wed Jun 19 2024)

#### 🐛 Bug Fix

- [AC-3947](https://salessolutions.atlassian.net/browse/AC-3947): Create a Script for open Opportunities of Legacy Partners [#7654](https://github.com/jaroop/agentcloud/pull/7654) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.72.1 (Tue Jun 18 2024)

#### 🐛 Bug Fix

- [AC-3466](https://salessolutions.atlassian.net/browse/AC-3466): Upgrade Dependencies [#7658](https://github.com/jaroop/agentcloud/pull/7658) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 1

- [@Jake10012](https://github.com/Jake10012)

---

# v5.72.0 (Mon Jun 17 2024)

#### 🚀 Enhancement

- [AC-3946](https://salessolutions.atlassian.net/browse/AC-3946): Create a Script for Open Tasks of Legacy Partners [#7656](https://github.com/jaroop/agentcloud/pull/7656) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.71.14 (Mon Jun 17 2024)

#### 🐛 Bug Fix

- [AC-3137](https://salessolutions.atlassian.net/browse/AC-3137): Flaky test fix DialerRuleListSpec.js [#7652](https://github.com/jaroop/agentcloud/pull/7652) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.71.13 (Thu Jun 13 2024)

#### 🐛 Bug Fix

- Post 5.70.0 deployment samconfig and template changes [#7647](https://github.com/jaroop/agentcloud/pull/7647) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.71.12 (Thu Jun 13 2024)

#### 🐛 Bug Fix

- [AC-3526](https://salessolutions.atlassian.net/browse/AC-3526): Fix flaky test issues with Journey Header [#7649](https://github.com/jaroop/agentcloud/pull/7649) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.71.11 (Thu Jun 13 2024)

#### 🐛 Bug Fix

- [AC-3134](https://salessolutions.atlassian.net/browse/AC-3134): Fix the RateClassAliasSpec.scala test [#7651](https://github.com/jaroop/agentcloud/pull/7651) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.71.10 (Tue Jun 11 2024)

#### 🐛 Bug Fix

- [AC-3647](https://salessolutions.atlassian.net/browse/AC-3647): Enforce SSL [#7650](https://github.com/jaroop/agentcloud/pull/7650) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 1

- [@Jake10012](https://github.com/Jake10012)

---

# v5.71.9 (Tue Jun 11 2024)

#### 🐛 Bug Fix

- [AC-3942](https://salessolutions.atlassian.net/browse/AC-3942): Replace quote service in PSG request with policy details service [#7643](https://github.com/jaroop/agentcloud/pull/7643) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.71.8 (Mon Jun 10 2024)

#### 🐛 Bug Fix

- [AC-3919,CVE-2022](https://salessolutions.atlassian.net/browse/AC-3919/CVE-2022): High - agentcloud - CVE-2022-21653 [#7639](https://github.com/jaroop/agentcloud/pull/7639) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.71.7 (Mon Jun 10 2024)

#### 🐛 Bug Fix

- [AC-3806](https://salessolutions.atlassian.net/browse/AC-3806): agentcloud-web - moment - journey-engine [#7648](https://github.com/jaroop/agentcloud/pull/7648) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.71.6 (Wed Jun 05 2024)

#### 🐛 Bug Fix

- [AC-3946](https://salessolutions.atlassian.net/browse/AC-3946): Create a Script for Open Tasks of Legacy Partners [#7635](https://github.com/jaroop/agentcloud/pull/7635) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.71.5 (Wed Jun 05 2024)

#### 🐛 Bug Fix

- [AC-3949](https://salessolutions.atlassian.net/browse/AC-3949): Add Schema to Associate HTML script with call campaign [#7642](https://github.com/jaroop/agentcloud/pull/7642) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.71.4 (Wed Jun 05 2024)

#### 🐛 Bug Fix

- [AC-3940](https://salessolutions.atlassian.net/browse/AC-3940): Split devDependencies [#7638](https://github.com/jaroop/agentcloud/pull/7638) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 1

- [@Jake10012](https://github.com/Jake10012)

---

# v5.71.3 (Wed Jun 05 2024)

#### 🐛 Bug Fix

- [AC-3944](https://salessolutions.atlassian.net/browse/AC-3944): Join table to be used at run time to manage contract requirem… [#7636](https://github.com/jaroop/agentcloud/pull/7636) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.71.2 (Tue Jun 04 2024)

#### 🐛 Bug Fix

- [AC-3462](https://salessolutions.atlassian.net/browse/AC-3462): Update cryptography to 42.0.7 [#7637](https://github.com/jaroop/agentcloud/pull/7637) ([@COtlowski](https://github.com/COtlowski))
- [AC-3819](https://salessolutions.atlassian.net/browse/AC-3819): Create a script to automate lead api users creation [#7629](https://github.com/jaroop/agentcloud/pull/7629) ([@AndreiMazu](https://github.com/AndreiMazu))
- Update lambdas on deploy-to-idev [#7612](https://github.com/jaroop/agentcloud/pull/7612) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.71.1 (Tue Jun 04 2024)

#### 🐛 Bug Fix

- [AC-3730](https://salessolutions.atlassian.net/browse/AC-3730): HIGH - delete old dev and old qa lambdas [#7632](https://github.com/jaroop/agentcloud/pull/7632) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.71.0 (Tue Jun 04 2024)

#### 🚀 Enhancement

- [AC-3462](https://salessolutions.atlassian.net/browse/AC-3462): Update SFTP cryptography package [#7634](https://github.com/jaroop/agentcloud/pull/7634) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.70.12 (Mon Jun 03 2024)

#### 🐛 Bug Fix

- [AC-3931](https://salessolutions.atlassian.net/browse/AC-3931): Schema work to add agency-level configuration [#7631](https://github.com/jaroop/agentcloud/pull/7631) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.70.11 (Mon Jun 03 2024)

#### 🐛 Bug Fix

- [AC-3138](https://salessolutions.atlassian.net/browse/AC-3138): Flaky Test: Fix LeadListDataSpec.scala [#7625](https://github.com/jaroop/agentcloud/pull/7625) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.70.10 (Fri May 31 2024)

#### 🐛 Bug Fix

- [AC-3377](https://salessolutions.atlassian.net/browse/AC-3377): Fix ExamOrderListenerSpec test flakiness [#7588](https://github.com/jaroop/agentcloud/pull/7588) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-3379](https://salessolutions.atlassian.net/browse/AC-3379): Flaky Test: policy-details-tab/index.test.tsx [#7626](https://github.com/jaroop/agentcloud/pull/7626) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 4

- [@sunilcs1](https://github.com/sunilcs1)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Sunil Kumar C S (<EMAIL>)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.70.9 (Wed May 29 2024)

#### 🐛 Bug Fix

- [AC-3862](https://salessolutions.atlassian.net/browse/AC-3862): Publish OpportunityCreation event from journey Create Opportunity node [#7618](https://github.com/jaroop/agentcloud/pull/7618) ([@AndreiMazu](https://github.com/AndreiMazu))
- [TOPS-1120](https://salessolutions.atlassian.net/browse/TOPS-1120): Deploy CircleCI IP ranges feature [#7617](https://github.com/jaroop/agentcloud/pull/7617) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3460](https://salessolutions.atlassian.net/browse/AC-3460): High - lambdas - cryptography - Lambda: batch-data [#7615](https://github.com/jaroop/agentcloud/pull/7615) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### 🏠 Internal

- [AC-3893](https://salessolutions.atlassian.net/browse/AC-3893): Flaky Test: JourneyPrefillMappingModal has "show" class inconsistency for snapshot [#7601](https://github.com/jaroop/agentcloud/pull/7601) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 3

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.70.8 (Fri May 24 2024)

#### 🐛 Bug Fix

- [AC-3897](https://salessolutions.atlassian.net/browse/AC-3897): Apply Partner iGo Link Upon Opportunity Resolution [#7607](https://github.com/jaroop/agentcloud/pull/7607) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.70.7 (Fri May 24 2024)

#### 🐛 Bug Fix

- [AC-3894](https://salessolutions.atlassian.net/browse/AC-3894): Remove enumeratum-play [#7592](https://github.com/jaroop/agentcloud/pull/7592) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.70.6 (Thu May 23 2024)

#### 🐛 Bug Fix

- [AC-3829](https://salessolutions.atlassian.net/browse/AC-3829): Decommission Transfer Auth Lambda [#7602](https://github.com/jaroop/agentcloud/pull/7602) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.70.5 (Thu May 23 2024)

#### 🐛 Bug Fix

- [AC-3925](https://salessolutions.atlassian.net/browse/AC-3925): Add carrier appointment requirements [#7606](https://github.com/jaroop/agentcloud/pull/7606) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-3805](https://salessolutions.atlassian.net/browse/AC-3805): Upgrade JCore-UI to 3.1.0 [#7605](https://github.com/jaroop/agentcloud/pull/7605) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.70.4 (Wed May 22 2024)

#### 🐛 Bug Fix

- [AC-3912](https://salessolutions.atlassian.net/browse/AC-3912): Update AccuQuote address in AgentCloud [#7604](https://github.com/jaroop/agentcloud/pull/7604) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.70.3 (Wed May 22 2024)

#### 🐛 Bug Fix

- Post 5.67.0 Training samconfig.toml update [#7547](https://github.com/jaroop/agentcloud/pull/7547) ([@AndreiMazu](https://github.com/AndreiMazu))
- Revert "[TOPS-1119](https://salessolutions.atlassian.net/browse/TOPS-1119): Move earthly job deploy-to-idev to circleci config" [#7603](https://github.com/jaroop/agentcloud/pull/7603) ([@nnovaeshc](https://github.com/nnovaeshc))
- Revert "[TOPS-1119](https://salessolutions.atlassian.net/browse/TOPS-1119): Update old pipeline in main to use circleci IP ranges feature" [#7600](https://github.com/jaroop/agentcloud/pull/7600) ([@nnovaeshc](https://github.com/nnovaeshc))
- [TOPS-1119](https://salessolutions.atlassian.net/browse/TOPS-1119): Move earthly job deploy-to-idev to circleci config [#7595](https://github.com/jaroop/agentcloud/pull/7595) ([@nnovaeshc](https://github.com/nnovaeshc))
- [TOPS-1119](https://salessolutions.atlassian.net/browse/TOPS-1119): Update old pipeline in main to use circleci IP ranges feature [#7596](https://github.com/jaroop/agentcloud/pull/7596) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.70.2 (Tue May 21 2024)

#### 🐛 Bug Fix

- [AC-3604](https://salessolutions.atlassian.net/browse/AC-3604): Upgrade Jaroop Core to snapshot 16 [#7598](https://github.com/jaroop/agentcloud/pull/7598) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-3772,CVE-2024](https://salessolutions.atlassian.net/browse/AC-3772/CVE-2024): (P2) CRITICAL - CVE-2024-1597 - org.postgresql:postgresql [#7599](https://github.com/jaroop/agentcloud/pull/7599) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- balanacqt (<EMAIL>)

---

# v5.70.1 (Tue May 21 2024)

#### 🐛 Bug Fix

- [AC-3366](https://salessolutions.atlassian.net/browse/AC-3366): Remove the htmlunit dependency [#7593](https://github.com/jaroop/agentcloud/pull/7593) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.70.0 (Mon May 20 2024)

#### 🚀 Enhancement

- [AC-3900](https://salessolutions.atlassian.net/browse/AC-3900): Update ingestion SQL script to resolve JSON type casting issue [#7587](https://github.com/jaroop/agentcloud/pull/7587) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🐛 Bug Fix

- [AC-3760,AC-3745](https://salessolutions.atlassian.net/browse/AC-3760/AC-3745): Unit Testing for AC-3745 [#7572](https://github.com/jaroop/agentcloud/pull/7572) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.69.8 (Wed May 15 2024)

#### 🐛 Bug Fix

- [AC-3846](https://salessolutions.atlassian.net/browse/AC-3846): Fix [#7586](https://github.com/jaroop/agentcloud/pull/7586) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.69.7 (Tue May 14 2024)

#### 🐛 Bug Fix

- AC- 3846 Apply Carrier and Partner Specific Inputs to PSG Request [#7577](https://github.com/jaroop/agentcloud/pull/7577) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-3709](https://salessolutions.atlassian.net/browse/AC-3709): enforce SSL [#7566](https://github.com/jaroop/agentcloud/pull/7566) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@Jake10012](https://github.com/Jake10012)
- balanacqt (<EMAIL>)

---

# v5.69.6 (Mon May 13 2024)

#### 🐛 Bug Fix

- [AC-3789](https://salessolutions.atlassian.net/browse/AC-3789): Decommission Lead Details Snap Lambda [#7575](https://github.com/jaroop/agentcloud/pull/7575) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.69.5 (Mon May 13 2024)

#### 🐛 Bug Fix

- [AC-3784](https://salessolutions.atlassian.net/browse/AC-3784): Upgrade agent cloud core, journey service and jaroop core to … [#7574](https://github.com/jaroop/agentcloud/pull/7574) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-3766](https://salessolutions.atlassian.net/browse/AC-3766): Add an option to "View" .PDF files [#7576](https://github.com/jaroop/agentcloud/pull/7576) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 3

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.69.4 (Fri May 10 2024)

#### 🐛 Bug Fix

- [AC-2889](https://salessolutions.atlassian.net/browse/AC-2889): Fix QuoteNode flaky test. [#7552](https://github.com/jaroop/agentcloud/pull/7552) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 2

- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.69.3 (Thu May 09 2024)

#### 🐛 Bug Fix

- [AC-3857](https://salessolutions.atlassian.net/browse/AC-3857): Fix opportunity tabs without the logo [#7573](https://github.com/jaroop/agentcloud/pull/7573) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.69.2 (Wed May 08 2024)

#### 🐛 Bug Fix

- [AC-3868](https://salessolutions.atlassian.net/browse/AC-3868): Add 'Go to App' button for Transamerica [#7571](https://github.com/jaroop/agentcloud/pull/7571) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.69.1 (Tue May 07 2024)

#### 🐛 Bug Fix

- [AC-3867](https://salessolutions.atlassian.net/browse/AC-3867): Bypass Ancillary Upsell page [#7569](https://github.com/jaroop/agentcloud/pull/7569) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.69.0 (Tue May 07 2024)

#### 🚀 Enhancement

- [AC-3528](https://salessolutions.atlassian.net/browse/AC-3528): Retroactively Update eap.dialer_calls with lead_id [#7518](https://github.com/jaroop/agentcloud/pull/7518) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### 🐛 Bug Fix

- [AC-3857](https://salessolutions.atlassian.net/browse/AC-3857): Add opportunity header logo for partners [#7567](https://github.com/jaroop/agentcloud/pull/7567) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.68.8 (Fri May 03 2024)

#### 🐛 Bug Fix

- Turn On B2B2C Flags for Training And Prod [#7556](https://github.com/jaroop/agentcloud/pull/7556) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.68.7 (Fri May 03 2024)

#### 🐛 Bug Fix

- [AC-3849](https://salessolutions.atlassian.net/browse/AC-3849): Add questions to introduction node [#7554](https://github.com/jaroop/agentcloud/pull/7554) ([@umeshaq](https://github.com/umeshaq))
- [AC-3444,hw5m-5](https://salessolutions.atlassian.net/browse/AC-3444/hw5m-5): High - GHSA-c28r-hw5m-5gv3 in Agentcloud - logback-classic [#7555](https://github.com/jaroop/agentcloud/pull/7555) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 2

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.68.6 (Wed May 01 2024)

#### 🐛 Bug Fix

- [AC-3718](https://salessolutions.atlassian.net/browse/AC-3718): Remove the AAA DNC report Cronjob [#7549](https://github.com/jaroop/agentcloud/pull/7549) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.68.5 (Tue Apr 30 2024)

#### 🐛 Bug Fix

- AC 3844 Add Entries to Partner API Users [#7550](https://github.com/jaroop/agentcloud/pull/7550) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.68.4 (Tue Apr 30 2024)

#### 🐛 Bug Fix

- [AC-3828](https://salessolutions.atlassian.net/browse/AC-3828): Fix JourneySelectionPage flaky tests [#7551](https://github.com/jaroop/agentcloud/pull/7551) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.68.3 (Mon Apr 29 2024)

#### 🐛 Bug Fix

- [AC-3825](https://salessolutions.atlassian.net/browse/AC-3825): Create LifeQuotes Branding [#7545](https://github.com/jaroop/agentcloud/pull/7545) ([@swoosh1337](https://github.com/swoosh1337))
- [AC-3822](https://salessolutions.atlassian.net/browse/AC-3822): PSG handoff changes for LifeQuotes [#7548](https://github.com/jaroop/agentcloud/pull/7548) ([@leaveller](https://github.com/leaveller))
- [AC-3444,hw5m-5](https://salessolutions.atlassian.net/browse/AC-3444/hw5m-5): High - GHSA-c28r-hw5m-5gv3 in Agentcloud - logback-classic [#7546](https://github.com/jaroop/agentcloud/pull/7546) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 3

- [@leaveller](https://github.com/leaveller)
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.68.2 (Thu Apr 25 2024)

#### 🐛 Bug Fix

- [AC-3769](https://salessolutions.atlassian.net/browse/AC-3769): Fix the template for SFTP lambdas [#7536](https://github.com/jaroop/agentcloud/pull/7536) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.68.1 (Thu Apr 25 2024)

#### 🐛 Bug Fix

- [AC-3808](https://salessolutions.atlassian.net/browse/AC-3808): Create LifeQuotes Journey [#7541](https://github.com/jaroop/agentcloud/pull/7541) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.68.0 (Wed Apr 24 2024)

#### 🚀 Enhancement

- [AC-3821](https://salessolutions.atlassian.net/browse/AC-3821): New table for PSG partner configuration [#7540](https://github.com/jaroop/agentcloud/pull/7540) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.67.10 (Tue Apr 23 2024)

#### 🐛 Bug Fix

- [AC-3809](https://salessolutions.atlassian.net/browse/AC-3809): Create LifeQuotes-B2B2C Business Unit [#7538](https://github.com/jaroop/agentcloud/pull/7538) ([@swoosh1337](https://github.com/swoosh1337))
- [AC-3707,TOPS-997](https://salessolutions.atlassian.net/browse/AC-3707/TOPS-997): AgentCloud to consume the image made by TOPS-997 - Journeys DUE AUG 2024 [#7539](https://github.com/jaroop/agentcloud/pull/7539) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 2

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.67.9 (Fri Apr 19 2024)

#### 🐛 Bug Fix

- Pipeline: Remove all-contributors plugin from .autorc [#7532](https://github.com/jaroop/agentcloud/pull/7532) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3649](https://salessolutions.atlassian.net/browse/AC-3649): Edit Call Campaign save changes [#7531](https://github.com/jaroop/agentcloud/pull/7531) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@nnovaeshc](https://github.com/nnovaeshc)
- balanacqt (<EMAIL>)

---

# v5.67.8 (Wed Apr 17 2024)

#### 🐛 Bug Fix

- Empty commit to generate v5.67.8 [#7529](https://github.com/jaroop/agentcloud/pull/7529) ([@leaveller](https://github.com/leaveller))
- [AC-3780](https://salessolutions.atlassian.net/browse/AC-3780): Set up Journey and Collation association in database [#7528](https://github.com/jaroop/agentcloud/pull/7528) ([@COtlowski](https://github.com/COtlowski) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-3649](https://salessolutions.atlassian.net/browse/AC-3649): Edit Call Campaign record [#7526](https://github.com/jaroop/agentcloud/pull/7526) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [ac-3779](https://salessolutions.atlassian.net/browse/ac-3779): Addressed edge case with partner names [#7527](https://github.com/jaroop/agentcloud/pull/7527) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 5

- [@balanacqt](https://github.com/balanacqt)
- [@leaveller](https://github.com/leaveller)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.67.7 (Tue Apr 16 2024)

#### 🐛 Bug Fix

- [AC-3765](https://salessolutions.atlassian.net/browse/AC-3765): Creating 'Lincoln' email branding for b2b2c journey. [#7525](https://github.com/jaroop/agentcloud/pull/7525) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-3778](https://salessolutions.atlassian.net/browse/AC-3778): Build a Opportunity Creation Node Type [#7524](https://github.com/jaroop/agentcloud/pull/7524) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 3

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.67.6 (Tue Apr 16 2024)

#### 🐛 Bug Fix

- [AC-3778](https://salessolutions.atlassian.net/browse/AC-3778): Build a Opportunity Creation Node Type [#7521](https://github.com/jaroop/agentcloud/pull/7521) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.67.5 (Tue Apr 16 2024)

#### 🐛 Bug Fix

- [AC-3118](https://salessolutions.atlassian.net/browse/AC-3118): modified comments from plural to singular in events.conf [#7522](https://github.com/jaroop/agentcloud/pull/7522) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-3765](https://salessolutions.atlassian.net/browse/AC-3765): Creating 'Lincoln' email branding for b2b2c journey [#7520](https://github.com/jaroop/agentcloud/pull/7520) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 4

- [@sunilcs1](https://github.com/sunilcs1)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Sunil Kumar C S (<EMAIL>)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.67.4 (Mon Apr 15 2024)

#### 🐛 Bug Fix

- [AC-3765](https://salessolutions.atlassian.net/browse/AC-3765): Creating 'journeyScripts' and 'customerData' fields to store … [#7519](https://github.com/jaroop/agentcloud/pull/7519) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.67.3 (Mon Apr 15 2024)

#### 🐛 Bug Fix

- [AC-3732](https://salessolutions.atlassian.net/browse/AC-3732): D2C API Users [#7506](https://github.com/jaroop/agentcloud/pull/7506) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.67.2 (Mon Apr 15 2024)

#### 🐛 Bug Fix

- [AC-3779](https://salessolutions.atlassian.net/browse/AC-3779): Put together a SQL Script that would ingest input file and pl… [#7516](https://github.com/jaroop/agentcloud/pull/7516) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- dev-Jacob-[AC-3646](https://salessolutions.atlassian.net/browse/AC-3646) [#7508](https://github.com/jaroop/agentcloud/pull/7508) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 2

- [@Jake10012](https://github.com/Jake10012)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.67.1 (Wed Apr 10 2024)

#### 🐛 Bug Fix

- [AC-3618](https://salessolutions.atlassian.net/browse/AC-3618): Stop Opps Moving to RQ when not in RNQ [#7509](https://github.com/jaroop/agentcloud/pull/7509) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.67.0 (Wed Apr 10 2024)

#### 🚀 Enhancement

- [AC-3769](https://salessolutions.atlassian.net/browse/AC-3769): Use image SFTP lambdas [#7507](https://github.com/jaroop/agentcloud/pull/7507) ([@COtlowski](https://github.com/COtlowski))

#### 🐛 Bug Fix

- Reset cache for tests-sbt-pipeline-circleci job [#7511](https://github.com/jaroop/agentcloud/pull/7511) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.66.10 (Wed Apr 10 2024)

#### 🐛 Bug Fix

- Update SBT CI image - New pipeline [#7504](https://github.com/jaroop/agentcloud/pull/7504) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.66.9 (Fri Apr 05 2024)

#### 🐛 Bug Fix

- [AC-3584](https://salessolutions.atlassian.net/browse/AC-3584): Upgrade AgentCloud Core to Play 3.0 [#7486](https://github.com/jaroop/agentcloud/pull/7486) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.66.8 (Thu Apr 04 2024)

#### 🐛 Bug Fix

- [AC-3636](https://salessolutions.atlassian.net/browse/AC-3636): Create, Update, Delete Values to New Prefill Configuration Table [#7501](https://github.com/jaroop/agentcloud/pull/7501) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.66.7 (Wed Apr 03 2024)

#### 🐛 Bug Fix

- [AC-3636](https://salessolutions.atlassian.net/browse/AC-3636): Create, Update, Delete Values to New Prefill Configuration Table [#7500](https://github.com/jaroop/agentcloud/pull/7500) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.66.6 (Tue Apr 02 2024)

#### 🐛 Bug Fix

- [AC-3468](https://salessolutions.atlassian.net/browse/AC-3468): enabled info logging for SoapGateway [#7499](https://github.com/jaroop/agentcloud/pull/7499) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.66.5 (Tue Apr 02 2024)

#### 🐛 Bug Fix

- [AC-3118](https://salessolutions.atlassian.net/browse/AC-3118): Fix Helm for journey service [#7498](https://github.com/jaroop/agentcloud/pull/7498) ([@leaveller](https://github.com/leaveller))
- [AC-3561](https://salessolutions.atlassian.net/browse/AC-3561): Remove AAA Email Address Field from User Profile [#7497](https://github.com/jaroop/agentcloud/pull/7497) ([@debmalya-pal](https://github.com/debmalya-pal))
- Dev cdo ac 3118 [#7482](https://github.com/jaroop/agentcloud/pull/7482) ([@COtlowski](https://github.com/COtlowski))
- [AC-3636](https://salessolutions.atlassian.net/browse/AC-3636): Create, Update, Delete Values to New Prefill Configuration Table [#7496](https://github.com/jaroop/agentcloud/pull/7496) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 4

- [@leaveller](https://github.com/leaveller)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.66.4 (Fri Mar 29 2024)

#### 🐛 Bug Fix

- [AC-3745](https://salessolutions.atlassian.net/browse/AC-3745): Update AgentSync field name [#7492](https://github.com/jaroop/agentcloud/pull/7492) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.66.3 (Thu Mar 28 2024)

#### 🐛 Bug Fix

- [AC-3672](https://salessolutions.atlassian.net/browse/AC-3672): Add prefill blob for B2B2C journey [#7488](https://github.com/jaroop/agentcloud/pull/7488) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.66.2 (Wed Mar 27 2024)

#### 🐛 Bug Fix

- [AC-3525](https://salessolutions.atlassian.net/browse/AC-3525): Create B2B2C schema in ETL database [#7485](https://github.com/jaroop/agentcloud/pull/7485) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.66.1 (Tue Mar 26 2024)

#### 🐛 Bug Fix

- [AC-3615](https://salessolutions.atlassian.net/browse/AC-3615): Upgrade Email Renderer to Use Dynamic User Partner Table - Opportunity Agent Email Address [#7483](https://github.com/jaroop/agentcloud/pull/7483) ([@debmalya-pal](https://github.com/debmalya-pal))

#### Authors: 1

- Debmalya Pal ([@debmalya-pal](https://github.com/debmalya-pal))

---

# v5.66.0 (Mon Mar 25 2024)

#### 🚀 Enhancement

- [AC-3616](https://salessolutions.atlassian.net/browse/AC-3616): Upgrade Email Renderer to use partner Admin email address [#7467](https://github.com/jaroop/agentcloud/pull/7467) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### 🐛 Bug Fix

- Change tag for the redis image [#7484](https://github.com/jaroop/agentcloud/pull/7484) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@mallikarjun0102](https://github.com/mallikarjun0102)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.65.5 (Fri Mar 22 2024)

#### 🐛 Bug Fix

- [AC-3468](https://salessolutions.atlassian.net/browse/AC-3468): Logging changes to Five9 lead removal process [#7480](https://github.com/jaroop/agentcloud/pull/7480) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.65.4 (Wed Mar 20 2024)

#### 🐛 Bug Fix

- [AC-3530](https://salessolutions.atlassian.net/browse/AC-3530): Lookup collation when partner name is provided [#7476](https://github.com/jaroop/agentcloud/pull/7476) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.65.3 (Wed Mar 20 2024)

#### 🐛 Bug Fix

- [AC-3531](https://salessolutions.atlassian.net/browse/AC-3531): Save rapport id to dialer calls tables. [#7472](https://github.com/jaroop/agentcloud/pull/7472) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.65.2 (Wed Mar 20 2024)

#### 🐛 Bug Fix

- [AC-3617](https://salessolutions.atlassian.net/browse/AC-3617): Add Ability to Delete Entries on Partner Email Tab [#7465](https://github.com/jaroop/agentcloud/pull/7465) ([@umeshaq](https://github.com/umeshaq))
- [AC-3530](https://salessolutions.atlassian.net/browse/AC-3530): Lookup collation when partner name is provided [#7459](https://github.com/jaroop/agentcloud/pull/7459) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.65.1 (Wed Mar 20 2024)

#### 🐛 Bug Fix

- [AC-3468](https://salessolutions.atlassian.net/browse/AC-3468): Logging changes to Five9 lead removal process [#7464](https://github.com/jaroop/agentcloud/pull/7464) ([@swoosh1337](https://github.com/swoosh1337))
- [AC-3652](https://salessolutions.atlassian.net/browse/AC-3652): Rename migration file [#7470](https://github.com/jaroop/agentcloud/pull/7470) ([@leaveller](https://github.com/leaveller))
- [AC-3652](https://salessolutions.atlassian.net/browse/AC-3652): Create rapportid and offer code [#7466](https://github.com/jaroop/agentcloud/pull/7466) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 3

- [@Jake10012](https://github.com/Jake10012)
- [@leaveller](https://github.com/leaveller)
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.65.0 (Tue Mar 19 2024)

#### 🚀 Enhancement

- [AC-3539](https://salessolutions.atlassian.net/browse/AC-3539): Use the Pre-Filled Configuration at Run-Time to Populate Answers [#7468](https://github.com/jaroop/agentcloud/pull/7468) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### 🐛 Bug Fix

- [AC-3538](https://salessolutions.atlassian.net/browse/AC-3538): Support Reading Values from Pre-fill Configuration Table [#7458](https://github.com/jaroop/agentcloud/pull/7458) ([@umeshaq](https://github.com/umeshaq))
- [AC-3549](https://salessolutions.atlassian.net/browse/AC-3549): Add a new CreateLeadNode for the journey [#7461](https://github.com/jaroop/agentcloud/pull/7461) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 4

- [@sunilcs1](https://github.com/sunilcs1)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Sunil Kumar C S (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.64.12 (Wed Mar 13 2024)

#### 🐛 Bug Fix

- [AC-3583](https://salessolutions.atlassian.net/browse/AC-3583): Upgrade Agent cloud core Play version to 2.9.0 [#7457](https://github.com/jaroop/agentcloud/pull/7457) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.64.11 (Fri Mar 08 2024)

#### 🐛 Bug Fix

- Upgrade AgentCloud Core to Scala 2.13 [#7455](https://github.com/jaroop/agentcloud/pull/7455) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-3623](https://salessolutions.atlassian.net/browse/AC-3623): Upgrade AgentCloud Core to Scala 2.13 [#7443](https://github.com/jaroop/agentcloud/pull/7443) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.64.10 (Thu Mar 07 2024)

#### 🐛 Bug Fix

- [AC-3559](https://salessolutions.atlassian.net/browse/AC-3559): Add Account partner email [#7453](https://github.com/jaroop/agentcloud/pull/7453) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.64.9 (Thu Mar 07 2024)

#### 🐛 Bug Fix

- [AC-3467](https://salessolutions.atlassian.net/browse/AC-3467): High - lambdas - urllib3 - Lambda: transfer-auth [#7452](https://github.com/jaroop/agentcloud/pull/7452) ([@mallikarjun0102](https://github.com/mallikarjun0102))
- [AC-3467](https://salessolutions.atlassian.net/browse/AC-3467): High - lambdas - urllib3 - Lambda: transfer-auth [#7450](https://github.com/jaroop/agentcloud/pull/7450) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.64.8 (Wed Mar 06 2024)

#### 🐛 Bug Fix

- [AC-3562](https://salessolutions.atlassian.net/browse/AC-3562): Revert changes for old AAA email field [#7451](https://github.com/jaroop/agentcloud/pull/7451) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.64.7 (Wed Mar 06 2024)

#### 🐛 Bug Fix

- [AC-3562](https://salessolutions.atlassian.net/browse/AC-3562): Use user-partner email for email renderer [#7449](https://github.com/jaroop/agentcloud/pull/7449) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.64.6 (Tue Mar 05 2024)

#### 🐛 Bug Fix

- [AC-3559](https://salessolutions.atlassian.net/browse/AC-3559): Create Account partner email [#7446](https://github.com/jaroop/agentcloud/pull/7446) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.64.5 (Mon Mar 04 2024)

#### 🐛 Bug Fix

- [AC-3536](https://salessolutions.atlassian.net/browse/AC-3536): Build Data Schema to Support Configurable Prefilling of Answers [#7444](https://github.com/jaroop/agentcloud/pull/7444) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.64.4 (Mon Mar 04 2024)

#### 🐛 Bug Fix

- [AC-3452](https://salessolutions.atlassian.net/browse/AC-3452): sftp lambdas - Bundle binaries to update python version to 3.8 [#7445](https://github.com/jaroop/agentcloud/pull/7445) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.64.3 (Thu Feb 29 2024)

#### 🐛 Bug Fix

- [AC-231](https://salessolutions.atlassian.net/browse/AC-231): decommissioning-AAA-DNC [#7441](https://github.com/jaroop/agentcloud/pull/7441) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 1

- [@Jake10012](https://github.com/Jake10012)

---

# v5.64.2 (Wed Feb 28 2024)

#### 🐛 Bug Fix

- [AC-3452](https://salessolutions.atlassian.net/browse/AC-3452): sftp lambdas - Updating python version from 3.7 to 3.8 [#7440](https://github.com/jaroop/agentcloud/pull/7440) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.64.1 (Tue Feb 27 2024)

#### 🐛 Bug Fix

- [AC-3577](https://salessolutions.atlassian.net/browse/AC-3577): Decommission RTS Lambda [#7439](https://github.com/jaroop/agentcloud/pull/7439) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.64.0 (Tue Feb 27 2024)

#### 🚀 Enhancement

- [AC-3418](https://salessolutions.atlassian.net/browse/AC-3418): Drop phone triggers that cause deadlocks [#7430](https://github.com/jaroop/agentcloud/pull/7430) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.63.6 (Mon Feb 26 2024)

#### 🐛 Bug Fix

- [AC-3609](https://salessolutions.atlassian.net/browse/AC-3609): Create Read-Only Tab for email/partner/account associations [#7434](https://github.com/jaroop/agentcloud/pull/7434) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.63.5 (Fri Feb 23 2024)

#### 🐛 Bug Fix

- [AC-3609](https://salessolutions.atlassian.net/browse/AC-3609): Create Read-Only Tab for email/partner/account associations [#7432](https://github.com/jaroop/agentcloud/pull/7432) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.63.4 (Thu Feb 22 2024)

#### 🐛 Bug Fix

- [AC-3634](https://salessolutions.atlassian.net/browse/AC-3634): Change Default TCPA Status to Null for Marketing Campaigns [#7429](https://github.com/jaroop/agentcloud/pull/7429) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.63.3 (Wed Feb 21 2024)

#### 🐛 Bug Fix

- [AC-3609](https://salessolutions.atlassian.net/browse/AC-3609): Create Read-Only Tab for email/partner/account associations [#7423](https://github.com/jaroop/agentcloud/pull/7423) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.63.2 (Tue Feb 20 2024)

#### 🐛 Bug Fix

- [AC-3552](https://salessolutions.atlassian.net/browse/AC-3552): Show DOB Confirmation on All Lead and Opportunities [#7415](https://github.com/jaroop/agentcloud/pull/7415) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.63.1 (Tue Feb 20 2024)

#### 🐛 Bug Fix

- [AC-3422](https://salessolutions.atlassian.net/browse/AC-3422): decommissioning-smarts [#7424](https://github.com/jaroop/agentcloud/pull/7424) ([@Jake10012](https://github.com/Jake10012))

#### Authors: 1

- [@Jake10012](https://github.com/Jake10012)

---

# v5.63.0 (Mon Feb 19 2024)

#### 🚀 Enhancement

- [AC-3380](https://salessolutions.atlassian.net/browse/AC-3380): Remove additional references to triggers [#7422](https://github.com/jaroop/agentcloud/pull/7422) ([@leaveller](https://github.com/leaveller))
- [AC-3618](https://salessolutions.atlassian.net/browse/AC-3618): Stop Opps Moving to Reached Quoted when not in Reached Not Qu… [#7412](https://github.com/jaroop/agentcloud/pull/7412) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🐛 Bug Fix

- [AC-3380](https://salessolutions.atlassian.net/browse/AC-3380): Email Entries - Reduce chances of Database deadlock [#7411](https://github.com/jaroop/agentcloud/pull/7411) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.62.13 (Mon Feb 19 2024)

#### 🐛 Bug Fix

- [AC-3532](https://salessolutions.atlassian.net/browse/AC-3532): Update Dialer Calls Table with Rapport ID Column [#7421](https://github.com/jaroop/agentcloud/pull/7421) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.62.12 (Fri Feb 09 2024)

#### 🐛 Bug Fix

- [AC-3558](https://salessolutions.atlassian.net/browse/AC-3558): Create Table with Account ID, Partner ID, Email Address [#7409](https://github.com/jaroop/agentcloud/pull/7409) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.62.11 (Thu Feb 08 2024)

#### 🐛 Bug Fix

- [AC-3310](https://salessolutions.atlassian.net/browse/AC-3310): Add Rate limiting logic when sending to CallMiner [#7408](https://github.com/jaroop/agentcloud/pull/7408) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.62.10 (Thu Feb 08 2024)

#### 🐛 Bug Fix

- [AC-3586](https://salessolutions.atlassian.net/browse/AC-3586): Add columns B2B2C boolean and domain to eap.partners table [#7405](https://github.com/jaroop/agentcloud/pull/7405) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.62.9 (Wed Feb 07 2024)

#### 🐛 Bug Fix

- [AC-3579](https://salessolutions.atlassian.net/browse/AC-3579): Create new view and indexes based on provided query [#7406](https://github.com/jaroop/agentcloud/pull/7406) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.62.8 (Wed Feb 07 2024)

#### 🐛 Bug Fix

- [AC-3310](https://salessolutions.atlassian.net/browse/AC-3310): Add Rate limiting logic when sending to CallMiner [#7407](https://github.com/jaroop/agentcloud/pull/7407) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.62.7 (Mon Feb 05 2024)

#### 🐛 Bug Fix

- [AC-3310](https://salessolutions.atlassian.net/browse/AC-3310): Add Rate limiting logic when sending to CallMiner [#7393](https://github.com/jaroop/agentcloud/pull/7393) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.62.6 (Sun Feb 04 2024)

#### 🐛 Bug Fix

- [AC-3516](https://salessolutions.atlassian.net/browse/AC-3516): Callminer: SQL updates for linking opps/calls [#7387](https://github.com/jaroop/agentcloud/pull/7387) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.62.5 (Fri Feb 02 2024)

#### 🐛 Bug Fix

- [AC-3452](https://salessolutions.atlassian.net/browse/AC-3452): High - lambdas - urllib3 [#7389](https://github.com/jaroop/agentcloud/pull/7389) ([@mallikarjun0102](https://github.com/mallikarjun0102))

#### Authors: 1

- [@mallikarjun0102](https://github.com/mallikarjun0102)

---

# v5.62.4 (Thu Feb 01 2024)

#### 🐛 Bug Fix

- [AC-3544](https://salessolutions.atlassian.net/browse/AC-3544): Fixed bug where wrong ID was used for lead lookup. [#7392](https://github.com/jaroop/agentcloud/pull/7392) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.62.3 (Wed Jan 31 2024)

#### 🐛 Bug Fix

- [AC-3544](https://salessolutions.atlassian.net/browse/AC-3544): Fixed business logic error where wrong policy was referenced [#7391](https://github.com/jaroop/agentcloud/pull/7391) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.62.2 (Wed Jan 31 2024)

#### 🐛 Bug Fix

- [AC-3544](https://salessolutions.atlassian.net/browse/AC-3544): Create replacing policy error on opportunity record [#7384](https://github.com/jaroop/agentcloud/pull/7384) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.62.1 (Wed Jan 31 2024)

#### 🐛 Bug Fix

- [AC-3505](https://salessolutions.atlassian.net/browse/AC-3505): Move <8 year duration term leads to ready to dial [#7388](https://github.com/jaroop/agentcloud/pull/7388) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.62.0 (Mon Jan 29 2024)

#### 🚀 Enhancement

- [AC-3507](https://salessolutions.atlassian.net/browse/AC-3507): Fix prefill for the conversion [#7386](https://github.com/jaroop/agentcloud/pull/7386) ([@COtlowski](https://github.com/COtlowski))

#### 🐛 Bug Fix

- [AC-3507](https://salessolutions.atlassian.net/browse/AC-3507): Prohibit "Replacing w/Same Carrier" in AAA journey [#7377](https://github.com/jaroop/agentcloud/pull/7377) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.61.10 (Thu Jan 25 2024)

#### 🐛 Bug Fix

- [AC-3417](https://salessolutions.atlassian.net/browse/AC-3417): AAA BRU Routing Rules [#7382](https://github.com/jaroop/agentcloud/pull/7382) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.61.9 (Wed Jan 24 2024)

#### 🐛 Bug Fix

- [TOPS-422](https://salessolutions.atlassian.net/browse/TOPS-422): Update CI Images [#7368](https://github.com/jaroop/agentcloud/pull/7368) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.61.8 (Wed Jan 24 2024)

#### 🐛 Bug Fix

- [AC-3450](https://salessolutions.atlassian.net/browse/AC-3450): Critical - lambdas - pyarrow [#7381](https://github.com/jaroop/agentcloud/pull/7381) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.61.7 (Tue Jan 23 2024)

#### 🐛 Bug Fix

- [AC-3425](https://salessolutions.atlassian.net/browse/AC-3425): OBR Decommissioning - Remove API User [#7378](https://github.com/jaroop/agentcloud/pull/7378) ([@JAbelRamosGelover](https://github.com/JAbelRamosGelover))

#### Authors: 1

- [@JAbelRamosGelover](https://github.com/JAbelRamosGelover)

---

# v5.61.6 (Tue Jan 23 2024)

#### 🐛 Bug Fix

- [AC-3401](https://salessolutions.atlassian.net/browse/AC-3401): Change to official non-root nginx image [#7373](https://github.com/jaroop/agentcloud/pull/7373) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🏠 Internal

- Save a copy of the lead-details-snap lambda to the iac-[agentcloud-3](https://salessolutions.atlassian.net/browse/agentcloud-3): rd-party repo [#7374](https://github.com/jaroop/agentcloud/pull/7374) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.61.5 (Tue Jan 23 2024)

#### 🐛 Bug Fix

- [AC-3403](https://salessolutions.atlassian.net/browse/AC-3403): Dry run deletion script - changes [#7364](https://github.com/jaroop/agentcloud/pull/7364) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@AndreiMazu](https://github.com/AndreiMazu))
- Revert "[AC-3401](https://salessolutions.atlassian.net/browse/AC-3401): Add Helm template fixes" [#7371](https://github.com/jaroop/agentcloud/pull/7371) ([@COtlowski](https://github.com/COtlowski))
- [AC-3401](https://salessolutions.atlassian.net/browse/AC-3401): Add Helm template fixes [#7369](https://github.com/jaroop/agentcloud/pull/7369) ([@COtlowski](https://github.com/COtlowski))

#### 🏠 Internal

- Remove caching from terraform on deploy-tf target [#7372](https://github.com/jaroop/agentcloud/pull/7372) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.61.4 (Fri Jan 19 2024)

#### 🐛 Bug Fix

- [AC-3401](https://salessolutions.atlassian.net/browse/AC-3401): Make AgentCloud Web run as non-root user [#7365](https://github.com/jaroop/agentcloud/pull/7365) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.61.3 (Thu Jan 18 2024)

#### 🐛 Bug Fix

- [AC-3450](https://salessolutions.atlassian.net/browse/AC-3450): Critical - lambdas - pyarrow [#7361](https://github.com/jaroop/agentcloud/pull/7361) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.61.2 (Wed Jan 17 2024)

#### 🐛 Bug Fix

- [AC-1695](https://salessolutions.atlassian.net/browse/AC-1695): Populate decision and postponed_to [#7362](https://github.com/jaroop/agentcloud/pull/7362) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.61.1 (Tue Jan 16 2024)

#### 🐛 Bug Fix

- [AC-2843](https://salessolutions.atlassian.net/browse/AC-2843): Make Convenience Logging method available to JCore [#7355](https://github.com/jaroop/agentcloud/pull/7355) ([@JAbelRamosGelover](https://github.com/JAbelRamosGelover))

#### Authors: 1

- [@JAbelRamosGelover](https://github.com/JAbelRamosGelover)

---

# v5.61.0 (Tue Jan 16 2024)

#### 🚀 Enhancement

- [AC-3371](https://salessolutions.atlassian.net/browse/AC-3371): Upgrade AWS SDK to remove vulnerability [#7359](https://github.com/jaroop/agentcloud/pull/7359) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.60.11 (Tue Jan 16 2024)

#### 🐛 Bug Fix

- [AC-3425](https://salessolutions.atlassian.net/browse/AC-3425): OBR Decommissioning - Remove API User [#7360](https://github.com/jaroop/agentcloud/pull/7360) ([@JAbelRamosGelover](https://github.com/JAbelRamosGelover))

#### Authors: 1

- [@JAbelRamosGelover](https://github.com/JAbelRamosGelover)

---

# v5.60.10 (Fri Jan 12 2024)

#### 🐛 Bug Fix

- [AC-3367](https://salessolutions.atlassian.net/browse/AC-3367): Update jaroop-core to fix logging [#7357](https://github.com/jaroop/agentcloud/pull/7357) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.60.9 (Fri Jan 12 2024)

#### 🐛 Bug Fix

- [AC-3276](https://salessolutions.atlassian.net/browse/AC-3276): Fix the endpoint to not wait on results [#7354](https://github.com/jaroop/agentcloud/pull/7354) ([@COtlowski](https://github.com/COtlowski))
- [AC-3400](https://salessolutions.atlassian.net/browse/AC-3400): Dates are getting changed in Old Policy Detail Tab [#7346](https://github.com/jaroop/agentcloud/pull/7346) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 3

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Valentyn Protsenko (<EMAIL>)

---

# v5.60.8 (Thu Jan 11 2024)

#### 🐛 Bug Fix

- [AC-3276](https://salessolutions.atlassian.net/browse/AC-3276): Add missing urls for the Callminer config [#7349](https://github.com/jaroop/agentcloud/pull/7349) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.60.7 (Wed Jan 10 2024)

#### 🐛 Bug Fix

- [AC-3276](https://salessolutions.atlassian.net/browse/AC-3276): Add metadata calls to the CallMiner system [#7347](https://github.com/jaroop/agentcloud/pull/7347) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.60.6 (Tue Jan 09 2024)

#### 🐛 Bug Fix

- [AC-3360](https://salessolutions.atlassian.net/browse/AC-3360): Fix permissions for the Agent Role in the Lead Management [#7335](https://github.com/jaroop/agentcloud/pull/7335) ([@JAbelRamosGelover](https://github.com/JAbelRamosGelover))

#### Authors: 1

- [@JAbelRamosGelover](https://github.com/JAbelRamosGelover)

---

# v5.60.5 (Mon Jan 08 2024)

#### 🐛 Bug Fix

- [AC-3370](https://salessolutions.atlassian.net/browse/AC-3370): Fix jackson vulnerabilities [#7339](https://github.com/jaroop/agentcloud/pull/7339) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.60.4 (Mon Jan 08 2024)

#### 🐛 Bug Fix

- [TOPS-710](https://salessolutions.atlassian.net/browse/TOPS-710): Decommission OBR [#7336](https://github.com/jaroop/agentcloud/pull/7336) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.60.3 (Sat Jan 06 2024)

#### 🐛 Bug Fix

- [AC-3381](https://salessolutions.atlassian.net/browse/AC-3381): Change agentcloud base image (Critical) [#7337](https://github.com/jaroop/agentcloud/pull/7337) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- Update agentcloud-web build job for old pipeline [#7308](https://github.com/jaroop/agentcloud/pull/7308) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3367](https://salessolutions.atlassian.net/browse/AC-3367): vulnerabilities related to jetty-http dependency (High) [#7333](https://github.com/jaroop/agentcloud/pull/7333) ([@swoosh1337](https://github.com/swoosh1337))

#### ⚠️ Pushed to `main`

- Reset cache ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 4

- [@balanacqt](https://github.com/balanacqt)
- [@nnovaeshc](https://github.com/nnovaeshc)
- balanacqt (<EMAIL>)
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.60.2 (Tue Jan 02 2024)

#### 🐛 Bug Fix

- [AC-3364](https://salessolutions.atlassian.net/browse/AC-3364): Fix vulnerabilities related to postgres JDBC driver [#7331](https://github.com/jaroop/agentcloud/pull/7331) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.60.1 (Tue Jan 02 2024)

#### 🐛 Bug Fix

- [AC-3364](https://salessolutions.atlassian.net/browse/AC-3364): Fix vulnerabilities related to postrgres JDBC driver [#7329](https://github.com/jaroop/agentcloud/pull/7329) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.60.0 (Tue Jan 02 2024)

#### 🚀 Enhancement

- [AC-3415](https://salessolutions.atlassian.net/browse/AC-3415): Add new DB indexes [#7330](https://github.com/jaroop/agentcloud/pull/7330) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.59.4 (Fri Dec 22 2023)

#### 🐛 Bug Fix

- [AC-3305](https://salessolutions.atlassian.net/browse/AC-3305): Cleanup work to Exclude PR Leads already associated with legacy partners [#7317](https://github.com/jaroop/agentcloud/pull/7317) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.59.3 (Thu Dec 21 2023)

#### 🐛 Bug Fix

- [AC-3365,CVE-2022](https://salessolutions.atlassian.net/browse/AC-3365/CVE-2022): CVE-2022-42889 [#7313](https://github.com/jaroop/agentcloud/pull/7313) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.59.2 (Thu Dec 21 2023)

#### 🐛 Bug Fix

- [AC-3305](https://salessolutions.atlassian.net/browse/AC-3305): Cleanup work to Exclude PR Leads already associated with inactive partners [#7315](https://github.com/jaroop/agentcloud/pull/7315) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.59.1 (Wed Dec 20 2023)

#### 🐛 Bug Fix

- [AC-3390](https://salessolutions.atlassian.net/browse/AC-3390): DOB in "Insured" Section in the Opp Record can't be edited manually [#7312](https://github.com/jaroop/agentcloud/pull/7312) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.59.0 (Wed Dec 20 2023)

#### 🚀 Enhancement

- [AC-3353](https://salessolutions.atlassian.net/browse/AC-3353): Add Partner as argument to collation [#7316](https://github.com/jaroop/agentcloud/pull/7316) ([@AndreiMazu](https://github.com/AndreiMazu))

#### 🐛 Bug Fix

- [AC-3402](https://salessolutions.atlassian.net/browse/AC-3402): Switching lead status from Ready to Dial to Active [#7314](https://github.com/jaroop/agentcloud/pull/7314) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🏠 Internal

- [TOPS-417](https://salessolutions.atlassian.net/browse/TOPS-417): Patching image to close vulnerabilities [#7307](https://github.com/jaroop/agentcloud/pull/7307) ([@Gopalkrishnaaccuquote](https://github.com/Gopalkrishnaaccuquote))

#### Authors: 3

- [@Gopalkrishnaaccuquote](https://github.com/Gopalkrishnaaccuquote)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.58.13 (Tue Dec 19 2023)

#### 🐛 Bug Fix

- [TOPS-638](https://salessolutions.atlassian.net/browse/TOPS-638): Update Base Java Image on AgentCloud [#7303](https://github.com/jaroop/agentcloud/pull/7303) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-1695](https://salessolutions.atlassian.net/browse/AC-1695): script to fix QA environment corruption [#7256](https://github.com/jaroop/agentcloud/pull/7256) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-3357](https://salessolutions.atlassian.net/browse/AC-3357): Create record for the Jornaya profile based on info in the call [#7305](https://github.com/jaroop/agentcloud/pull/7305) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.58.12 (Fri Dec 15 2023)

#### 🐛 Bug Fix

- [AC-3093](https://salessolutions.atlassian.net/browse/AC-3093): Deletion script upgrades [#7304](https://github.com/jaroop/agentcloud/pull/7304) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.58.11 (Thu Dec 14 2023)

#### 🐛 Bug Fix

- [AC-1952](https://salessolutions.atlassian.net/browse/AC-1952): fixed bugs [#7299](https://github.com/jaroop/agentcloud/pull/7299) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-1790](https://salessolutions.atlassian.net/browse/AC-1790): adding logging for frequent dialer list task runner [#7261](https://github.com/jaroop/agentcloud/pull/7261) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 2

- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.58.10 (Thu Dec 14 2023)

#### 🐛 Bug Fix

- [AC-3303](https://salessolutions.atlassian.net/browse/AC-3303): Disallow creation of PR Leads based on old opportunity's partner [#7278](https://github.com/jaroop/agentcloud/pull/7278) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🏠 Internal

- Run circleci pipeline-web-publish job conditionally; change resource class to large [#7291](https://github.com/jaroop/agentcloud/pull/7291) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.58.9 (Tue Dec 12 2023)

#### 🐛 Bug Fix

- [AC-3391](https://salessolutions.atlassian.net/browse/AC-3391): AAA Database updates [#7297](https://github.com/jaroop/agentcloud/pull/7297) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- [AC-2917](https://salessolutions.atlassian.net/browse/AC-2917): Fix silent merge conflict [#7298](https://github.com/jaroop/agentcloud/pull/7298) ([@leaveller](https://github.com/leaveller))
- [AC-2917](https://salessolutions.atlassian.net/browse/AC-2917): Conversion Policy Retrieval Service (PR) [#7296](https://github.com/jaroop/agentcloud/pull/7296) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 4

- [@leaveller](https://github.com/leaveller)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))
- Valentyn Protsenko (<EMAIL>)

---

# v5.58.8 (Tue Dec 12 2023)

#### 🐛 Bug Fix

- [AC-2969](https://salessolutions.atlassian.net/browse/AC-2969): Display Migrated Data In New Policy Details Tab [#7289](https://github.com/jaroop/agentcloud/pull/7289) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.58.7 (Tue Dec 12 2023)

#### 🐛 Bug Fix

- [AC-3392](https://salessolutions.atlassian.net/browse/AC-3392): Production issue: Fix offer upload process [#7295](https://github.com/jaroop/agentcloud/pull/7295) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.58.6 (Mon Dec 11 2023)

#### 🐛 Bug Fix

- Dummy PR to create release version for [AC-3334](https://salessolutions.atlassian.net/browse/AC-3334): . [#7293](https://github.com/jaroop/agentcloud/pull/7293) ([@swoosh1337](https://github.com/swoosh1337))
- [AC-3334](https://salessolutions.atlassian.net/browse/AC-3334): United of Omaha Childrens Policy showing Globe LAIC as the crrier in the Task [#7292](https://github.com/jaroop/agentcloud/pull/7292) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.58.5 (Sat Dec 09 2023)

#### 🐛 Bug Fix

- Empty commit to test pipeline end-to-end + bump version [#7290](https://github.com/jaroop/agentcloud/pull/7290) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3321](https://salessolutions.atlassian.net/browse/AC-3321): CallMiner endpoint logs the number of entries in CallMiner table that are queued [#7275](https://github.com/jaroop/agentcloud/pull/7275) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- irakli. ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.58.4 (Fri Dec 08 2023)

#### 🐛 Bug Fix

- [TOPS-415,CVE-2023](https://salessolutions.atlassian.net/browse/TOPS-415/CVE-2023): upgrade os packages to close CVE-2023-44487 [#7273](https://github.com/jaroop/agentcloud/pull/7273) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3362](https://salessolutions.atlassian.net/browse/AC-3362): Reactivate leads for AAA [#7280](https://github.com/jaroop/agentcloud/pull/7280) ([@COtlowski](https://github.com/COtlowski))
- Upgrade CI images to iac-agentcloud v6.6.1 [#7264](https://github.com/jaroop/agentcloud/pull/7264) ([@nnovaeshc](https://github.com/nnovaeshc))
- [TOPS-536](https://salessolutions.atlassian.net/browse/TOPS-536): Make /scripts available from DMZ [#7267](https://github.com/jaroop/agentcloud/pull/7267) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🏠 Internal

- enable deploy-to-idev [#7279](https://github.com/jaroop/agentcloud/pull/7279) ([@nnovaeshc](https://github.com/nnovaeshc))
- temporarily disable deploy-to-idev [#7266](https://github.com/jaroop/agentcloud/pull/7266) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.58.3 (Thu Dec 07 2023)

#### 🐛 Bug Fix

- Update base images for agentcloud-web [#7268](https://github.com/jaroop/agentcloud/pull/7268) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.58.2 (Wed Dec 06 2023)

#### 🐛 Bug Fix

- Revert PRs for changing base image to run agentcloud [#7272](https://github.com/jaroop/agentcloud/pull/7272) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3386](https://salessolutions.atlassian.net/browse/AC-3386): Fix applied policy validation message [#7271](https://github.com/jaroop/agentcloud/pull/7271) ([@COtlowski](https://github.com/COtlowski))
- change baseimage from amazoncorretto:11 to amazoncorretto:11-alpine [#7270](https://github.com/jaroop/agentcloud/pull/7270) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.58.1 (Tue Dec 05 2023)

#### 🐛 Bug Fix

- [TOPS-414](https://salessolutions.atlassian.net/browse/TOPS-414): change base image to run agentcloud [#7262](https://github.com/jaroop/agentcloud/pull/7262) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.58.0 (Mon Dec 04 2023)

#### 🚀 Enhancement

- [AC-3302](https://salessolutions.atlassian.net/browse/AC-3302): Database work to add a column called "allow_pr" to eap.partners [#7257](https://github.com/jaroop/agentcloud/pull/7257) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### 🐛 Bug Fix

- [AC-3267](https://salessolutions.atlassian.net/browse/AC-3267): Add AgentCloud deletion scripts [#7255](https://github.com/jaroop/agentcloud/pull/7255) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.57.14 (Thu Nov 30 2023)

#### 🐛 Bug Fix

- [AC-3275](https://salessolutions.atlassian.net/browse/AC-3275): Work to get Opportunity data into CallMiner table [#7254](https://github.com/jaroop/agentcloud/pull/7254) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.57.13 (Thu Nov 30 2023)

#### 🐛 Bug Fix

- [AC-3262](https://salessolutions.atlassian.net/browse/AC-3262): Add Replacing Policy to App Completed Validation - QA fixes 2 [#7253](https://github.com/jaroop/agentcloud/pull/7253) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.57.12 (Thu Nov 30 2023)

#### 🐛 Bug Fix

- [AC-3275](https://salessolutions.atlassian.net/browse/AC-3275): Work to get Opportunity data into CallMiner table [#7252](https://github.com/jaroop/agentcloud/pull/7252) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- Add UAT2 and Training2 config files [#7203](https://github.com/jaroop/agentcloud/pull/7203) ([@nnovaeshc](https://github.com/nnovaeshc))
- Re-enable sbt testQuick [#7251](https://github.com/jaroop/agentcloud/pull/7251) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@nnovaeshc](https://github.com/nnovaeshc)
- balanacqt (<EMAIL>)

---

# v5.57.11 (Wed Nov 29 2023)

#### 🐛 Bug Fix

- change agentcloud base image [#7247](https://github.com/jaroop/agentcloud/pull/7247) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.57.10 (Wed Nov 29 2023)

#### 🐛 Bug Fix

- [AC-3262](https://salessolutions.atlassian.net/browse/AC-3262): Add Replacing Policy to App Completed Validation - QA fixes [#7249](https://github.com/jaroop/agentcloud/pull/7249) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.57.9 (Wed Nov 29 2023)

#### 🐛 Bug Fix

- [AC-3275](https://salessolutions.atlassian.net/browse/AC-3275): Work to get Opportunity data into CallMiner table [#7232](https://github.com/jaroop/agentcloud/pull/7232) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.57.8 (Tue Nov 28 2023)

#### 🐛 Bug Fix

- [AC-3267](https://salessolutions.atlassian.net/browse/AC-3267): Remove duplicate queries for deletion [#7246](https://github.com/jaroop/agentcloud/pull/7246) ([@COtlowski](https://github.com/COtlowski))

#### 🏠 Internal

- change pipeline web so it only run tests when agentcloud-web files ch… [#7248](https://github.com/jaroop/agentcloud/pull/7248) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.57.7 (Mon Nov 27 2023)

#### 🐛 Bug Fix

- Pipeline - Path filtering and pipeline updates 11/2023 [#7204](https://github.com/jaroop/agentcloud/pull/7204) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.57.6 (Mon Nov 27 2023)

#### 🐛 Bug Fix

- [AC-3337](https://salessolutions.atlassian.net/browse/AC-3337): UAT Feedback - Create CallMiner Table [#7234](https://github.com/jaroop/agentcloud/pull/7234) ([@umeshaq](https://github.com/umeshaq))
- [AC-3335](https://salessolutions.atlassian.net/browse/AC-3335): [Lead Management] The 'Partner' dropdown is not sorting data. [#7216](https://github.com/jaroop/agentcloud/pull/7216) ([@swoosh1337](https://github.com/swoosh1337))
- [TOPS-591](https://salessolutions.atlassian.net/browse/TOPS-591): Upgrade agentcloud-worker / agentcloud-server memory to 32GB [#7242](https://github.com/jaroop/agentcloud/pull/7242) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🏠 Internal

- [AC-3316](https://salessolutions.atlassian.net/browse/AC-3316): Remove references to old CI image - MAIN [#7230](https://github.com/jaroop/agentcloud/pull/7230) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- irakli. ([@swoosh1337](https://github.com/swoosh1337))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.57.5 (Thu Nov 23 2023)

#### 🐛 Bug Fix

- [AC-3211](https://salessolutions.atlassian.net/browse/AC-3211): Upgrade Node.js to version 18 [#7224](https://github.com/jaroop/agentcloud/pull/7224) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.57.4 (Wed Nov 22 2023)

#### 🐛 Bug Fix

- Revert the column rename change [AC-3274](https://salessolutions.atlassian.net/browse/AC-3274): Create CallMiner Table [#7229](https://github.com/jaroop/agentcloud/pull/7229) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.57.3 (Wed Nov 22 2023)

#### 🐛 Bug Fix

- [AC-3309](https://salessolutions.atlassian.net/browse/AC-3309): Create CallMiner Cron-Job and endpoint [#7220](https://github.com/jaroop/agentcloud/pull/7220) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.57.2 (Tue Nov 21 2023)

#### 🐛 Bug Fix

- [AC-3267](https://salessolutions.atlassian.net/browse/AC-3267): Add validation generation script [#7219](https://github.com/jaroop/agentcloud/pull/7219) ([@COtlowski](https://github.com/COtlowski))

#### 🏠 Internal

- [AC-3316](https://salessolutions.atlassian.net/browse/AC-3316): Remove references to old CI image in all agentcloud active branches [#7222](https://github.com/jaroop/agentcloud/pull/7222) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 2

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.57.1 (Tue Nov 21 2023)

#### 🐛 Bug Fix

- [AC-3274](https://salessolutions.atlassian.net/browse/AC-3274): Create CallMiner Table [#7221](https://github.com/jaroop/agentcloud/pull/7221) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.57.0 (Tue Nov 21 2023)

#### 🚀 Enhancement

- [AC-3320](https://salessolutions.atlassian.net/browse/AC-3320): Update CallMiner Status Domain [#7217](https://github.com/jaroop/agentcloud/pull/7217) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.56.5 (Fri Nov 17 2023)

#### 🐛 Bug Fix

- [AC-2951](https://salessolutions.atlassian.net/browse/AC-2951): Upgrade React-Scripts to v5 [#7214](https://github.com/jaroop/agentcloud/pull/7214) ([@AndreiMazu](https://github.com/AndreiMazu) [@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.56.4 (Fri Nov 17 2023)

#### 🐛 Bug Fix

- [AC-2949](https://salessolutions.atlassian.net/browse/AC-2949): Upgrade React Flow to v11 [#7177](https://github.com/jaroop/agentcloud/pull/7177) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.56.3 (Thu Nov 16 2023)

#### 🐛 Bug Fix

- [AC-3013](https://salessolutions.atlassian.net/browse/AC-3013): AAA Opportunities should only receive AAA Branded Email Templ… [#7191](https://github.com/jaroop/agentcloud/pull/7191) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-3085](https://salessolutions.atlassian.net/browse/AC-3085): Fixing HC data deletion script. [#7208](https://github.com/jaroop/agentcloud/pull/7208) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 3

- [@sunilcs1](https://github.com/sunilcs1)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Sunil Kumar C S (<EMAIL>)

---

# v5.56.2 (Thu Nov 16 2023)

#### 🐛 Bug Fix

- [AC-3327](https://salessolutions.atlassian.net/browse/AC-3327): AAA Journey Changes - Additional field [#7209](https://github.com/jaroop/agentcloud/pull/7209) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.56.1 (Wed Nov 15 2023)

#### 🐛 Bug Fix

- [AC-3327](https://salessolutions.atlassian.net/browse/AC-3327): AAA Journey Changes - Math expressions [#7201](https://github.com/jaroop/agentcloud/pull/7201) ([@AndreiMazu](https://github.com/AndreiMazu))
- remove references to the old dev environment from agentcloud repo [#7198](https://github.com/jaroop/agentcloud/pull/7198) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.56.0 (Wed Nov 15 2023)

#### 🚀 Enhancement

- [AC-3085](https://salessolutions.atlassian.net/browse/AC-3085): Generate script to delete HC data [#7197](https://github.com/jaroop/agentcloud/pull/7197) ([@COtlowski](https://github.com/COtlowski))

#### 🐛 Bug Fix

- [TOPS-524](https://salessolutions.atlassian.net/browse/TOPS-524): add assume role flag to aws eks update-kubeconfig [#7202](https://github.com/jaroop/agentcloud/pull/7202) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3298](https://salessolutions.atlassian.net/browse/AC-3298): Fix TokenType on OAuthToken in jaroop-core-web-services [#7200](https://github.com/jaroop/agentcloud/pull/7200) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Valentyn Protsenko (<EMAIL>)

---

# v5.55.23 (Mon Nov 13 2023)

#### 🐛 Bug Fix

- [AC-3257](https://salessolutions.atlassian.net/browse/AC-3257): Lead Management Page Query Improvements [#7176](https://github.com/jaroop/agentcloud/pull/7176) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.55.22 (Fri Nov 10 2023)

#### 🐛 Bug Fix

- [AC-3298](https://salessolutions.atlassian.net/browse/AC-3298): Fix TokenType on OAuthToken in jaroop-core-web-services [#7175](https://github.com/jaroop/agentcloud/pull/7175) (<EMAIL> [@nnovaeshc](https://github.com/nnovaeshc) [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.55.21 (Fri Nov 10 2023)

#### 🐛 Bug Fix

- [AC-3299](https://salessolutions.atlassian.net/browse/AC-3299): AAA Journey Changes [#7180](https://github.com/jaroop/agentcloud/pull/7180) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.55.20 (Thu Nov 09 2023)

#### 🐛 Bug Fix

- [AC-2925](https://salessolutions.atlassian.net/browse/AC-2925): Update triggers for policy details [#7179](https://github.com/jaroop/agentcloud/pull/7179) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.55.19 (Wed Nov 08 2023)

#### 🐛 Bug Fix

- [AC-1783](https://salessolutions.atlassian.net/browse/AC-1783): Replace <EMAIL> usage [#7171](https://github.com/jaroop/agentcloud/pull/7171) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.55.18 (Wed Nov 08 2023)

#### 🐛 Bug Fix

- [AC-3274](https://salessolutions.atlassian.net/browse/AC-3274): Create CallMiner Table [#7173](https://github.com/jaroop/agentcloud/pull/7173) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.55.17 (Tue Nov 07 2023)

#### 🐛 Bug Fix

- [AC-3262](https://salessolutions.atlassian.net/browse/AC-3262): Add Replacing Policy to App Completed Validation [#7166](https://github.com/jaroop/agentcloud/pull/7166) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Remove alpine scratch images [#7174](https://github.com/jaroop/agentcloud/pull/7174) ([@nnovaeshc](https://github.com/nnovaeshc))
- update CI image versions [#7114](https://github.com/jaroop/agentcloud/pull/7114) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.55.16 (Mon Nov 06 2023)

#### 🐛 Bug Fix

- [AC-3139](https://salessolutions.atlassian.net/browse/AC-3139): Flaky Test: test_opportunity_details.py [#7165](https://github.com/jaroop/agentcloud/pull/7165) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.55.15 (Fri Nov 03 2023)

#### 🐛 Bug Fix

- fix nginx.conf on agentcloud-web [#7163](https://github.com/jaroop/agentcloud/pull/7163) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.55.14 (Thu Nov 02 2023)

#### 🐛 Bug Fix

- [AC-3229](https://salessolutions.atlassian.net/browse/AC-3229): Make Phone Number FIeld on TCPA Node Uneditable [#7164](https://github.com/jaroop/agentcloud/pull/7164) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.55.13 (Thu Nov 02 2023)

#### 🐛 Bug Fix

- [AC-3229](https://salessolutions.atlassian.net/browse/AC-3229): Make Phone Number FIeld on TCPA Node Uneditable [#7162](https://github.com/jaroop/agentcloud/pull/7162) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.55.12 (Wed Nov 01 2023)

#### 🐛 Bug Fix

- [AC-2893](https://salessolutions.atlassian.net/browse/AC-2893): Prohibit User from Creating Opportunity if not Licensed [#7160](https://github.com/jaroop/agentcloud/pull/7160) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 1

- tazeey ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.55.11 (Wed Nov 01 2023)

#### 🐛 Bug Fix

- [AC-1952](https://salessolutions.atlassian.net/browse/AC-1952): Fix Typescript 4 issue [#7161](https://github.com/jaroop/agentcloud/pull/7161) ([@leaveller](https://github.com/leaveller))
- [AC-1952](https://salessolutions.atlassian.net/browse/AC-1952): Edit links on journey edges [#7153](https://github.com/jaroop/agentcloud/pull/7153) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-3269](https://salessolutions.atlassian.net/browse/AC-3269): Flaky Test: test_opportunity_details_routing.py - fix [#7159](https://github.com/jaroop/agentcloud/pull/7159) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 4

- [@leaveller](https://github.com/leaveller)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Suraj Amaranarayana (<EMAIL>)

---

# v5.55.10 (Wed Nov 01 2023)

#### 🐛 Bug Fix

- [AC-2026](https://salessolutions.atlassian.net/browse/AC-2026): Broken prefilling Nodes on Legacy Engine journeys after template [#7158](https://github.com/jaroop/agentcloud/pull/7158) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.55.9 (Wed Nov 01 2023)

#### 🐛 Bug Fix

- [AC-3229](https://salessolutions.atlassian.net/browse/AC-3229): Make Phone Number FIeld on TCPA Node Uneditable [#7127](https://github.com/jaroop/agentcloud/pull/7127) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- [AC-1695](https://salessolutions.atlassian.net/browse/AC-1695): Addressing QA feedback and preparing for rollback script. [#7125](https://github.com/jaroop/agentcloud/pull/7125) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 3

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.55.8 (Tue Oct 31 2023)

#### 🐛 Bug Fix

- [AC-2912](https://salessolutions.atlassian.net/browse/AC-2912): Change OpportunityAppComplete run freqency [#7157](https://github.com/jaroop/agentcloud/pull/7157) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.55.7 (Tue Oct 31 2023)

#### 🐛 Bug Fix

- [AC-2026](https://salessolutions.atlassian.net/browse/AC-2026): Broken prefilling Nodes on Legacy Engine journeys [#7154](https://github.com/jaroop/agentcloud/pull/7154) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-3269](https://salessolutions.atlassian.net/browse/AC-3269): Flaky Test: test_opportunity_details_routing.py [#7144](https://github.com/jaroop/agentcloud/pull/7144) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2912](https://salessolutions.atlassian.net/browse/AC-2912): Change Lumico AppComplete task runner [#7156](https://github.com/jaroop/agentcloud/pull/7156) ([@leaveller](https://github.com/leaveller) [@COtlowski](https://github.com/COtlowski))

#### Authors: 5

- [@balanacqt](https://github.com/balanacqt)
- [@leaveller](https://github.com/leaveller)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.55.6 (Mon Oct 30 2023)

#### 🐛 Bug Fix

- [AC-3187](https://salessolutions.atlassian.net/browse/AC-3187): Add New Replacement Field to Selected KPI Report [#7155](https://github.com/jaroop/agentcloud/pull/7155) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.55.5 (Mon Oct 30 2023)

#### 🐛 Bug Fix

- [AC-3199](https://salessolutions.atlassian.net/browse/AC-3199): Create Audit History for Manager Date Banding [#7145](https://github.com/jaroop/agentcloud/pull/7145) ([@umeshaq](https://github.com/umeshaq))
- create vagrantfile for deploying serverless via AWS SAM [#7131](https://github.com/jaroop/agentcloud/pull/7131) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2837](https://salessolutions.atlassian.net/browse/AC-2837): adjusted lambda deployment configuration [#7139](https://github.com/jaroop/agentcloud/pull/7139) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.55.4 (Thu Oct 26 2023)

#### 🐛 Bug Fix

- [AC-3075](https://salessolutions.atlassian.net/browse/AC-3075): Lumico Updater - Set Policy Status to Approved [#7140](https://github.com/jaroop/agentcloud/pull/7140) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.55.3 (Thu Oct 26 2023)

#### 🐛 Bug Fix

- [AC-3255](https://salessolutions.atlassian.net/browse/AC-3255): Update AAA DNC report with feedback [#7138](https://github.com/jaroop/agentcloud/pull/7138) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.55.2 (Wed Oct 25 2023)

#### 🐛 Bug Fix

- [AC-3227](https://salessolutions.atlassian.net/browse/AC-3227): QA fixes - preventing the dropdown from reappearing on Enter [#7137](https://github.com/jaroop/agentcloud/pull/7137) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.55.1 (Tue Oct 24 2023)

#### 🐛 Bug Fix

- [AC-3249](https://salessolutions.atlassian.net/browse/AC-3249): Adjust time TCPA Task Runner runs to 11:00 am UTC [#7134](https://github.com/jaroop/agentcloud/pull/7134) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.55.0 (Tue Oct 24 2023)

#### 🚀 Enhancement

- [AC-3213](https://salessolutions.atlassian.net/browse/AC-3213): Added new dispositions to reporting [#7132](https://github.com/jaroop/agentcloud/pull/7132) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.54.17 (Tue Oct 24 2023)

#### 🐛 Bug Fix

- [AC-2952](https://salessolutions.atlassian.net/browse/AC-2952): Upgrade TypeScript to v4.9.5 [#7129](https://github.com/jaroop/agentcloud/pull/7129) ([@COtlowski](https://github.com/COtlowski))
- [AC-3227](https://salessolutions.atlassian.net/browse/AC-3227): Search Functionality Enhancements on Lead Management [#7128](https://github.com/jaroop/agentcloud/pull/7128) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 2

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.54.16 (Mon Oct 23 2023)

#### 🐛 Bug Fix

- [AC-3228](https://salessolutions.atlassian.net/browse/AC-3228): Create : Policy Review Distinct Prudential Dialer List [#7119](https://github.com/jaroop/agentcloud/pull/7119) ([@AndreiMazu](https://github.com/AndreiMazu))
- rename transfer auth lambda [#7124](https://github.com/jaroop/agentcloud/pull/7124) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.54.15 (Fri Oct 20 2023)

#### 🐛 Bug Fix

- [AC-2921](https://salessolutions.atlassian.net/browse/AC-2921): Policy Pre-Filler [#7123](https://github.com/jaroop/agentcloud/pull/7123) ([@umeshaq](https://github.com/umeshaq))
- [AC-2921](https://salessolutions.atlassian.net/browse/AC-2921): Policy Pre-Filler [#7122](https://github.com/jaroop/agentcloud/pull/7122) ([@umeshaq](https://github.com/umeshaq))

#### 🏠 Internal

- replace old pipeline image on MAIN branch [#7115](https://github.com/jaroop/agentcloud/pull/7115) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.54.14 (Thu Oct 19 2023)

#### 🐛 Bug Fix

- [AC-2914](https://salessolutions.atlassian.net/browse/AC-2914): Inforce Opportunity New Policy Details update [#7118](https://github.com/jaroop/agentcloud/pull/7118) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.54.13 (Thu Oct 19 2023)

#### 🐛 Bug Fix

- [AC-2921](https://salessolutions.atlassian.net/browse/AC-2921): Empty commit to include lables for PR [#7120](https://github.com/jaroop/agentcloud/pull/7120) ([@umeshaq](https://github.com/umeshaq))
- [AC-2837](https://salessolutions.atlassian.net/browse/AC-2837): Fixed whitespace bug for Decryption [#7111](https://github.com/jaroop/agentcloud/pull/7111) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-2921](https://salessolutions.atlassian.net/browse/AC-2921): Policy Pre-Filler [#7112](https://github.com/jaroop/agentcloud/pull/7112) ([@umeshaq](https://github.com/umeshaq))
- Move lead-details-snap to agentcloud repository [#7091](https://github.com/jaroop/agentcloud/pull/7091) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.54.12 (Wed Oct 18 2023)

#### 🐛 Bug Fix

- [AC-3221](https://salessolutions.atlassian.net/browse/AC-3221): Create : Policy Review Distinct Protective Dialer List [#7110](https://github.com/jaroop/agentcloud/pull/7110) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.54.11 (Wed Oct 18 2023)

#### 🐛 Bug Fix

- [AC-2837](https://salessolutions.atlassian.net/browse/AC-2837): Lumico Email Notifications Not Firing [#7082](https://github.com/jaroop/agentcloud/pull/7082) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.54.10 (Tue Oct 17 2023)

#### 🐛 Bug Fix

- Bump Patch Version for AgentCloud [#7109](https://github.com/jaroop/agentcloud/pull/7109) ([@COtlowski](https://github.com/COtlowski))
- [AC-3186](https://salessolutions.atlassian.net/browse/AC-3186): Fix the CallMiner credentials for non-prod [#7108](https://github.com/jaroop/agentcloud/pull/7108) ([@COtlowski](https://github.com/COtlowski))
- [AC-1686](https://salessolutions.atlassian.net/browse/AC-1686): Fix lead_detail view middle name issue [#7107](https://github.com/jaroop/agentcloud/pull/7107) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 2

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.54.9 (Tue Oct 17 2023)

#### 🐛 Bug Fix

- [AC-3186](https://salessolutions.atlassian.net/browse/AC-3186): Fix CallMiner Credentials [#7106](https://github.com/jaroop/agentcloud/pull/7106) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.54.8 (Mon Oct 16 2023)

#### 🐛 Bug Fix

- remove latest version from CI images [#7095](https://github.com/jaroop/agentcloud/pull/7095) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.54.7 (Mon Oct 16 2023)

#### 🐛 Bug Fix

- [AC-2914](https://salessolutions.atlassian.net/browse/AC-2914): Inforce list opportunity update details into new policy details tables [#7098](https://github.com/jaroop/agentcloud/pull/7098) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-1783](https://salessolutions.atlassian.net/browse/AC-1783): Replace <EMAIL> usage [#7104](https://github.com/jaroop/agentcloud/pull/7104) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@srkothapalli-aq](https://github.com/srkothapalli-aq)
- balanacqt (<EMAIL>)

---

# v5.54.6 (Mon Oct 16 2023)

#### 🐛 Bug Fix

- [AC-3200](https://salessolutions.atlassian.net/browse/AC-3200): Remove Unconverted Duplicate AAA leads from the Dialer [#7103](https://github.com/jaroop/agentcloud/pull/7103) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.54.5 (Fri Oct 13 2023)

#### 🐛 Bug Fix

- [AC-3065](https://salessolutions.atlassian.net/browse/AC-3065): Setup Automations for how Leads are handled in Active or Read… [#7099](https://github.com/jaroop/agentcloud/pull/7099) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.54.4 (Fri Oct 13 2023)

#### 🐛 Bug Fix

- [AC-3205](https://salessolutions.atlassian.net/browse/AC-3205): Always return true for DNC column in AAA DNC report [#7097](https://github.com/jaroop/agentcloud/pull/7097) ([@leaveller](https://github.com/leaveller))
- [AC-3065](https://salessolutions.atlassian.net/browse/AC-3065): Setup Automations for how Leads are handled in Active or Read… [#7096](https://github.com/jaroop/agentcloud/pull/7096) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.54.3 (Thu Oct 12 2023)

#### 🐛 Bug Fix

- [AC-3065](https://salessolutions.atlassian.net/browse/AC-3065): Setup Automations for how Leads are handled in Active or Read… [#7088](https://github.com/jaroop/agentcloud/pull/7088) ([@umeshaq](https://github.com/umeshaq))
- Dev retro secret pr template change [#7087](https://github.com/jaroop/agentcloud/pull/7087) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.54.2 (Wed Oct 11 2023)

#### 🐛 Bug Fix

- [AC-3105](https://salessolutions.atlassian.net/browse/AC-3105): Remove logic to prevent job from running [#7092](https://github.com/jaroop/agentcloud/pull/7092) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.54.1 (Tue Oct 10 2023)

#### 🐛 Bug Fix

- [AC-2890](https://salessolutions.atlassian.net/browse/AC-2890): Fix the CarrierExclusions unit test flakiness [#7083](https://github.com/jaroop/agentcloud/pull/7083) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.54.0 (Tue Oct 10 2023)

#### 🚀 Enhancement

- [AC-3105](https://salessolutions.atlassian.net/browse/AC-3105): Add AgentCloud DNC list to AAA DNC Report [#7081](https://github.com/jaroop/agentcloud/pull/7081) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.53.21 (Sun Oct 08 2023)

#### 🐛 Bug Fix

- Turn back ON allowNewPolicyDetails flag on QA2 after testing [#7078](https://github.com/jaroop/agentcloud/pull/7078) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.53.20 (Sat Oct 07 2023)

#### 🐛 Bug Fix

- [AC-3074](https://salessolutions.atlassian.net/browse/AC-3074): Setting policy type into new policy details policy type [#7079](https://github.com/jaroop/agentcloud/pull/7079) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.53.19 (Sat Oct 07 2023)

#### 🐛 Bug Fix

- Turn OFF allowNewPolicyDetails flag on QA2 temporary for testing [#7077](https://github.com/jaroop/agentcloud/pull/7077) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.53.18 (Sat Oct 07 2023)

#### 🐛 Bug Fix

- [AC-3704](https://salessolutions.atlassian.net/browse/AC-3704): BOB Uploader Set Policy status [#7076](https://github.com/jaroop/agentcloud/pull/7076) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))

#### Authors: 1

- [@srkothapalli-aq](https://github.com/srkothapalli-aq)

---

# v5.53.17 (Sat Oct 07 2023)

#### 🐛 Bug Fix

- Turn ON Fake Lumico GW on QA2 [#7075](https://github.com/jaroop/agentcloud/pull/7075) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- Turn ON Fake Lumico GW on QA2 [#7073](https://github.com/jaroop/agentcloud/pull/7073) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- upgrade terraform CI image from 1.5.5 to 1.5.7 [#7074](https://github.com/jaroop/agentcloud/pull/7074) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2916](https://salessolutions.atlassian.net/browse/AC-2916): Lumico Changes for Policy Model Redesign [#7072](https://github.com/jaroop/agentcloud/pull/7072) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- [AC-3074](https://salessolutions.atlassian.net/browse/AC-3074): BOB Uploader - Set Policy Status [#7071](https://github.com/jaroop/agentcloud/pull/7071) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@srkothapalli-aq](https://github.com/srkothapalli-aq)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.53.16 (Fri Oct 06 2023)

#### 🐛 Bug Fix

- [AC-2916](https://salessolutions.atlassian.net/browse/AC-2916): Lumico Changes for Policy Model Redesign [#7068](https://github.com/jaroop/agentcloud/pull/7068) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### Authors: 2

- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.53.15 (Fri Oct 06 2023)

#### 🐛 Bug Fix

- [AC-3105](https://salessolutions.atlassian.net/browse/AC-3105): Fix query getting AAA DNC phone numbers [#7069](https://github.com/jaroop/agentcloud/pull/7069) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.53.14 (Thu Oct 05 2023)

#### 🐛 Bug Fix

- [AC-3056](https://salessolutions.atlassian.net/browse/AC-3056): Expose INFO logging for Five9ListService [#7067](https://github.com/jaroop/agentcloud/pull/7067) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.53.13 (Thu Oct 05 2023)

#### 🐛 Bug Fix

- fix version bump logic [#7065](https://github.com/jaroop/agentcloud/pull/7065) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.53.12 (Thu Oct 05 2023)

#### 🐛 Bug Fix

- [AC-2916](https://salessolutions.atlassian.net/browse/AC-2916): Lumico Changes for Policy Model Redesign [#7066](https://github.com/jaroop/agentcloud/pull/7066) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- bump versions only after images were published [#7063](https://github.com/jaroop/agentcloud/pull/7063) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Valentyn Protsenko (<EMAIL>)

---

# v5.53.11 (Tue Oct 03 2023)

#### 🐛 Bug Fix

- [AC-2916](https://salessolutions.atlassian.net/browse/AC-2916): Lumico Changes for Policy Model Redesign [#7059](https://github.com/jaroop/agentcloud/pull/7059) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- [AC-1639](https://salessolutions.atlassian.net/browse/AC-1639): Removed logging statement from batch_data_helper.py [#7062](https://github.com/jaroop/agentcloud/pull/7062) (<EMAIL> [@nnovaeshc](https://github.com/nnovaeshc) [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 5

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@SurajKomarla](https://github.com/SurajKomarla)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Suraj Amaranarayana (<EMAIL>)
- Valentyn Protsenko (<EMAIL>)

---

# v5.53.10 (Tue Oct 03 2023)

#### 🐛 Bug Fix

- [AC-1639](https://salessolutions.atlassian.net/browse/AC-1639): added logging for batch_data_helpers [#7058](https://github.com/jaroop/agentcloud/pull/7058) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-3105](https://salessolutions.atlassian.net/browse/AC-3105): Quick fix for the AAA DNC endpoint return [#7061](https://github.com/jaroop/agentcloud/pull/7061) ([@COtlowski](https://github.com/COtlowski))

#### 📝 Documentation

- update values.yaml with instructions on iac-agentcloud overrides [#7034](https://github.com/jaroop/agentcloud/pull/7034) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Suraj Amaranarayana (<EMAIL>)

---

# v5.53.9 (Mon Oct 02 2023)

#### 🐛 Bug Fix

- [AC-3105](https://salessolutions.atlassian.net/browse/AC-3105): Fix result strings for the AAA DNC Report [#7055](https://github.com/jaroop/agentcloud/pull/7055) ([@COtlowski](https://github.com/COtlowski))
- [AC-3056](https://salessolutions.atlassian.net/browse/AC-3056): Five9 integration fixes [#7051](https://github.com/jaroop/agentcloud/pull/7051) ([@leaveller](https://github.com/leaveller) [@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@leaveller](https://github.com/leaveller)
- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.53.8 (Mon Oct 02 2023)

#### 🐛 Bug Fix

- [AC-1639](https://salessolutions.atlassian.net/browse/AC-1639): changes for batch_helper to ensure directory is created if not present [#7053](https://github.com/jaroop/agentcloud/pull/7053) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-3067](https://salessolutions.atlassian.net/browse/AC-3067): Add Date of Birth Validation Reminder to AAA OPP/Lead Headers [#7040](https://github.com/jaroop/agentcloud/pull/7040) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 3

- [@SurajKomarla](https://github.com/SurajKomarla)
- irakli ([@swoosh1337](https://github.com/swoosh1337))
- Suraj Amaranarayana (<EMAIL>)

---

# v5.53.7 (Mon Oct 02 2023)

#### 🐛 Bug Fix

- add earthly git branch variable to Earthfile [#7054](https://github.com/jaroop/agentcloud/pull/7054) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🏠 Internal

- cache yarn install images on push to main [#7049](https://github.com/jaroop/agentcloud/pull/7049) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.53.6 (Sat Sep 30 2023)

#### 🐛 Bug Fix

- [AC-2906](https://salessolutions.atlassian.net/browse/AC-2906): App Taken Task Creation function Update [#7037](https://github.com/jaroop/agentcloud/pull/7037) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.53.5 (Sat Sep 30 2023)

#### 🐛 Bug Fix

- [AC-3105](https://salessolutions.atlassian.net/browse/AC-3105): Add an endpoint for the AAA DNC Report. [#7032](https://github.com/jaroop/agentcloud/pull/7032) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.53.4 (Fri Sep 29 2023)

#### 🐛 Bug Fix

- pipeline Patch 0929 - 3 [#7044](https://github.com/jaroop/agentcloud/pull/7044) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-1699](https://salessolutions.atlassian.net/browse/AC-1699): migrate reliashield policy details [#7042](https://github.com/jaroop/agentcloud/pull/7042) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- Dev nnovaes pipeline patches0929 [#7043](https://github.com/jaroop/agentcloud/pull/7043) ([@nnovaeshc](https://github.com/nnovaeshc))
- Dev nnovaes pipeline patches0929 [#7041](https://github.com/jaroop/agentcloud/pull/7041) ([@nnovaeshc](https://github.com/nnovaeshc))
- pipeline updates - 092023 [#6988](https://github.com/jaroop/agentcloud/pull/6988) ([@nnovaeshc](https://github.com/nnovaeshc))

#### ⚠️ Pushed to `main`

- activate commit hook ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@nnovaeshc](https://github.com/nnovaeshc)
- balanacqt (<EMAIL>)

---

# v5.53.3 (Thu Sep 28 2023)

#### 🐛 Bug Fix

- Enable IXN LiveGateway in idev [#7028](https://github.com/jaroop/agentcloud/pull/7028) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-3162](https://salessolutions.atlassian.net/browse/AC-3162): Make Leads Ineligible [#7031](https://github.com/jaroop/agentcloud/pull/7031) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 2

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.53.2 (Wed Sep 27 2023)

#### 🐛 Bug Fix

- [AC-3155](https://salessolutions.atlassian.net/browse/AC-3155): Lumico Quotes Not Returning [#7027](https://github.com/jaroop/agentcloud/pull/7027) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.53.1 (Mon Sep 25 2023)

#### 🐛 Bug Fix

- [AC-3155](https://salessolutions.atlassian.net/browse/AC-3155): Lumico Quotes Not Returning [#7023](https://github.com/jaroop/agentcloud/pull/7023) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.53.0 (Sun Sep 24 2023)

#### 🚀 Enhancement

- Rev bump for the start of ACS046. [#7021](https://github.com/jaroop/agentcloud/pull/7021) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.52.21 (Sat Sep 23 2023)

#### 🐛 Bug Fix

- [AC-2898](https://salessolutions.atlassian.net/browse/AC-2898): Refactor Unit Tests [#7018](https://github.com/jaroop/agentcloud/pull/7018) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-2808](https://salessolutions.atlassian.net/browse/AC-2808): Leads Bulk Data Queue Event Publishers/Listeners [#7019](https://github.com/jaroop/agentcloud/pull/7019) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 3

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.52.20 (Sat Sep 23 2023)

#### 🐛 Bug Fix

- [AC-2786](https://salessolutions.atlassian.net/browse/AC-2786): Opportunities Bulk Data [#7017](https://github.com/jaroop/agentcloud/pull/7017) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.52.19 (Sat Sep 23 2023)

#### 🐛 Bug Fix

- [AC-2786](https://salessolutions.atlassian.net/browse/AC-2786): Opportunities Bulk Data [#7000](https://github.com/jaroop/agentcloud/pull/7000) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.52.18 (Fri Sep 22 2023)

#### 🐛 Bug Fix

- [AC-2808](https://salessolutions.atlassian.net/browse/AC-2808): Leads Bulk Data Queue Event Publishers/Listeners [#6996](https://github.com/jaroop/agentcloud/pull/6996) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.52.17 (Fri Sep 22 2023)

#### 🐛 Bug Fix

- [AC-2898](https://salessolutions.atlassian.net/browse/AC-2898): Fixed bug causing contact to become unselected. [#7016](https://github.com/jaroop/agentcloud/pull/7016) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.52.16 (Thu Sep 21 2023)

#### 🐛 Bug Fix

- [AC-2808](https://salessolutions.atlassian.net/browse/AC-2808): Leads Bulk Data Queue Event Publishers/Listeners. [#7009](https://github.com/jaroop/agentcloud/pull/7009) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.52.15 (Thu Sep 21 2023)

#### 🐛 Bug Fix

- Checklist Addition [#7014](https://github.com/jaroop/agentcloud/pull/7014) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-2898](https://salessolutions.atlassian.net/browse/AC-2898): Decoupling extraInstructions from answer submission [#7013](https://github.com/jaroop/agentcloud/pull/7013) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Turn on feature flag for Universal Journey [#7005](https://github.com/jaroop/agentcloud/pull/7005) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 2

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.52.14 (Wed Sep 20 2023)

#### 🐛 Bug Fix

- [AC-2898](https://salessolutions.atlassian.net/browse/AC-2898): Pre-Fill Opportunity - Search & New Contact [#7004](https://github.com/jaroop/agentcloud/pull/7004) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.52.13 (Wed Sep 20 2023)

#### 🐛 Bug Fix

- [AC-2787](https://salessolutions.atlassian.net/browse/AC-2787): Public API Queues Event Publishers/Listeners [#7002](https://github.com/jaroop/agentcloud/pull/7002) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.52.12 (Tue Sep 19 2023)

#### 🐛 Bug Fix

- [AC-1761](https://salessolutions.atlassian.net/browse/AC-1761): Optimized query for reporting.retreaver_information [#7003](https://github.com/jaroop/agentcloud/pull/7003) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 2

- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.52.11 (Tue Sep 19 2023)

#### 🐛 Bug Fix

- [AC-1308](https://salessolutions.atlassian.net/browse/AC-1308): Enhance Task Management - Page Refresh Behavior [#6999](https://github.com/jaroop/agentcloud/pull/6999) ([@AndreiMazu](https://github.com/AndreiMazu))
- BLOCKS OTHER PRs - move caches to their own ECR repository domain [#7001](https://github.com/jaroop/agentcloud/pull/7001) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.52.10 (Mon Sep 18 2023)

#### 🐛 Bug Fix

- [AC-2967](https://salessolutions.atlassian.net/browse/AC-2967): Create Call Campaign Button QA feedback [#6998](https://github.com/jaroop/agentcloud/pull/6998) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 2

- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.52.9 (Mon Sep 18 2023)

#### 🐛 Bug Fix

- [AC-3046](https://salessolutions.atlassian.net/browse/AC-3046): New function to pull latest policy details for type [#6989](https://github.com/jaroop/agentcloud/pull/6989) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.52.8 (Mon Sep 18 2023)

#### 🐛 Bug Fix

- [AC-3047](https://salessolutions.atlassian.net/browse/AC-3047): Create the PolicyDetailsUpdate Event and Listener/Handler [#6986](https://github.com/jaroop/agentcloud/pull/6986) ([@Franksant13](https://github.com/Franksant13))

#### Authors: 1

- [@Franksant13](https://github.com/Franksant13)

---

# v5.52.7 (Mon Sep 18 2023)

#### 🐛 Bug Fix

- [AC-2898](https://salessolutions.atlassian.net/browse/AC-2898): Prefill lead address info on contact in standard journey [#6997](https://github.com/jaroop/agentcloud/pull/6997) ([@leaveller](https://github.com/leaveller))
- [AC-2787](https://salessolutions.atlassian.net/browse/AC-2787): Public API Queues Event Publishers/Listeners [#6991](https://github.com/jaroop/agentcloud/pull/6991) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.52.6 (Fri Sep 15 2023)

#### 🐛 Bug Fix

- [AC-950](https://salessolutions.atlassian.net/browse/AC-950): Fix Cohort.list query join, specify columns [#6980](https://github.com/jaroop/agentcloud/pull/6980) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.52.5 (Fri Sep 15 2023)

#### 🐛 Bug Fix

- [AC-2787](https://salessolutions.atlassian.net/browse/AC-2787): Public API Queues Event Publishers/Listeners [#6978](https://github.com/jaroop/agentcloud/pull/6978) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.52.4 (Thu Sep 14 2023)

#### 🐛 Bug Fix

- [AC-3116](https://salessolutions.atlassian.net/browse/AC-3116): Fix PR journey handoff to Universal journey [#6985](https://github.com/jaroop/agentcloud/pull/6985) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.52.3 (Thu Sep 14 2023)

#### 🐛 Bug Fix

- [AC-3124](https://salessolutions.atlassian.net/browse/AC-3124): Add partner prefill for universal journey [#6984](https://github.com/jaroop/agentcloud/pull/6984) ([@COtlowski](https://github.com/COtlowski))
- [AC-3095](https://salessolutions.atlassian.net/browse/AC-3095): OpportunityTaskUpdate and OppoprtunityFileUpload must get put… [#6974](https://github.com/jaroop/agentcloud/pull/6974) ([@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- irakli ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.52.2 (Tue Sep 12 2023)

#### 🐛 Bug Fix

- Suraj/dev/ac 2826 [#6979](https://github.com/jaroop/agentcloud/pull/6979) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 2

- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.52.1 (Tue Sep 12 2023)

#### 🐛 Bug Fix

- [AC-3108](https://salessolutions.atlassian.net/browse/AC-3108): Remove unused sbt-web dependencies [#6977](https://github.com/jaroop/agentcloud/pull/6977) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.52.0 (Sat Sep 09 2023)

#### 🚀 Enhancement

- EMPTY COMMIT to push minor version [#6971](https://github.com/jaroop/agentcloud/pull/6971) ([@leaveller](https://github.com/leaveller))

#### 🐛 Bug Fix

- D2C: Fix issue with null quotedRateClasses [#6970](https://github.com/jaroop/agentcloud/pull/6970) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.51.21 (Sat Sep 09 2023)

#### 🐛 Bug Fix

- D2C: Fixes for journey quoting [#6969](https://github.com/jaroop/agentcloud/pull/6969) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.51.20 (Sat Sep 09 2023)

#### 🐛 Bug Fix

- [AC-2967](https://salessolutions.atlassian.net/browse/AC-2967): Create call campaign button [#6932](https://github.com/jaroop/agentcloud/pull/6932) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla) [@Franksant13](https://github.com/Franksant13))

#### Authors: 3

- [@Franksant13](https://github.com/Franksant13)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.51.19 (Fri Sep 08 2023)

#### 🐛 Bug Fix

- [AC-2785](https://salessolutions.atlassian.net/browse/AC-2785): Application UI Queues Event Publishers/Listeners 1 of 2 [#6968](https://github.com/jaroop/agentcloud/pull/6968) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))

#### Authors: 1

- [@srkothapalli-aq](https://github.com/srkothapalli-aq)

---

# v5.51.18 (Fri Sep 08 2023)

#### 🐛 Bug Fix

- [AC-3016](https://salessolutions.atlassian.net/browse/AC-3016): Save height from journeys [#6967](https://github.com/jaroop/agentcloud/pull/6967) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.51.17 (Thu Sep 07 2023)

#### 🐛 Bug Fix

- [AC-2785](https://salessolutions.atlassian.net/browse/AC-2785): Applications UI queues Event Publishers/Listeners 1 of 2 [#6953](https://github.com/jaroop/agentcloud/pull/6953) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))
- [AC-2784](https://salessolutions.atlassian.net/browse/AC-2784): Contacts UI Queue Event Publishers/Listeners [#6961](https://github.com/jaroop/agentcloud/pull/6961) ([@umeshaq](https://github.com/umeshaq))
- [AC-2987](https://salessolutions.atlassian.net/browse/AC-2987): Log errors received from AWS SDK in journey service [#6964](https://github.com/jaroop/agentcloud/pull/6964) ([@leaveller](https://github.com/leaveller))
- [AC-2950](https://salessolutions.atlassian.net/browse/AC-2950): Adjust linter warnings [#6963](https://github.com/jaroop/agentcloud/pull/6963) ([@leaveller](https://github.com/leaveller))

#### Authors: 3

- [@leaveller](https://github.com/leaveller)
- [@srkothapalli-aq](https://github.com/srkothapalli-aq)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.51.16 (Thu Sep 07 2023)

#### 🐛 Bug Fix

- [AC-2987](https://salessolutions.atlassian.net/browse/AC-2987): Further improvements [#6962](https://github.com/jaroop/agentcloud/pull/6962) ([@leaveller](https://github.com/leaveller))

#### 🏠 Internal

- Update Earthfile [#6960](https://github.com/jaroop/agentcloud/pull/6960) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.51.15 (Wed Sep 06 2023)

#### 🐛 Bug Fix

- [AC-2782](https://salessolutions.atlassian.net/browse/AC-2782): Opportunity UI Queue Event Publishers/Listeners [#6959](https://github.com/jaroop/agentcloud/pull/6959) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-2784](https://salessolutions.atlassian.net/browse/AC-2784): Contacts UI Queue Event Publishers/Listeners [#6956](https://github.com/jaroop/agentcloud/pull/6956) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.51.14 (Wed Sep 06 2023)

#### 🐛 Bug Fix

- [AC-2783](https://salessolutions.atlassian.net/browse/AC-2783): Quotes UI Queue Event Publishers/Listeners in new SQS queue [#6958](https://github.com/jaroop/agentcloud/pull/6958) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.51.13 (Wed Sep 06 2023)

#### 🐛 Bug Fix

- [AC-2987](https://salessolutions.atlassian.net/browse/AC-2987): Application UI Queues Event Publishers/Listeners 2 of 2 [#6954](https://github.com/jaroop/agentcloud/pull/6954) ([@jaroopshared](https://github.com/jaroopshared) [@swoosh1337](https://github.com/swoosh1337))

#### Authors: 2

- [@jaroopshared](https://github.com/jaroopshared)
- irakli ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.51.12 (Wed Sep 06 2023)

#### 🐛 Bug Fix

- [AC-2936](https://salessolutions.atlassian.net/browse/AC-2936): Cleanup issues with ownerId for node [#6957](https://github.com/jaroop/agentcloud/pull/6957) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 1

- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.51.11 (Tue Sep 05 2023)

#### 🐛 Bug Fix

- [AC-2782](https://salessolutions.atlassian.net/browse/AC-2782): Opportunity UI Queue Event Publishers/Listeners [#6950](https://github.com/jaroop/agentcloud/pull/6950) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 2

- [@balanacqt](https://github.com/balanacqt)
- balanacqt (<EMAIL>)

---

# v5.51.10 (Tue Sep 05 2023)

#### 🐛 Bug Fix

- [AC-2936](https://salessolutions.atlassian.net/browse/AC-2936): Retrigger Licensing Check with PR Journey [#6942](https://github.com/jaroop/agentcloud/pull/6942) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- D2C journey work [#6947](https://github.com/jaroop/agentcloud/pull/6947) ([@leaveller](https://github.com/leaveller))

#### Authors: 2

- [@leaveller](https://github.com/leaveller)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.51.9 (Tue Sep 05 2023)

#### 🐛 Bug Fix

- [AC-3094](https://salessolutions.atlassian.net/browse/AC-3094): Update campaigns for last AAA lead upload. [#6955](https://github.com/jaroop/agentcloud/pull/6955) ([@Franksant13](https://github.com/Franksant13))

#### Authors: 1

- [@Franksant13](https://github.com/Franksant13)

---

# v5.51.8 (Fri Sep 01 2023)

#### 🐛 Bug Fix

- [AC-2783](https://salessolutions.atlassian.net/browse/AC-2783): Quotes UI Queue Event Publishers/Listeners in new SQS queue [#6945](https://github.com/jaroop/agentcloud/pull/6945) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.51.7 (Thu Aug 31 2023)

#### 🐛 Bug Fix

- [AC-3008](https://salessolutions.atlassian.net/browse/AC-3008): New opp fields for the universal journey [#6944](https://github.com/jaroop/agentcloud/pull/6944) ([@leaveller](https://github.com/leaveller))

#### Authors: 1

- [@leaveller](https://github.com/leaveller)

---

# v5.51.6 (Thu Aug 31 2023)

#### 🐛 Bug Fix

- dev/ac 2784 [#6941](https://github.com/jaroop/agentcloud/pull/6941) ([@umeshaq](https://github.com/umeshaq))

#### Authors: 1

- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.51.5 (Wed Aug 30 2023)

#### 🐛 Bug Fix

- [AC-3058](https://salessolutions.atlassian.net/browse/AC-3058): Updates to Five9 Interaction_Events [#6943](https://github.com/jaroop/agentcloud/pull/6943) ([@Franksant13](https://github.com/Franksant13))

#### Authors: 1

- [@Franksant13](https://github.com/Franksant13)

---

# v5.51.4 (Wed Aug 30 2023)

#### 🐛 Bug Fix

- [AC-3068](https://salessolutions.atlassian.net/browse/AC-3068): Reactivate Non-DMATs Leads and Opps [#6940](https://github.com/jaroop/agentcloud/pull/6940) ([@Franksant13](https://github.com/Franksant13))

#### Authors: 1

- [@Franksant13](https://github.com/Franksant13)

---

# v5.51.3 (Tue Aug 29 2023)

#### 🐛 Bug Fix

- Add iDev and QA2 to content security policy in nginx.conf [#6918](https://github.com/jaroop/agentcloud/pull/6918) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.51.2 (Tue Aug 29 2023)

#### 🐛 Bug Fix

- [AC-2950](https://salessolutions.atlassian.net/browse/AC-2950): Upgrade React-Scripts to v4 [#6935](https://github.com/jaroop/agentcloud/pull/6935) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.46.27 (Tue Aug 29 2023)

#### 🐛 Bug Fix

- Transfer auth lambda [#6938](https://github.com/jaroop/agentcloud/pull/6938) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🤖 CI

- bump agentcloud minor version to 5.51.x [#6939](https://github.com/jaroop/agentcloud/pull/6939) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.46.26 (Mon Aug 28 2023)

#### 🐛 Bug Fix

- [AC-2776](https://salessolutions.atlassian.net/browse/AC-2776): Remove Backbone contact management page and reassign modal [#6934](https://github.com/jaroop/agentcloud/pull/6934) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2899](https://salessolutions.atlassian.net/browse/AC-2899): "View Task" Modal needs to have two Buttons when the Task is not assigned to an Agent [#6933](https://github.com/jaroop/agentcloud/pull/6933) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2826](https://salessolutions.atlassian.net/browse/AC-2826): Create Lead Campaign button [#6931](https://github.com/jaroop/agentcloud/pull/6931) ([@sowmyavarakala](https://github.com/sowmyavarakala))

#### Authors: 2

- [@sowmyavarakala](https://github.com/sowmyavarakala)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.46.25 (Thu Aug 24 2023)

#### 🐛 Bug Fix

- [AC-3009](https://salessolutions.atlassian.net/browse/AC-3009): Change product type from journey listener [#6928](https://github.com/jaroop/agentcloud/pull/6928) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.46.24 (Wed Aug 23 2023)

#### 🐛 Bug Fix

- [AC-2948](https://salessolutions.atlassian.net/browse/AC-2948): Upgrade React to v17 [#6926](https://github.com/jaroop/agentcloud/pull/6926) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.46.23 (Tue Aug 22 2023)

#### 🐛 Bug Fix

- [AC-2791](https://salessolutions.atlassian.net/browse/AC-2791): SPIKE - Five9 Interaction Events are providing a value of 'null' [#6920](https://github.com/jaroop/agentcloud/pull/6920) ([@swoosh1337](https://github.com/swoosh1337))
- [AC-3014](https://salessolutions.atlassian.net/browse/AC-3014): Update Code to Handle Functionality [#6905](https://github.com/jaroop/agentcloud/pull/6905) ([@AndreiMazu](https://github.com/AndreiMazu) [@valentyn-protsenko](https://github.com/valentyn-protsenko))

#### 🏠 Internal

- Add missing var PUSH_LATEST to lambda targets [#6916](https://github.com/jaroop/agentcloud/pull/6916) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 4

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- irakli ([@swoosh1337](https://github.com/swoosh1337))

---

# v5.46.22 (Mon Aug 21 2023)

#### 🐛 Bug Fix

- Update configs for QA2 environment [#6917](https://github.com/jaroop/agentcloud/pull/6917) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.46.21 (Mon Aug 21 2023)

#### 🐛 Bug Fix

- [AC-2902](https://salessolutions.atlassian.net/browse/AC-2902): Fail AAA emails if an Agent does not have a AAA email address [#6911](https://github.com/jaroop/agentcloud/pull/6911) ([@leaveller](https://github.com/leaveller) <EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-3009](https://salessolutions.atlassian.net/browse/AC-3009): Change product type from journey listener [#6904](https://github.com/jaroop/agentcloud/pull/6904) ([@leaveller](https://github.com/leaveller))
- [AC-2978](https://salessolutions.atlassian.net/browse/AC-2978): Added logger when updating answers [#6908](https://github.com/jaroop/agentcloud/pull/6908) ([@Franksant13](https://github.com/Franksant13))
- [AC-2824](https://salessolutions.atlassian.net/browse/AC-2824): display partner campaigns fix null handling error [#6906](https://github.com/jaroop/agentcloud/pull/6906) ([@jacklo225](https://github.com/jacklo225))
- Pipeline Updates [08-15](https://salessolutions.atlassian.net/browse/08-15): 23 [#6900](https://github.com/jaroop/agentcloud/pull/6900) ([@nnovaeshc](https://github.com/nnovaeshc))
- Pipeline updates - agentcloud-web [#6902](https://github.com/jaroop/agentcloud/pull/6902) ([@nnovaeshc](https://github.com/nnovaeshc))
- fix python test for lambda_function.py [#6903](https://github.com/jaroop/agentcloud/pull/6903) ([@nnovaeshc](https://github.com/nnovaeshc))
- Update obr helm deployment.yaml [#6841](https://github.com/jaroop/agentcloud/pull/6841) ([@nnovaeshc](https://github.com/nnovaeshc))
- Pipeline / Generic method for batch data lambda [#6868](https://github.com/jaroop/agentcloud/pull/6868) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2979](https://salessolutions.atlassian.net/browse/AC-2979): Journey Editor: Number Widget [#6892](https://github.com/jaroop/agentcloud/pull/6892) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-2824](https://salessolutions.atlassian.net/browse/AC-2824): another UI change [#6897](https://github.com/jaroop/agentcloud/pull/6897) ([@jacklo225](https://github.com/jacklo225))
- [AC-3017](https://salessolutions.atlassian.net/browse/AC-3017): QA fixes to script [#6896](https://github.com/jaroop/agentcloud/pull/6896) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-3021](https://salessolutions.atlassian.net/browse/AC-3021): Fix campaign ID on newly created leads [#6894](https://github.com/jaroop/agentcloud/pull/6894) ([@leaveller](https://github.com/leaveller))
- [AC-3022](https://salessolutions.atlassian.net/browse/AC-3022): Assign Lead Campaign to Policy Review Primary Rule in Lead Routing [#6893](https://github.com/jaroop/agentcloud/pull/6893) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2824](https://salessolutions.atlassian.net/browse/AC-2824): partner campaign change UI fixes [#6886](https://github.com/jaroop/agentcloud/pull/6886) ([@jacklo225](https://github.com/jacklo225))
- [AC-2939](https://salessolutions.atlassian.net/browse/AC-2939): Fix issues with PR Upload blocking thread [#6887](https://github.com/jaroop/agentcloud/pull/6887) ([@COtlowski](https://github.com/COtlowski))

#### Authors: 9

- [@balanacqt](https://github.com/balanacqt)
- [@Franksant13](https://github.com/Franksant13)
- [@jacklo225](https://github.com/jacklo225)
- [@leaveller](https://github.com/leaveller)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.46.20 (Fri Aug 11 2023)

#### 🐛 Bug Fix

- Add conf/env files for qa2 environment [#6884](https://github.com/jaroop/agentcloud/pull/6884) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-3017](https://salessolutions.atlassian.net/browse/AC-3017): Upload the Priority to new non-AAA Leads [#6883](https://github.com/jaroop/agentcloud/pull/6883) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2824](https://salessolutions.atlassian.net/browse/AC-2824): button changes [#6881](https://github.com/jaroop/agentcloud/pull/6881) ([@jacklo225](https://github.com/jacklo225))

#### Authors: 3

- [@jacklo225](https://github.com/jacklo225)
- [@nnovaeshc](https://github.com/nnovaeshc)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.46.19 (Fri Aug 11 2023)

#### 🐛 Bug Fix

- [AC-2958](https://salessolutions.atlassian.net/browse/AC-2958): Create Data Needed for PR upload testing [#6857](https://github.com/jaroop/agentcloud/pull/6857) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))
- [AC-2942](https://salessolutions.atlassian.net/browse/AC-2942): QA fixes [#6878](https://github.com/jaroop/agentcloud/pull/6878) ([@AndreiMazu](https://github.com/AndreiMazu) [@Franksant13](https://github.com/Franksant13))
- [AC-2821](https://salessolutions.atlassian.net/browse/AC-2821): Small fixes [#6877](https://github.com/jaroop/agentcloud/pull/6877) ([@sowmyavarakala](https://github.com/sowmyavarakala))

#### Authors: 5

- [@Franksant13](https://github.com/Franksant13)
- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@sunilcs1](https://github.com/sunilcs1)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- Sunil Kumar C S (<EMAIL>)

---

# v5.46.18 (Thu Aug 10 2023)

#### 🐛 Bug Fix

- [AC-2824](https://salessolutions.atlassian.net/browse/AC-2824): display partner campaigns [#6858](https://github.com/jaroop/agentcloud/pull/6858) ([@jacklo225](https://github.com/jacklo225))
- [AC-2821](https://salessolutions.atlassian.net/browse/AC-2821): Small fix [#6875](https://github.com/jaroop/agentcloud/pull/6875) ([@sowmyavarakala](https://github.com/sowmyavarakala))
- [AC-2942](https://salessolutions.atlassian.net/browse/AC-2942): SQL changes for aaa.main_data table [#6869](https://github.com/jaroop/agentcloud/pull/6869) ([@AndreiMazu](https://github.com/AndreiMazu) [@Franksant13](https://github.com/Franksant13))
- [AC-2945](https://salessolutions.atlassian.net/browse/AC-2945): Migrate Offer Info to Eap.Leads [#6866](https://github.com/jaroop/agentcloud/pull/6866) (<EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- [AC-2821](https://salessolutions.atlassian.net/browse/AC-2821): Styling and small fixes [#6871](https://github.com/jaroop/agentcloud/pull/6871) ([@sowmyavarakala](https://github.com/sowmyavarakala) [@Franksant13](https://github.com/Franksant13))
- Fix lambda integration tests [#6872](https://github.com/jaroop/agentcloud/pull/6872) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2939](https://salessolutions.atlassian.net/browse/AC-2939): Fail Policy Review Tool Gracefully and Allow for Larger Uploads [#6862](https://github.com/jaroop/agentcloud/pull/6862) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 9

- [@balanacqt](https://github.com/balanacqt)
- [@Franksant13](https://github.com/Franksant13)
- [@jacklo225](https://github.com/jacklo225)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- balanacqt (<EMAIL>)
- Valentyn Protsenko (<EMAIL>)

---

# v5.46.17 (Wed Aug 09 2023)

#### 🐛 Bug Fix

- [AC-2946](https://salessolutions.atlassian.net/browse/AC-2946): Add offer_code to Policy Review collation Pre-fills [#6845](https://github.com/jaroop/agentcloud/pull/6845) ([@Franksant13](https://github.com/Franksant13))
- [AC-2942](https://salessolutions.atlassian.net/browse/AC-2942): Updates to the AAA Ingestion APIs [#6861](https://github.com/jaroop/agentcloud/pull/6861) ([@swoosh1337](https://github.com/swoosh1337) [@Franksant13](https://github.com/Franksant13))
- [AC-2943](https://salessolutions.atlassian.net/browse/AC-2943): Post QA cleanup [#6860](https://github.com/jaroop/agentcloud/pull/6860) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2821](https://salessolutions.atlassian.net/browse/AC-2821): Add button to create partners [#6856](https://github.com/jaroop/agentcloud/pull/6856) ([@sowmyavarakala](https://github.com/sowmyavarakala))
- [AC-2944](https://salessolutions.atlassian.net/browse/AC-2944): Update AAA tab to pull fields from eap.leads [#6852](https://github.com/jaroop/agentcloud/pull/6852) ([@umeshaq](https://github.com/umeshaq))
- [AC-1697](https://salessolutions.atlassian.net/browse/AC-1697) [#6820](https://github.com/jaroop/agentcloud/pull/6820) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))
- [AC-2972](https://salessolutions.atlassian.net/browse/AC-2972): Create Campaigns for AAA Retrys [#6846](https://github.com/jaroop/agentcloud/pull/6846) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 7

- [@Franksant13](https://github.com/Franksant13)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@srkothapalli-aq](https://github.com/srkothapalli-aq)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- irakli ([@swoosh1337](https://github.com/swoosh1337))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.46.16 (Tue Aug 01 2023)

#### 🐛 Bug Fix

- [AC-2943](https://salessolutions.atlassian.net/browse/AC-2943): Update Five9 API to Send customer_priority from leads table [#6838](https://github.com/jaroop/agentcloud/pull/6838) ([@AndreiMazu](https://github.com/AndreiMazu))

#### Authors: 1

- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.46.15 (Tue Aug 01 2023)

#### 🐛 Bug Fix

- [AC-2971](https://salessolutions.atlassian.net/browse/AC-2971): Add campaign name to PR journey header [#6843](https://github.com/jaroop/agentcloud/pull/6843) ([@COtlowski](https://github.com/COtlowski))
- [AC-2945](https://salessolutions.atlassian.net/browse/AC-2945): Migrate Offer Info to Eap.Leads [#6833](https://github.com/jaroop/agentcloud/pull/6833) ([@valentyn-protsenko](https://github.com/valentyn-protsenko))
- fix helm chart error [#6836](https://github.com/jaroop/agentcloud/pull/6836) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🏠 Internal

- restrict len of CACHE_VERSION [#6839](https://github.com/jaroop/agentcloud/pull/6839) ([@nnovaeshc](https://github.com/nnovaeshc))
- fix issue with invalid characters on agentcloud-web tests names [#6835](https://github.com/jaroop/agentcloud/pull/6835) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 3

- [@nnovaeshc](https://github.com/nnovaeshc)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.46.14 (Mon Jul 31 2023)

#### 🐛 Bug Fix

- Helm OCI images [#6832](https://github.com/jaroop/agentcloud/pull/6832) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.46.13 (Mon Jul 31 2023)

#### 🐛 Bug Fix

- agentcloud lambdas [#6825](https://github.com/jaroop/agentcloud/pull/6825) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2944](https://salessolutions.atlassian.net/browse/AC-2944): New feature flag [#6831](https://github.com/jaroop/agentcloud/pull/6831) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2820](https://salessolutions.atlassian.net/browse/AC-2820): Styling and permission changes and small fixes [#6821](https://github.com/jaroop/agentcloud/pull/6821) ([@jacklo225](https://github.com/jacklo225))
- [AC-1695](https://salessolutions.atlassian.net/browse/AC-1695): Developer Testing adjustments [#6827](https://github.com/jaroop/agentcloud/pull/6827) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 4

- [@jacklo225](https://github.com/jacklo225)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))

---

# v5.46.12 (Fri Jul 28 2023)

#### 🐛 Bug Fix

- [AC-1695](https://salessolutions.atlassian.net/browse/AC-1695): Migrate Data in Eap.Policy Details to New Tables [#6824](https://github.com/jaroop/agentcloud/pull/6824) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 1

- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)

---

# v5.46.11 (Fri Jul 28 2023)

#### 🐛 Bug Fix

- Dev/sowmya/ac1698 [#6822](https://github.com/jaroop/agentcloud/pull/6822) ([@sowmyavarakala](https://github.com/sowmyavarakala))
- [AC-1697](https://salessolutions.atlassian.net/browse/AC-1697) [#6816](https://github.com/jaroop/agentcloud/pull/6816) ([@srkothapalli-aq](https://github.com/srkothapalli-aq))

#### Authors: 2

- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@srkothapalli-aq](https://github.com/srkothapalli-aq)

---

# v5.46.10 (Thu Jul 27 2023)

#### 🐛 Bug Fix

- [AC-2781](https://salessolutions.atlassian.net/browse/AC-2781): Adding Lead events to Lead queue. [#6813](https://github.com/jaroop/agentcloud/pull/6813) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.46.9 (Thu Jul 27 2023)

#### 🐛 Bug Fix

- [AC-1698](https://salessolutions.atlassian.net/browse/AC-1698): Success message modification [#6812](https://github.com/jaroop/agentcloud/pull/6812) ([@sowmyavarakala](https://github.com/sowmyavarakala))
- [AC-1696](https://salessolutions.atlassian.net/browse/AC-1696): Modified success message [#6811](https://github.com/jaroop/agentcloud/pull/6811) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))

#### Authors: 3

- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.46.8 (Thu Jul 27 2023)

#### 🐛 Bug Fix

- [AC-1698](https://salessolutions.atlassian.net/browse/AC-1698): Migrate Accidental Death Policy Details [#6799](https://github.com/jaroop/agentcloud/pull/6799) ([@sowmyavarakala](https://github.com/sowmyavarakala) [@Franksant13](https://github.com/Franksant13))
- [AC-2820](https://salessolutions.atlassian.net/browse/AC-2820): Make styling changes to partner management table [#6806](https://github.com/jaroop/agentcloud/pull/6806) ([@jacklo225](https://github.com/jacklo225) [@Franksant13](https://github.com/Franksant13))
- [AC-2799](https://salessolutions.atlassian.net/browse/AC-2799): Fix missing task history tracking [#6795](https://github.com/jaroop/agentcloud/pull/6795) ([@COtlowski](https://github.com/COtlowski))
- [AC-1639](https://salessolutions.atlassian.net/browse/AC-1639): fixed formatting in unit tests [#6805](https://github.com/jaroop/agentcloud/pull/6805) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-2820,AC-2777](https://salessolutions.atlassian.net/browse/AC-2820/AC-2777): and AC-2777 test linter fix [#6802](https://github.com/jaroop/agentcloud/pull/6802) ([@Franksant13](https://github.com/Franksant13))
- [AC-2820](https://salessolutions.atlassian.net/browse/AC-2820): Partner Management View CSS Linter Fix [#6798](https://github.com/jaroop/agentcloud/pull/6798) ([@Franksant13](https://github.com/Franksant13) [@jacklo225](https://github.com/jacklo225))
- [AC-1696](https://salessolutions.atlassian.net/browse/AC-1696): Migrating all rows including null valued rows from new_benef… [#6794](https://github.com/jaroop/agentcloud/pull/6794) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-1639](https://salessolutions.atlassian.net/browse/AC-1639): Birthdate changes and unit test creation [#6765](https://github.com/jaroop/agentcloud/pull/6765) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-2777](https://salessolutions.atlassian.net/browse/AC-2777): Replace Gridlock in Lead Reassign Modal [#6796](https://github.com/jaroop/agentcloud/pull/6796) ([@umeshaq](https://github.com/umeshaq) [@Franksant13](https://github.com/Franksant13))

#### 🏠 Internal

- add lint style checks [#6801](https://github.com/jaroop/agentcloud/pull/6801) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 9

- [@Franksant13](https://github.com/Franksant13)
- [@jacklo225](https://github.com/jacklo225)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Suraj Amaranarayana (<EMAIL>)
- Suraj Amaranarayana (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.46.7 (Wed Jul 26 2023)

#### 🐛 Bug Fix

- [AC-2823](https://salessolutions.atlassian.net/browse/AC-2823): Update campaign related schema [#6764](https://github.com/jaroop/agentcloud/pull/6764) (<EMAIL>)
- [AC-2820](https://salessolutions.atlassian.net/browse/AC-2820): create partner management view [#6761](https://github.com/jaroop/agentcloud/pull/6761) ([@jacklo225](https://github.com/jacklo225))
- [AC-2795](https://salessolutions.atlassian.net/browse/AC-2795): Update HTTP4S to 0.23.22 [#6793](https://github.com/jaroop/agentcloud/pull/6793) ([@Franksant13](https://github.com/Franksant13))
- [AC-1697](https://salessolutions.atlassian.net/browse/AC-1697): Children's Whole Life Details Migration [#6774](https://github.com/jaroop/agentcloud/pull/6774) (<EMAIL> [@srkothapalli-aq](https://github.com/srkothapalli-aq))
- set the base path for secrets as an environment variable [#6784](https://github.com/jaroop/agentcloud/pull/6784) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2941](https://salessolutions.atlassian.net/browse/AC-2941): Add new columns to eap.leads [#6790](https://github.com/jaroop/agentcloud/pull/6790) ([@leaveller](https://github.com/leaveller))
- [AC-1699](https://salessolutions.atlassian.net/browse/AC-1699): Migrate Reliashield Data [#6771](https://github.com/jaroop/agentcloud/pull/6771) (<EMAIL> [@swoosh1337](https://github.com/swoosh1337))

#### 🏠 Internal

- Fix missing aws image [#6792](https://github.com/jaroop/agentcloud/pull/6792) ([@nnovaeshc](https://github.com/nnovaeshc))
- Update source CI images [#6750](https://github.com/jaroop/agentcloud/pull/6750) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 9

- [@Franksant13](https://github.com/Franksant13)
- [@jacklo225](https://github.com/jacklo225)
- [@leaveller](https://github.com/leaveller)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@srkothapalli-aq](https://github.com/srkothapalli-aq)
- irakli ([@swoosh1337](https://github.com/swoosh1337))
- Irakli Grigolia (<EMAIL>)
- Nikhil Konijeti ([@kkonijeti](https://github.com/kkonijeti))
- sridhar.kothapalli (<EMAIL>)

---

# v5.46.6 (Mon Jul 24 2023)

#### 🐛 Bug Fix

- [AC-2799](https://salessolutions.atlassian.net/browse/AC-2799): Add task history entries for updates. [#6768](https://github.com/jaroop/agentcloud/pull/6768) ([@COtlowski](https://github.com/COtlowski))

#### 🏠 Internal

- Fix pipeline [#6781](https://github.com/jaroop/agentcloud/pull/6781) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 2

- [@nnovaeshc](https://github.com/nnovaeshc)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))

---

# v5.46.5 (Mon Jul 24 2023)

#### 🐛 Bug Fix

- [AC-2781](https://salessolutions.atlassian.net/browse/AC-2781): Attaching LeadUpdate,LeadInteractionEvent,LeadAgentReassignm… [#6758](https://github.com/jaroop/agentcloud/pull/6758) (<EMAIL> [@sunilcs1](https://github.com/sunilcs1))

#### Authors: 2

- [@sunilcs1](https://github.com/sunilcs1)
- Sunil Kumar C S (<EMAIL>)

---

# v5.46.4 (Mon Jul 24 2023)

#### 🐛 Bug Fix

- [AC-2762](https://salessolutions.atlassian.net/browse/AC-2762): Policy Number Validated in Policy Details Applied Approved an… [#6763](https://github.com/jaroop/agentcloud/pull/6763) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-2901](https://salessolutions.atlassian.net/browse/AC-2901): Old Replacing Value in BOB [#6775](https://github.com/jaroop/agentcloud/pull/6775) ([@Franksant13](https://github.com/Franksant13))
- [AC-2901](https://salessolutions.atlassian.net/browse/AC-2901): Add Old Replacing Value [#6772](https://github.com/jaroop/agentcloud/pull/6772) ([@Franksant13](https://github.com/Franksant13))
- [AC-2901](https://salessolutions.atlassian.net/browse/AC-2901): Add Old Replacing Value in BOB Exception File [#6766](https://github.com/jaroop/agentcloud/pull/6766) ([@Franksant13](https://github.com/Franksant13))
- [AC-1696](https://salessolutions.atlassian.net/browse/AC-1696): New Benefits Migration [#6762](https://github.com/jaroop/agentcloud/pull/6762) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-2892](https://salessolutions.atlassian.net/browse/AC-2892): Add script used to remove plan code leads [#6756](https://github.com/jaroop/agentcloud/pull/6756) ([@COtlowski](https://github.com/COtlowski))
- [AC-1926](https://salessolutions.atlassian.net/browse/AC-1926): Revert changes [#6753](https://github.com/jaroop/agentcloud/pull/6753) ([@leaveller](https://github.com/leaveller))
- [AC-2086](https://salessolutions.atlassian.net/browse/AC-2086): Update Task Modal - QA fixes [#6751](https://github.com/jaroop/agentcloud/pull/6751) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2773](https://salessolutions.atlassian.net/browse/AC-2773): Address row level security [#6746](https://github.com/jaroop/agentcloud/pull/6746) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))

#### Authors: 9

- [@balanacqt](https://github.com/balanacqt)
- [@Franksant13](https://github.com/Franksant13)
- [@leaveller](https://github.com/leaveller)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Suraj Amaranarayana (<EMAIL>)

---

# v5.46.3 (Thu Jul 13 2023)

#### 🐛 Bug Fix

- [AC-2086](https://salessolutions.atlassian.net/browse/AC-2086): Task Handling Tracking - Update Task Modal [#6748](https://github.com/jaroop/agentcloud/pull/6748) ([@umeshaq](https://github.com/umeshaq))
- [AC-2086](https://salessolutions.atlassian.net/browse/AC-2086): Task Handling Tracking - Update Task Modal [#6743](https://github.com/jaroop/agentcloud/pull/6743) ([@umeshaq](https://github.com/umeshaq))
- [AC-2014](https://salessolutions.atlassian.net/browse/AC-2014): Journey Version Update [#6744](https://github.com/jaroop/agentcloud/pull/6744) (<EMAIL> [@SeevietaBiswas](https://github.com/SeevietaBiswas))
- [AC-2809](https://salessolutions.atlassian.net/browse/AC-2809): allow new FE journey on QA [#6737](https://github.com/jaroop/agentcloud/pull/6737) ([@valentyn-protsenko](https://github.com/valentyn-protsenko))
- [AC-2014](https://salessolutions.atlassian.net/browse/AC-2014): Update Journey Engine Version [#6736](https://github.com/jaroop/agentcloud/pull/6736) (<EMAIL> [@SeevietaBiswas](https://github.com/SeevietaBiswas))
- [AC-2078](https://salessolutions.atlassian.net/browse/AC-2078): Replace Gridlock in Contact Management page [#6740](https://github.com/jaroop/agentcloud/pull/6740) ([@Franksant13](https://github.com/Franksant13))

#### Authors: 5

- [@Franksant13](https://github.com/Franksant13)
- [@SeevietaBiswas](https://github.com/SeevietaBiswas)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- Seevieta Biswas Toa (<EMAIL>)
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))

---

# v5.46.2 (Tue Jul 11 2023)

#### 🐛 Bug Fix

- AC -2078 Replace Gridlock in Contact Management page [#6738](https://github.com/jaroop/agentcloud/pull/6738) ([@Franksant13](https://github.com/Franksant13))
- [AC-2081](https://salessolutions.atlassian.net/browse/AC-2081): replacing policy value mapping in bob upload file [#6735](https://github.com/jaroop/agentcloud/pull/6735) (<EMAIL> [@balanacqt](https://github.com/balanacqt))

#### Authors: 3

- [@balanacqt](https://github.com/balanacqt)
- [@Franksant13](https://github.com/Franksant13)
- balanacqt (<EMAIL>)

---

# v5.46.1 (Mon Jul 10 2023)

#### 🐛 Bug Fix

- [AC-2774](https://salessolutions.atlassian.net/browse/AC-2774): Add React version of journey switcher [#6729](https://github.com/jaroop/agentcloud/pull/6729) ([@COtlowski](https://github.com/COtlowski))
- [AC-2765](https://salessolutions.atlassian.net/browse/AC-2765): hides [open Journey] and [Term] buttons for PR journey [#6728](https://github.com/jaroop/agentcloud/pull/6728) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- [AC-2058](https://salessolutions.atlassian.net/browse/AC-2058): Modify one existing FE Journey field [#6712](https://github.com/jaroop/agentcloud/pull/6712) ([@jacklo225](https://github.com/jacklo225) [@sunilcs1](https://github.com/sunilcs1))
- [AC-951](https://salessolutions.atlassian.net/browse/AC-951): SQL Improvements in Queries in CarrierProductCarrierService Class [#6713](https://github.com/jaroop/agentcloud/pull/6713) (<EMAIL> [@SeevietaBiswas](https://github.com/SeevietaBiswas))
- [AC-1761](https://salessolutions.atlassian.net/browse/AC-1761): Optimize query for reporting.retreaver_information view [#6719](https://github.com/jaroop/agentcloud/pull/6719) (<EMAIL>)

#### Authors: 8

- [@jacklo225](https://github.com/jacklo225)
- [@SeevietaBiswas](https://github.com/SeevietaBiswas)
- [@sunilcs1](https://github.com/sunilcs1)
- [@SurajKomarla](https://github.com/SurajKomarla)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Nikhil Konijeti ([@kkonijeti](https://github.com/kkonijeti))
- Seevieta Biswas Toa (<EMAIL>)
- Suraj Amaranarayana (<EMAIL>)

---

# v5.46.0 (Wed Jul 05 2023)

#### 🚀 Enhancement

- Bump 5.45.x to 5.46.x [#6722](https://github.com/jaroop/agentcloud/pull/6722) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.45.2 (Wed Jul 05 2023)

#### 🐛 Bug Fix

- Update package.json [#6721](https://github.com/jaroop/agentcloud/pull/6721) ([@nnovaeshc](https://github.com/nnovaeshc))

#### 🏠 Internal

- Fix missing github parameter on auto release [#6720](https://github.com/jaroop/agentcloud/pull/6720) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: 1

- [@nnovaeshc](https://github.com/nnovaeshc)

---

# v5.43.2 (Wed Jun 28 2023)

#### 🐛 Bug Fix

- [AC-2028](https://salessolutions.atlassian.net/browse/AC-2028): Create term collation new [#6696](https://github.com/jaroop/agentcloud/pull/6696) ([@umeshaq](https://github.com/umeshaq))
- [AC-2788](https://salessolutions.atlassian.net/browse/AC-2788): String masker changes [#6693](https://github.com/jaroop/agentcloud/pull/6693) ([@leaveller](https://github.com/leaveller))
- Turn off FE modular journey in QA [#6690](https://github.com/jaroop/agentcloud/pull/6690) ([@leaveller](https://github.com/leaveller))
- [AC-2085](https://salessolutions.atlassian.net/browse/AC-2085): Utilize License Checking on Front-end Modular Journeys [#6670](https://github.com/jaroop/agentcloud/pull/6670) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-2028](https://salessolutions.atlassian.net/browse/AC-2028): Create Term Collations [#6689](https://github.com/jaroop/agentcloud/pull/6689) ([@umeshaq](https://github.com/umeshaq))
- [AC-2091](https://salessolutions.atlassian.net/browse/AC-2091): Add campaign PR Distinct Cohort 06/19/2023 [#6660](https://github.com/jaroop/agentcloud/pull/6660) ([@Franksant13](https://github.com/Franksant13))
- Dev/ac 2079 bob upload policy number ignore case [#6674](https://github.com/jaroop/agentcloud/pull/6674) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-2083](https://salessolutions.atlassian.net/browse/AC-2083): Update Replacing Policy Values [#6663](https://github.com/jaroop/agentcloud/pull/6663) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- [AC-1991](https://salessolutions.atlassian.net/browse/AC-1991): Script updates [#6681](https://github.com/jaroop/agentcloud/pull/6681) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2763](https://salessolutions.atlassian.net/browse/AC-2763): Fix memory issues with string masking [#6666](https://github.com/jaroop/agentcloud/pull/6666) ([@leaveller](https://github.com/leaveller))
- Pipeline fixes [#6679](https://github.com/jaroop/agentcloud/pull/6679) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2770](https://salessolutions.atlassian.net/browse/AC-2770): Update Campaign IDs to match Five9 Campaign ID Format [#6671](https://github.com/jaroop/agentcloud/pull/6671) ([@Franksant13](https://github.com/Franksant13))
- Fix 652 evolution [#6672](https://github.com/jaroop/agentcloud/pull/6672) ([@COtlowski](https://github.com/COtlowski))
- Restricting QA deployment to a single workflow. [#6668](https://github.com/jaroop/agentcloud/pull/6668) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- [AC-2082](https://salessolutions.atlassian.net/browse/AC-2082): Add index to Cohorts.name [#6662](https://github.com/jaroop/agentcloud/pull/6662) (<EMAIL> [@SeevietaBiswas](https://github.com/SeevietaBiswas))
- [AC-2088](https://salessolutions.atlassian.net/browse/AC-2088): Only Allow Next Button to Be Pressed After the Lead Management… [#6665](https://github.com/jaroop/agentcloud/pull/6665) ([@Franksant13](https://github.com/Franksant13))
- AC1760 [#6657](https://github.com/jaroop/agentcloud/pull/6657) (<EMAIL> [@SurajKomarla](https://github.com/SurajKomarla))
- Nnovaes/pipeline updates [#6611](https://github.com/jaroop/agentcloud/pull/6611) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2061](https://salessolutions.atlassian.net/browse/AC-2061): Create New PR Dialer List [#6654](https://github.com/jaroop/agentcloud/pull/6654) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Dev/sowmyavarakala/ac 1293 [#6641](https://github.com/jaroop/agentcloud/pull/6641) (<EMAIL> [@sowmyavarakala](https://github.com/sowmyavarakala))
- [AC-2060](https://salessolutions.atlassian.net/browse/AC-2060): edit on QA test setup [#6651](https://github.com/jaroop/agentcloud/pull/6651) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Update docker version for OBR publish in QA pipeline [#6650](https://github.com/jaroop/agentcloud/pull/6650) ([@leaveller](https://github.com/leaveller))
- [AC-2033](https://salessolutions.atlassian.net/browse/AC-2033): Add Licensing Information to Node Resolution [#6642](https://github.com/jaroop/agentcloud/pull/6642) ([@Franksant13](https://github.com/Franksant13))
- [AC-1290](https://salessolutions.atlassian.net/browse/AC-1290): SQL Improvements - Opportunity Reassign Flow [#6633](https://github.com/jaroop/agentcloud/pull/6633) ([@MANDACQT](https://github.com/MANDACQT))
- [AC-974](https://salessolutions.atlassian.net/browse/AC-974): sql improvements [#6632](https://github.com/jaroop/agentcloud/pull/6632) (<EMAIL> [@jacklo225](https://github.com/jacklo225))
- [AC-2033](https://salessolutions.atlassian.net/browse/AC-2033): Add Licensing Information to Node Resolution [#6629](https://github.com/jaroop/agentcloud/pull/6629) ([@Franksant13](https://github.com/Franksant13))
- [AC-2060](https://salessolutions.atlassian.net/browse/AC-2060): Updates to Reporting.campaign_out_to_paid_designation [#6636](https://github.com/jaroop/agentcloud/pull/6636) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Merge branch 'qa' into main [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@leaveller](https://github.com/leaveller))
- [AC-2029](https://salessolutions.atlassian.net/browse/AC-2029): Fix journey redirects [#6630](https://github.com/jaroop/agentcloud/pull/6630) ([@leaveller](https://github.com/leaveller))
- [AC-1086](https://salessolutions.atlassian.net/browse/AC-1086): move the eap.quotes_bkup table to reporting schema. Then rena… [#6628](https://github.com/jaroop/agentcloud/pull/6628) (<EMAIL> [@balanacqt](https://github.com/balanacqt))
- [AC-1791](https://salessolutions.atlassian.net/browse/AC-1791): Create lightweight LeadListData.listByStatus function [#6609](https://github.com/jaroop/agentcloud/pull/6609) (<EMAIL> [@SeevietaBiswas](https://github.com/SeevietaBiswas))
- Develop to main [#6605](https://github.com/jaroop/agentcloud/pull/6605) ([@umeshaq](https://github.com/umeshaq) [@leaveller](https://github.com/leaveller) [@AndreiMazu](https://github.com/AndreiMazu) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ) <EMAIL> <EMAIL>)
- Develop to QA [#6617](https://github.com/jaroop/agentcloud/pull/6617) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) <EMAIL> <EMAIL> [@leaveller](https://github.com/leaveller) [@COtlowski](https://github.com/COtlowski))
- only run the trigger.yaml (CircleCI Pipeline) job if the PR is targeting the main branch [#6618](https://github.com/jaroop/agentcloud/pull/6618) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-982](https://salessolutions.atlassian.net/browse/AC-982): SQL Improvements -- Queries in LumicoSound.scala file [#6604](https://github.com/jaroop/agentcloud/pull/6604) (<EMAIL> [@YaroslavBobechko1994](https://github.com/YaroslavBobechko1994))
- [AC-2029](https://salessolutions.atlassian.net/browse/AC-2029): FE journey collation setup [#6599](https://github.com/jaroop/agentcloud/pull/6599) ([@leaveller](https://github.com/leaveller))
- [AC-2056](https://salessolutions.atlassian.net/browse/AC-2056): Change Pattern Matching for Sensitive Value Masking [#6608](https://github.com/jaroop/agentcloud/pull/6608) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Dev/ac 1896 pi contact reached changes [#6596](https://github.com/jaroop/agentcloud/pull/6596) (<EMAIL> [@COtlowski](https://github.com/COtlowski))
- [AC-2047](https://salessolutions.atlassian.net/browse/AC-2047): Create USA Family Protection Campaign [#6595](https://github.com/jaroop/agentcloud/pull/6595) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- split web into three separate jobs [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- fix obr-microservice target name [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- fix pipeline build sequence [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- update options for auto sbt plugin; add auto version command to release target [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- remove git plugin from .autorc; add publishlocal to sbt publish command [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- remove skip on pull_requests; add build steps to web [#6602](https://github.com/jaroop/agentcloud/pull/6602) ([@nnovaeshc](https://github.com/nnovaeshc))
- pipeline updates [#6601](https://github.com/jaroop/agentcloud/pull/6601) ([@nnovaeshc](https://github.com/nnovaeshc))
- point build to correct version file [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- add tests into publish targets; add obr-microservice [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- QA <- Develop [#6598](https://github.com/jaroop/agentcloud/pull/6598) ([@AndreiMazu](https://github.com/AndreiMazu))
- [AC-2024](https://salessolutions.atlassian.net/browse/AC-2024): Make the process of adjusting the objectId for new Journeys automatic [#6589](https://github.com/jaroop/agentcloud/pull/6589) ([@AndreiMazu](https://github.com/AndreiMazu))
- Develop to QA [#6593](https://github.com/jaroop/agentcloud/pull/6593) ([@leaveller](https://github.com/leaveller))
- [AC-2043](https://salessolutions.atlassian.net/browse/AC-2043): Multiform widget fixes [#6592](https://github.com/jaroop/agentcloud/pull/6592) ([@leaveller](https://github.com/leaveller))
- chore: Empty commit [#6591](https://github.com/jaroop/agentcloud/pull/6591) (<EMAIL> [@nnovaeshc](https://github.com/nnovaeshc))
- chore: add git tags to .autorc [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- chore: move baseBranch on .autorc to main [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- QA <- Develop [#6590](https://github.com/jaroop/agentcloud/pull/6590) ([@umeshaq](https://github.com/umeshaq) [@COtlowski](https://github.com/COtlowski))
- [AC-2052](https://salessolutions.atlassian.net/browse/AC-2052): Move Open Journey Button to Opportunity Header [#6588](https://github.com/jaroop/agentcloud/pull/6588) ([@umeshaq](https://github.com/umeshaq))
- fix: point sbt-agentcloud-version target to the correct python script path [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- add Earthfile [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@nnovaeshc](https://github.com/nnovaeshc))
- [AC-2018](https://salessolutions.atlassian.net/browse/AC-2018): Add Button to Change Product Type on Term and Conversion Opportunities [#6631](https://github.com/jaroop/agentcloud/pull/6631) ([@Franksant13](https://github.com/Franksant13))
- QA <- Develop [#6587](https://github.com/jaroop/agentcloud/pull/6587) ([@AndreiMazu](https://github.com/AndreiMazu))
- Develop to QA [#6584](https://github.com/jaroop/agentcloud/pull/6584) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6581](https://github.com/jaroop/agentcloud/pull/6581) ([@AndreiMazu](https://github.com/AndreiMazu) <EMAIL>)
- Develop to QA [#6580](https://github.com/jaroop/agentcloud/pull/6580) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- Develop [#6575](https://github.com/jaroop/agentcloud/pull/6575) ([@Franksant13](https://github.com/Franksant13) [@COtlowski](https://github.com/COtlowski))
- Develop to QA [#6574](https://github.com/jaroop/agentcloud/pull/6574) ([@MANDACQT](https://github.com/MANDACQT) [@umeshaq](https://github.com/umeshaq) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6568](https://github.com/jaroop/agentcloud/pull/6568) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- Develop [#6562](https://github.com/jaroop/agentcloud/pull/6562) ([@Franksant13](https://github.com/Franksant13))
- QA <- Develop [#6559](https://github.com/jaroop/agentcloud/pull/6559) ([@valentyn-protsenko](https://github.com/valentyn-protsenko))
- QA <- Develop [#6555](https://github.com/jaroop/agentcloud/pull/6555) ([@AndreiMazu](https://github.com/AndreiMazu) <EMAIL> [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6554](https://github.com/jaroop/agentcloud/pull/6554) ([@umeshaq](https://github.com/umeshaq) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6551](https://github.com/jaroop/agentcloud/pull/6551) ([@Franksant13](https://github.com/Franksant13) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6547](https://github.com/jaroop/agentcloud/pull/6547) ([@leaveller](https://github.com/leaveller))
- Develop to QA [#6544](https://github.com/jaroop/agentcloud/pull/6544) (<EMAIL> [@leaveller](https://github.com/leaveller))
- Develop to QA [#6537](https://github.com/jaroop/agentcloud/pull/6537) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6534](https://github.com/jaroop/agentcloud/pull/6534) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6531](https://github.com/jaroop/agentcloud/pull/6531) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- Develop to QA [#6528](https://github.com/jaroop/agentcloud/pull/6528) ([@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller))
- Develop -> QA [#6526](https://github.com/jaroop/agentcloud/pull/6526) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Develop to QA [#6525](https://github.com/jaroop/agentcloud/pull/6525) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6523](https://github.com/jaroop/agentcloud/pull/6523) ([@COtlowski](https://github.com/COtlowski))
- Develop [#6520](https://github.com/jaroop/agentcloud/pull/6520) ([@Franksant13](https://github.com/Franksant13) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6517](https://github.com/jaroop/agentcloud/pull/6517) ([@AndreiMazu](https://github.com/AndreiMazu))
- Develop to QA [#6515](https://github.com/jaroop/agentcloud/pull/6515) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6512](https://github.com/jaroop/agentcloud/pull/6512) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6511](https://github.com/jaroop/agentcloud/pull/6511) ([@nnovaeshc](https://github.com/nnovaeshc) [@leaveller](https://github.com/leaveller) <EMAIL> [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6496](https://github.com/jaroop/agentcloud/pull/6496) ([@nnovaeshc](https://github.com/nnovaeshc) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6491](https://github.com/jaroop/agentcloud/pull/6491) ([@nnovaeshc](https://github.com/nnovaeshc) [@COtlowski](https://github.com/COtlowski) [@Franksant13](https://github.com/Franksant13))
- Develop to QA [#6483](https://github.com/jaroop/agentcloud/pull/6483) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6482](https://github.com/jaroop/agentcloud/pull/6482) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@Ed-HealthcareDotcom](https://github.com/Ed-HealthcareDotcom) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6474](https://github.com/jaroop/agentcloud/pull/6474) ([@COtlowski](https://github.com/COtlowski))
- Develop [#6473](https://github.com/jaroop/agentcloud/pull/6473) ([@Franksant13](https://github.com/Franksant13) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Develop to QA [#6463](https://github.com/jaroop/agentcloud/pull/6463) ([@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Develop [#6454](https://github.com/jaroop/agentcloud/pull/6454) ([@Franksant13](https://github.com/Franksant13) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6452](https://github.com/jaroop/agentcloud/pull/6452) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6451](https://github.com/jaroop/agentcloud/pull/6451) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6441](https://github.com/jaroop/agentcloud/pull/6441) ([@Franksant13](https://github.com/Franksant13) [@COtlowski](https://github.com/COtlowski) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6436](https://github.com/jaroop/agentcloud/pull/6436) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@Ed-HealthcareDotcom](https://github.com/Ed-HealthcareDotcom) [@AndreiMazu](https://github.com/AndreiMazu))
- Develop to QA [#6428](https://github.com/jaroop/agentcloud/pull/6428) ([@leaveller](https://github.com/leaveller))
- Develop to QA [#6424](https://github.com/jaroop/agentcloud/pull/6424) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6422](https://github.com/jaroop/agentcloud/pull/6422) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6418](https://github.com/jaroop/agentcloud/pull/6418) ([@COtlowski](https://github.com/COtlowski) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6417](https://github.com/jaroop/agentcloud/pull/6417) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6412](https://github.com/jaroop/agentcloud/pull/6412) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- Develop [#6405](https://github.com/jaroop/agentcloud/pull/6405) ([@AndreiMazu](https://github.com/AndreiMazu) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6399](https://github.com/jaroop/agentcloud/pull/6399) ([@Franksant13](https://github.com/Franksant13) <EMAIL> [@COtlowski](https://github.com/COtlowski) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller))
- Develop [#6389](https://github.com/jaroop/agentcloud/pull/6389) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@Franksant13](https://github.com/Franksant13))
- Develop to QA [#6383](https://github.com/jaroop/agentcloud/pull/6383) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6380](https://github.com/jaroop/agentcloud/pull/6380) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6378](https://github.com/jaroop/agentcloud/pull/6378) ([@valentyn-protsenko](https://github.com/valentyn-protsenko))
- QA <- Develop [#6371](https://github.com/jaroop/agentcloud/pull/6371) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6368](https://github.com/jaroop/agentcloud/pull/6368) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6364](https://github.com/jaroop/agentcloud/pull/6364) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6361](https://github.com/jaroop/agentcloud/pull/6361) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- Develop to QA [#6360](https://github.com/jaroop/agentcloud/pull/6360) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6357](https://github.com/jaroop/agentcloud/pull/6357) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6351](https://github.com/jaroop/agentcloud/pull/6351) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- Develop -> QA [#6349](https://github.com/jaroop/agentcloud/pull/6349) ([@AndreiMazu](https://github.com/AndreiMazu) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6340](https://github.com/jaroop/agentcloud/pull/6340) ([@COtlowski](https://github.com/COtlowski))
- Develop -> QA [#6339](https://github.com/jaroop/agentcloud/pull/6339) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Develop -> QA [#6326](https://github.com/jaroop/agentcloud/pull/6326) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- Develop to QA [#6322](https://github.com/jaroop/agentcloud/pull/6322) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6320](https://github.com/jaroop/agentcloud/pull/6320) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6318](https://github.com/jaroop/agentcloud/pull/6318) ([@leaveller](https://github.com/leaveller))
- Develop to QA [#6316](https://github.com/jaroop/agentcloud/pull/6316) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller) [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- Develop -> QA [#6313](https://github.com/jaroop/agentcloud/pull/6313) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- QA <- Develop [#6307](https://github.com/jaroop/agentcloud/pull/6307) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ))
- QA <-Develop [#6302](https://github.com/jaroop/agentcloud/pull/6302) (<EMAIL> [@leaveller](https://github.com/leaveller) [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- QA <- Develop [#6298](https://github.com/jaroop/agentcloud/pull/6298) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6292](https://github.com/jaroop/agentcloud/pull/6292) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6288](https://github.com/jaroop/agentcloud/pull/6288) ([@Franksant13](https://github.com/Franksant13) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6286](https://github.com/jaroop/agentcloud/pull/6286) ([@Franksant13](https://github.com/Franksant13) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6283](https://github.com/jaroop/agentcloud/pull/6283) ([@COtlowski](https://github.com/COtlowski))
- Develop to QA [#6281](https://github.com/jaroop/agentcloud/pull/6281) ([@leaveller](https://github.com/leaveller))
- Develop to QA [#6279](https://github.com/jaroop/agentcloud/pull/6279) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6277](https://github.com/jaroop/agentcloud/pull/6277) ([@COtlowski](https://github.com/COtlowski))
- Develop to QA [#6275](https://github.com/jaroop/agentcloud/pull/6275) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6273](https://github.com/jaroop/agentcloud/pull/6273) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6270](https://github.com/jaroop/agentcloud/pull/6270) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6267](https://github.com/jaroop/agentcloud/pull/6267) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6265](https://github.com/jaroop/agentcloud/pull/6265) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6263](https://github.com/jaroop/agentcloud/pull/6263) ([@leaveller](https://github.com/leaveller))
- Develop to QA [#6261](https://github.com/jaroop/agentcloud/pull/6261) ([@leaveller](https://github.com/leaveller) [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- QA <- Develop [#6258](https://github.com/jaroop/agentcloud/pull/6258) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6255](https://github.com/jaroop/agentcloud/pull/6255) ([@COtlowski](https://github.com/COtlowski) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop -> QA [#6254](https://github.com/jaroop/agentcloud/pull/6254) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@Franksant13](https://github.com/Franksant13) [@COtlowski](https://github.com/COtlowski))
- Develop ->QA [#6244](https://github.com/jaroop/agentcloud/pull/6244) ([@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6242](https://github.com/jaroop/agentcloud/pull/6242) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6237](https://github.com/jaroop/agentcloud/pull/6237) ([@IanCZane](https://github.com/IanCZane))
- Develop to QA [#6234](https://github.com/jaroop/agentcloud/pull/6234) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6232](https://github.com/jaroop/agentcloud/pull/6232) ([@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller))
- Dev -> QA [#6231](https://github.com/jaroop/agentcloud/pull/6231) ([@nnovaeshc](https://github.com/nnovaeshc) [@leaveller](https://github.com/leaveller) [@PoisonTrainJJ](https://github.com/PoisonTrainJJ) [@IanCZane](https://github.com/IanCZane))
- QA <- Develop [#6229](https://github.com/jaroop/agentcloud/pull/6229) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6225](https://github.com/jaroop/agentcloud/pull/6225) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6218](https://github.com/jaroop/agentcloud/pull/6218) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@leaveller](https://github.com/leaveller))
- Develop [#6216](https://github.com/jaroop/agentcloud/pull/6216) ([@Franksant13](https://github.com/Franksant13))
- QA <- Develop [#6212](https://github.com/jaroop/agentcloud/pull/6212) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6196](https://github.com/jaroop/agentcloud/pull/6196) ([@IanCZane](https://github.com/IanCZane))
- Merge branch 'uat' into qa [#6220](https://github.com/jaroop/agentcloud/pull/6220) ([@leaveller](https://github.com/leaveller))
- QA <- Develop [#6194](https://github.com/jaroop/agentcloud/pull/6194) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6192](https://github.com/jaroop/agentcloud/pull/6192) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6191](https://github.com/jaroop/agentcloud/pull/6191) ([@COtlowski](https://github.com/COtlowski))
- Cherry pick to UAT [#6183](https://github.com/jaroop/agentcloud/pull/6183) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- Develop to QA [#6182](https://github.com/jaroop/agentcloud/pull/6182) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6181](https://github.com/jaroop/agentcloud/pull/6181) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- UAT <- QA [#6175](https://github.com/jaroop/agentcloud/pull/6175) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6173](https://github.com/jaroop/agentcloud/pull/6173) ([@COtlowski](https://github.com/COtlowski))
- Develop to QA [#6171](https://github.com/jaroop/agentcloud/pull/6171) ([@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6169](https://github.com/jaroop/agentcloud/pull/6169) ([@COtlowski](https://github.com/COtlowski) [@leaveller](https://github.com/leaveller))
- QA <- Develop [#6166](https://github.com/jaroop/agentcloud/pull/6166) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6164](https://github.com/jaroop/agentcloud/pull/6164) ([@AndreiMazu](https://github.com/AndreiMazu) [@leaveller](https://github.com/leaveller))
- UAT <- QA Cherry-Pick [#6158](https://github.com/jaroop/agentcloud/pull/6158) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6157](https://github.com/jaroop/agentcloud/pull/6157) ([@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6155](https://github.com/jaroop/agentcloud/pull/6155) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance))
- Develop to QA [#6154](https://github.com/jaroop/agentcloud/pull/6154) ([@AndreiMazu](https://github.com/AndreiMazu) [@Cody-Alleman](https://github.com/Cody-Alleman))
- QA <- Develop [#6151](https://github.com/jaroop/agentcloud/pull/6151) ([@COtlowski](https://github.com/COtlowski))
- UAT <- QA (With Merge Conflict Fix) [#6143](https://github.com/jaroop/agentcloud/pull/6143) ([@COtlowski](https://github.com/COtlowski) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance) [@AndreiMazu](https://github.com/AndreiMazu) [@Cody-Alleman](https://github.com/Cody-Alleman) <EMAIL>)
- QA <- Develop [#6141](https://github.com/jaroop/agentcloud/pull/6141) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6140](https://github.com/jaroop/agentcloud/pull/6140) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6136](https://github.com/jaroop/agentcloud/pull/6136) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6132](https://github.com/jaroop/agentcloud/pull/6132) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6131](https://github.com/jaroop/agentcloud/pull/6131) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6130](https://github.com/jaroop/agentcloud/pull/6130) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6129](https://github.com/jaroop/agentcloud/pull/6129) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6125](https://github.com/jaroop/agentcloud/pull/6125) ([@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6120](https://github.com/jaroop/agentcloud/pull/6120) ([@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance))
- [AC-1777](https://salessolutions.atlassian.net/browse/AC-1777): to UAT [#6117](https://github.com/jaroop/agentcloud/pull/6117) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- UAT <- Change the FrequentDialerListTaskRunner interval (cherry pick) [#6112](https://github.com/jaroop/agentcloud/pull/6112) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6111](https://github.com/jaroop/agentcloud/pull/6111) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6109](https://github.com/jaroop/agentcloud/pull/6109) ([@COtlowski](https://github.com/COtlowski))
- UAT <- QA ACS027 [#6106](https://github.com/jaroop/agentcloud/pull/6106) ([@COtlowski](https://github.com/COtlowski) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@AndreiMazu](https://github.com/AndreiMazu) [@Cody-Alleman](https://github.com/Cody-Alleman) <EMAIL> [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6105](https://github.com/jaroop/agentcloud/pull/6105) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6103](https://github.com/jaroop/agentcloud/pull/6103) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6100](https://github.com/jaroop/agentcloud/pull/6100) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6098](https://github.com/jaroop/agentcloud/pull/6098) (<EMAIL> [@COtlowski](https://github.com/COtlowski))
- Develop -> QA [#6097](https://github.com/jaroop/agentcloud/pull/6097) ([@Cody-Alleman](https://github.com/Cody-Alleman))
- QA <- Develop [#6095](https://github.com/jaroop/agentcloud/pull/6095) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6094](https://github.com/jaroop/agentcloud/pull/6094) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6092](https://github.com/jaroop/agentcloud/pull/6092) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6088](https://github.com/jaroop/agentcloud/pull/6088) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6086](https://github.com/jaroop/agentcloud/pull/6086) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6085](https://github.com/jaroop/agentcloud/pull/6085) ([@COtlowski](https://github.com/COtlowski))
- UAT <- QA [#6079](https://github.com/jaroop/agentcloud/pull/6079) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6077](https://github.com/jaroop/agentcloud/pull/6077) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6075](https://github.com/jaroop/agentcloud/pull/6075) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- UAT <- QA [#6071](https://github.com/jaroop/agentcloud/pull/6071) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu) <EMAIL>)
- QA <- Develop [#6070](https://github.com/jaroop/agentcloud/pull/6070) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6068](https://github.com/jaroop/agentcloud/pull/6068) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6067](https://github.com/jaroop/agentcloud/pull/6067) (<EMAIL> [@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6060](https://github.com/jaroop/agentcloud/pull/6060) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6057](https://github.com/jaroop/agentcloud/pull/6057) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- UAT <- QA ([AC-1742](https://salessolutions.atlassian.net/browse/AC-1742): Only) [#6054](https://github.com/jaroop/agentcloud/pull/6054) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6052](https://github.com/jaroop/agentcloud/pull/6052) ([@COtlowski](https://github.com/COtlowski))
- UAT <- QA [#6051](https://github.com/jaroop/agentcloud/pull/6051) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance) [@valentyn-protsenko](https://github.com/valentyn-protsenko) <EMAIL> [@Cody-Alleman](https://github.com/Cody-Alleman))
- QA <- Develop [#6049](https://github.com/jaroop/agentcloud/pull/6049) ([@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6047](https://github.com/jaroop/agentcloud/pull/6047) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6045](https://github.com/jaroop/agentcloud/pull/6045) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6041](https://github.com/jaroop/agentcloud/pull/6041) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6039](https://github.com/jaroop/agentcloud/pull/6039) (<EMAIL> [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6038](https://github.com/jaroop/agentcloud/pull/6038) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#6036](https://github.com/jaroop/agentcloud/pull/6036) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6034](https://github.com/jaroop/agentcloud/pull/6034) ([@ChrisVance](https://github.com/ChrisVance) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6032](https://github.com/jaroop/agentcloud/pull/6032) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) <EMAIL> [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6031](https://github.com/jaroop/agentcloud/pull/6031) ([@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6027](https://github.com/jaroop/agentcloud/pull/6027) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- [AC-1747](https://salessolutions.atlassian.net/browse/AC-1747): UAT <- QA cherry pick [#6021](https://github.com/jaroop/agentcloud/pull/6021) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6020](https://github.com/jaroop/agentcloud/pull/6020) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#6014](https://github.com/jaroop/agentcloud/pull/6014) ([@AndreiMazu](https://github.com/AndreiMazu) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#6009](https://github.com/jaroop/agentcloud/pull/6009) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance))
- UAT <- QA [#6002](https://github.com/jaroop/agentcloud/pull/6002) ([@AndreiMazu](https://github.com/AndreiMazu) [@COtlowski](https://github.com/COtlowski) [@Cody-Alleman](https://github.com/Cody-Alleman) [@ChrisVance](https://github.com/ChrisVance) [@valentyn-protsenko](https://github.com/valentyn-protsenko))
- QA <- Develop [#6001](https://github.com/jaroop/agentcloud/pull/6001) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- Qa <- Develop [#5999](https://github.com/jaroop/agentcloud/pull/5999) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#5998](https://github.com/jaroop/agentcloud/pull/5998) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@Cody-Alleman](https://github.com/Cody-Alleman) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5996](https://github.com/jaroop/agentcloud/pull/5996) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#5994](https://github.com/jaroop/agentcloud/pull/5994) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#5992](https://github.com/jaroop/agentcloud/pull/5992) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5989](https://github.com/jaroop/agentcloud/pull/5989) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@ChrisVance](https://github.com/ChrisVance))
- UAT <- QA for [AC-1716](https://salessolutions.atlassian.net/browse/AC-1716) [#5981](https://github.com/jaroop/agentcloud/pull/5981) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#5980](https://github.com/jaroop/agentcloud/pull/5980) ([@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#5977](https://github.com/jaroop/agentcloud/pull/5977) ([@AndreiMazu](https://github.com/AndreiMazu))
- UAT <- QA [#5975](https://github.com/jaroop/agentcloud/pull/5975) ([@ChrisVance](https://github.com/ChrisVance) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski) [@Franksant13](https://github.com/Franksant13) <EMAIL> [@AndreiMazu](https://github.com/AndreiMazu) [@Cody-Alleman](https://github.com/Cody-Alleman))
- QA <- Develop [#5974](https://github.com/jaroop/agentcloud/pull/5974) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#5972](https://github.com/jaroop/agentcloud/pull/5972) ([@ChrisVance](https://github.com/ChrisVance) [@COtlowski](https://github.com/COtlowski))
- Develop to QA [#5971](https://github.com/jaroop/agentcloud/pull/5971) ([@Cody-Alleman](https://github.com/Cody-Alleman))
- QA <- Develop [#5969](https://github.com/jaroop/agentcloud/pull/5969) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5965](https://github.com/jaroop/agentcloud/pull/5965) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5963](https://github.com/jaroop/agentcloud/pull/5963) (<EMAIL> [@ChrisVance](https://github.com/ChrisVance))
- UAT <- QA for hotfix [AC-1653](https://salessolutions.atlassian.net/browse/AC-1653): Fix Retention Agent Licenses [#5959](https://github.com/jaroop/agentcloud/pull/5959) ([@Franksant13](https://github.com/Franksant13) [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#5957](https://github.com/jaroop/agentcloud/pull/5957) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5956](https://github.com/jaroop/agentcloud/pull/5956) ([@COtlowski](https://github.com/COtlowski) <EMAIL> [@AndreiMazu](https://github.com/AndreiMazu) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5952](https://github.com/jaroop/agentcloud/pull/5952) ([@COtlowski](https://github.com/COtlowski))
- [AC-1654](https://salessolutions.atlassian.net/browse/AC-1654): Increase memory limit for Opportunity Details parquet batch file [#5950](https://github.com/jaroop/agentcloud/pull/5950) ([@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5949](https://github.com/jaroop/agentcloud/pull/5949) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance) [@Franksant13](https://github.com/Franksant13))
- QA <- Develop [#5943](https://github.com/jaroop/agentcloud/pull/5943) ([@ChrisVance](https://github.com/ChrisVance))
- UAT <- QA [#5944](https://github.com/jaroop/agentcloud/pull/5944) (D1r2o3k4o5n6iA_ [@ChrisVance](https://github.com/ChrisVance) [@AndreiMazu](https://github.com/AndreiMazu) [@valentyn-protsenko](https://github.com/valentyn-protsenko) [@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#5941](https://github.com/jaroop/agentcloud/pull/5941) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5939](https://github.com/jaroop/agentcloud/pull/5939) ([@valentyn-protsenko](https://github.com/valentyn-protsenko) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5938](https://github.com/jaroop/agentcloud/pull/5938) ([@COtlowski](https://github.com/COtlowski))
- UAT <- QA [AC-1629](https://salessolutions.atlassian.net/browse/AC-1629) [#5935](https://github.com/jaroop/agentcloud/pull/5935) ([@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5933](https://github.com/jaroop/agentcloud/pull/5933) ([@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5930](https://github.com/jaroop/agentcloud/pull/5930) (D1r2o3k4o5n6iA_ [@ChrisVance](https://github.com/ChrisVance))
- UAT <- [AC-1598](https://salessolutions.atlassian.net/browse/AC-1598): Add Trigger to quotes.rate_class_aliases [#5925](https://github.com/jaroop/agentcloud/pull/5925) ([@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#5923](https://github.com/jaroop/agentcloud/pull/5923) (D1r2o3k4o5n6iA_ [@ChrisVance](https://github.com/ChrisVance) [@AndreiMazu](https://github.com/AndreiMazu))
- UAT <- QA [#5920](https://github.com/jaroop/agentcloud/pull/5920) ([@teemak](https://github.com/teemak) [@ChrisVance](https://github.com/ChrisVance) [@leaveller](https://github.com/leaveller) [@IanCZane](https://github.com/IanCZane) [@COtlowski](https://github.com/COtlowski) [@AndreiMazu](https://github.com/AndreiMazu) <EMAIL> [@Cody-Alleman](https://github.com/Cody-Alleman) D1r2o3k4o5n6iA_)
- QA <- Develop [#5919](https://github.com/jaroop/agentcloud/pull/5919) (D1r2o3k4o5n6iA_ [@AndreiMazu](https://github.com/AndreiMazu))
- QA <- Develop [#5916](https://github.com/jaroop/agentcloud/pull/5916) ([@Cody-Alleman](https://github.com/Cody-Alleman) [@AndreiMazu](https://github.com/AndreiMazu))
- Develop to QA [#5914](https://github.com/jaroop/agentcloud/pull/5914) ([@leaveller](https://github.com/leaveller) [@teemak](https://github.com/teemak) [@dependabot[bot]](https://github.com/dependabot[bot]) [@IanCZane](https://github.com/IanCZane) [@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance) [@Cody-Alleman](https://github.com/Cody-Alleman) D1r2o3k4o5n6iA_ [@AndreiMazu](https://github.com/AndreiMazu) <EMAIL>)
- QA <- Develop [#5906](https://github.com/jaroop/agentcloud/pull/5906) ([@COtlowski](https://github.com/COtlowski) [@ChrisVance](https://github.com/ChrisVance))
- QA <- Develop [#5904](https://github.com/jaroop/agentcloud/pull/5904) ([@COtlowski](https://github.com/COtlowski))
- QA <- Develop [#5899](https://github.com/jaroop/agentcloud/pull/5899) ([@ChrisVance](https://github.com/ChrisVance) [@COtlowski](https://github.com/COtlowski))

#### 🤖 CI

- Rename EARTHLY_GIT_BRANCH to EARTHLY_GIT_BRANCH_SAFE [#6676](https://github.com/jaroop/agentcloud/pull/6676) ([@AndreiMazu](https://github.com/AndreiMazu))
- Fix error when running old pipeline [#6634](https://github.com/jaroop/agentcloud/pull/6634) ([@nnovaeshc](https://github.com/nnovaeshc))
- ci: pipeline automation [#6600](https://github.com/jaroop/agentcloud/pull/6600) (<EMAIL> [@jaroopshared](https://github.com/jaroopshared))

#### 🏠 Internal

- Pipeline fixes [#6691](https://github.com/jaroop/agentcloud/pull/6691) ([@AndreiMazu](https://github.com/AndreiMazu))
- Dev/nnovaes/dual pipeline [#6620](https://github.com/jaroop/agentcloud/pull/6620) ([@nnovaeshc](https://github.com/nnovaeshc))
- add the new obr-microservice to the pipeline [#6597](https://github.com/jaroop/agentcloud/pull/6597) ([@nnovaeshc](https://github.com/nnovaeshc))

#### Authors: <AUTHORS>

- [@balanacqt](https://github.com/balanacqt)
- [@Cody-Alleman](https://github.com/Cody-Alleman)
- [@dependabot[bot]](https://github.com/dependabot[bot])
- [@Franksant13](https://github.com/Franksant13)
- [@jacklo225](https://github.com/jacklo225)
- [@jaroopshared](https://github.com/jaroopshared)
- [@leaveller](https://github.com/leaveller)
- [@MANDACQT](https://github.com/MANDACQT)
- [@nnovaeshc](https://github.com/nnovaeshc)
- [@PoisonTrainJJ](https://github.com/PoisonTrainJJ)
- [@SeevietaBiswas](https://github.com/SeevietaBiswas)
- [@sowmyavarakala](https://github.com/sowmyavarakala)
- [@SurajKomarla](https://github.com/SurajKomarla)
- [@valentyn-protsenko](https://github.com/valentyn-protsenko)
- [@YaroslavBobechko1994](https://github.com/YaroslavBobechko1994)
- amazurin (D1r2o3k4o5n6iA_)
- Andrei Mazurin ([@AndreiMazu](https://github.com/AndreiMazu))
- balanacqt (<EMAIL>)
- Chris Otlowski ([@COtlowski](https://github.com/COtlowski))
- Chris Vance ([@ChrisVance](https://github.com/ChrisVance))
- Ed at HealthCare ([@Ed-HealthcareDotcom](https://github.com/Ed-HealthcareDotcom))
- Ian Zane ([@IanCZane](https://github.com/IanCZane))
- Jack Lo (<EMAIL>)
- jaroopshared (<EMAIL>)
- Norman Novaes (<EMAIL>)
- Seevieta Biswas Toa (<EMAIL>)
- Sowmya Varakala (<EMAIL>)
- Sunil Kumar C S (<EMAIL>)
- Suraj Amaranarayana (<EMAIL>)
- Tee Mak ([@teemak](https://github.com/teemak))
- Umesh Adgaonkar ([@umeshaq](https://github.com/umeshaq))
- Yaroslav Bobechko (<EMAIL>)
